# CatFlow 智能体结构编辑器

## 功能特性

- **全屏可视化编辑**: 专注的编辑体验，充分利用PC浏览器屏幕空间
- **子智能体展示**: 子结构块清晰展示包含的智能体列表
- **嵌套子结构支持**: 子结构可以包含其他子结构，支持复杂层次
- **配置集成**: 主智能体支持配置参数（温度、最大输出等）
- **真实数据加载**: 从API加载真实的结构配置数据
- **完整工具栏**: 包含放大、缩小、还原、垃圾桶等Blockly标准工具
- **层次关系清晰**: 主agent → 子结构 → 智能体的三层架构

## 结构层次说明

CatFlow 系统采用三层结构设计，确保智能体的有序组织和协作：

### 1. 结构层次图

```
主结构 (Structure)
├── 主智能体 (Main Agent)
│   ├── 子结构引用 (Sub Structure Refs)
│   │   ├── weather_structure
│   │   └── summary_structure
│   └── 配置信息
└── 子结构定义 (Sub Structures)
    ├── weather_structure
    │   └── 子智能体 (Sub Agents)
    │       ├── weatherAgent
    │       └── locationAgent
    └── summary_structure
        └── 子智能体 (Sub Agents)
            ├── summaryAgent
            └── reportAgent
```

### 2. 关键概念

#### 主智能体 (Main Agent)
- **作用**: 协调和管理整个结构的执行流程
- **引用**: 使用 `sub_structure_refs` 引用子结构（不直接引用智能体）
- **职责**: 决定子结构的执行顺序和协作方式

#### 子结构 (Sub Structure)
- **作用**: 组织相关功能的智能体集合
- **引用**: 使用 `agent_refs` 引用具体的智能体
- **类型**: sequential（顺序）、concurrent（并发）、loop（循环）等

#### 智能体引用 (Agent Refs)
- **作用**: 指向具体的智能体实例
- **位置**: 只存在于子结构中
- **功能**: 执行具体的任务逻辑

### 3. Blockly 编辑器块说明

#### 结构块 (Structure Block)
- **颜色**: 蓝色 (#5C81A6)
- **功能**: 定义整个智能体结构的根节点
- **字段**: 名称、类型、描述

#### 主智能体块 (Main Agent Block)
- **颜色**: 绿色 (#160)
- **功能**: 定义主智能体配置
- **连接**: 连接子结构引用块
- **字段**: 名称、类型、模型、指令

#### 子结构块 (Sub Structure Block)
- **颜色**: 橙色 (#120)
- **功能**: 定义子结构配置
- **连接**: 连接智能体引用块
- **字段**: 名称、类型、描述

#### 子结构引用块 (Sub Structure Ref Block)
- **颜色**: 青色 (#95)
- **功能**: 在主智能体中引用子结构
- **字段**: 子结构名称

#### 智能体引用块 (Agent Ref Block)
- **颜色**: 深绿色 (#65)
- **功能**: 在子结构中引用智能体
- **字段**: 智能体名称

### 4. JSON 结构示例

```json
{
  "meta": {
    "description": "智能体结构配置文件",
    "purpose": "定义多智能体系统的组织结构和协作模式"
  },
  "structures": {
    "travel_planning_structure": {
      "name": "旅行规划结构",
      "type": "coordinator",
      "description": "用于旅行规划的多智能体协作结构",
      "main_agent": {
        "name": "main_agent",
        "type": "loop_agent",
        "model": "qwen-max",
        "instruction": "协调各个子结构完成旅行规划任务",
        "sub_structure_refs": [
          "weather_structure",
          "summary_structure"
        ]
      },
      "agent_refs": [
        "weatherAgent",
        "summaryAgent"
      ],
      "sub_structures": {
        "weather_structure": {
          "name": "weather_structure",
          "type": "sequential",
          "description": "处理天气查询相关任务",
          "agent_refs": [
            "weatherAgent"
          ]
        },
        "summary_structure": {
          "name": "summary_structure",
          "type": "sequential", 
          "description": "处理总结相关任务",
          "agent_refs": [
            "summaryAgent"
          ]
        }
      }
    }
  }
}
```

### 5. 编辑器使用流程

1. **创建结构**: 拖拽"结构块"到工作区
2. **添加主智能体**: 在结构块内添加"主智能体块"
3. **引用子结构**: 在主智能体中添加"子结构引用块"
4. **定义子结构**: 在结构块内添加"子结构块"
5. **引用智能体**: 在子结构中添加"智能体引用块"
6. **生成配置**: 点击"生成JSON"查看配置
7. **保存到API**: 点击"保存到API"持久化配置

### 6. 注意事项

- **层次清晰**: 主智能体不直接引用智能体，必须通过子结构
- **名称一致**: 子结构引用的名称必须与子结构定义的名称一致
- **类型匹配**: 确保智能体类型与其功能相匹配
- **Meta保护**: 生成JSON时会保持原有meta信息不变

这种设计确保了系统的可扩展性和可维护性，同时提供了清晰的组织结构。
