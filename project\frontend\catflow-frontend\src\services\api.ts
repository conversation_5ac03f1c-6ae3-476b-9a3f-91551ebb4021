import axios from 'axios'

/**
 * API服务配置
 */
const API_BASE_URL = 'http://127.0.0.1:8000'

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

/**
 * 智能体配置接口
 */
export interface AgentConfig {
  name: string
  model: string
  instruction: string
  tools: string[]
  agent_type: string
  generate_content_config?: {
    temperature?: number
    max_output_tokens?: number
  }
}

/**
 * 主智能体配置接口
 */
export interface MainAgent {
  name: string
  type: string
  model: string
  max_iterations?: number
  instruction: string
  description?: string
  agent_refs: string[]
}

/**
 * 子结构配置接口
 */
export interface SubStructure {
  name: string
  type: string
  max_iterations?: number
  description?: string
  agent_refs: string[]
}

/**
 * 结构配置接口
 */
export interface StructConfig {
  name: string
  description?: string
  type: string
  global_instruction?: string
  generate_content_config?: {
    temperature?: number
    max_output_tokens?: number
  }
  main_agent: MainAgent
  agent_refs: string[]
  sub_structures?: Record<string, SubStructure>
}

/**
 * API响应接口
 */
export interface ApiResponse<T> {
  data: T
  status: number
  message?: string
}

/**
 * 结构配置API服务类
 */
export class StructConfigService {
  /**
   * 获取所有结构配置
   */
  static async getAllStructConfigs(): Promise<any> {
    try {
      const response = await api.get('/struct-config')
      return response.data
    } catch (error) {
      console.error('获取结构配置失败:', error)
      throw error
    }
  }

  /**
   * 获取指定结构配置
   */
  static async getStructConfig(structName: string): Promise<StructConfig> {
    try {
      const response = await api.get(`/struct-config/${structName}`)
      return response.data
    } catch (error) {
      console.error(`获取结构配置 ${structName} 失败:`, error)
      throw error
    }
  }

  /**
   * 创建或更新结构配置
   */
  static async createOrUpdateStructConfig(structName: string, config: StructConfig): Promise<any> {
    try {
      const response = await api.post(`/struct-config/${structName}`, config)
      return response.data
    } catch (error) {
      console.error(`创建/更新结构配置 ${structName} 失败:`, error)
      throw error
    }
  }

  /**
   * 部分更新结构配置
   */
  static async updateStructConfig(structName: string, config: Partial<StructConfig>): Promise<any> {
    try {
      const response = await api.put(`/struct-config/${structName}`, config)
      return response.data
    } catch (error) {
      console.error(`更新结构配置 ${structName} 失败:`, error)
      throw error
    }
  }

  /**
   * 删除结构配置
   */
  static async deleteStructConfig(structName: string): Promise<any> {
    try {
      const response = await api.delete(`/struct-config/${structName}`)
      return response.data
    } catch (error) {
      console.error(`删除结构配置 ${structName} 失败:`, error)
      throw error
    }
  }
}

/**
 * 智能体配置API服务类
 */
export class AgentConfigService {
  /**
   * 获取所有智能体配置
   */
  static async getAllAgentConfigs(): Promise<any> {
    try {
      const response = await api.get('/agent-config')
      return response.data
    } catch (error) {
      console.error('获取智能体配置失败:', error)
      throw error
    }
  }

  /**
   * 获取指定智能体配置
   */
  static async getAgentConfig(agentName: string): Promise<AgentConfig> {
    try {
      const response = await api.get(`/agent-config/${agentName}`)
      return response.data
    } catch (error) {
      console.error(`获取智能体配置 ${agentName} 失败:`, error)
      throw error
    }
  }
}

/**
 * 健康检查API
 */
export class HealthService {
  /**
   * 检查API服务健康状态
   */
  static async checkHealth(): Promise<any> {
    try {
      const response = await api.get('/health')
      return response.data
    } catch (error) {
      console.error('健康检查失败:', error)
      throw error
    }
  }
}

export default api
