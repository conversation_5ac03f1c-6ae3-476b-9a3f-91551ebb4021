#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CatFlow配置管理API服务启动脚本

提供LLM配置和工具配置的RESTful API接口
包含Swagger文档自动生成功能
"""

import uvicorn
from config_api import app

def start_server():
    """启动FastAPI服务器"""
    print("正在启动CatFlow配置管理API服务...")
    print("API文档地址: http://localhost:8000/docs")
    print("ReDoc文档地址: http://localhost:8000/redoc")
    print("按 Ctrl+C 停止服务")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=True,  # 开发模式下自动重载
        log_level="info"
    )

if __name__ == "__main__":
    start_server()

    # 启动API服务
    # uvicorn config_api:app --reload --host 0.0.0.0 --port 8000