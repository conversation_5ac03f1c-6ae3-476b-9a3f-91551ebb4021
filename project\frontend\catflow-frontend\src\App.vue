<script setup lang="ts">
import { ref } from 'vue'
import BlocklyEditor from './components/BlocklyEditor.vue'
import StructureEditor from './components/StructureEditor.vue'
import TestComponent from './components/TestComponent.vue'

/**
 * 主应用组件
 * 提供编辑器切换功能
 */

const currentEditor = ref<'blockly' | 'structure' | 'test'>('test')

const switchEditor = (editor: 'blockly' | 'structure' | 'test') => {
  currentEditor.value = editor
}
</script>

<template>
  <div id="app">
    <div class="app-header">
      <h1>CatFlow 可视化编程平台</h1>
      <div class="editor-tabs">
        <button
          @click="switchEditor('test')"
          :class="{ active: currentEditor === 'test' }"
          class="tab-button"
        >
          功能测试
        </button>
        <button
          @click="switchEditor('structure')"
          :class="{ active: currentEditor === 'structure' }"
          class="tab-button"
        >
          结构体编辑器
        </button>
        <button
          @click="switchEditor('blockly')"
          :class="{ active: currentEditor === 'blockly' }"
          class="tab-button"
        >
          通用编辑器
        </button>
      </div>
    </div>

    <div class="editor-container">
      <TestComponent v-if="currentEditor === 'test'" />
      <StructureEditor v-else-if="currentEditor === 'structure'" />
      <BlocklyEditor v-else-if="currentEditor === 'blockly'" />
    </div>
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#app {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.app-header h1 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
}

.editor-tabs {
  display: flex;
  gap: 0.5rem;
}

.tab-button {
  padding: 0.6rem 1.2rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.tab-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.tab-button.active {
  background: white;
  color: #667eea;
  border-color: white;
  font-weight: 600;
}

.editor-container {
  flex: 1;
  overflow: hidden;
}

body {
  margin: 0;
  padding: 0;
  height: 100vh;
  width: 100vw;
}

html {
  height: 100%;
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .app-header h1 {
    font-size: 1.5rem;
  }

  .editor-tabs {
    justify-content: center;
    flex-wrap: wrap;
  }

  .tab-button {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
}
</style>
