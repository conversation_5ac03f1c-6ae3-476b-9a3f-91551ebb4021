{"version": 3, "sources": ["../../blockly/msg/en.js", "../../blockly/blocks/variables_dynamic.ts", "../../blockly/blocks/variables.ts", "../../blockly/blocks/text.ts", "../../blockly/blocks/procedures.ts", "../../blockly/blocks/math.ts", "../../blockly/blocks/loops.ts", "../../blockly/blocks/logic.ts", "../../blockly/blocks/lists.ts", "../../blockly/blocks/blocks.ts", "../../blockly/index.js", "../../blockly/index.mjs"], "sourcesContent": ["/* eslint-disable */\n;(function(root, factory) {\n  if (typeof define === 'function' && define.amd) { // AMD\n    define([], factory);\n  } else if (typeof exports === 'object') { // Node.js\n    module.exports = factory();\n  } else { // Browser\n    var messages = factory();\n    for (var key in messages) {\n      root.Blockly.Msg[key] = messages[key];\n    }\n  }\n}(this, function() {\n// This file was automatically generated.  Do not modify.\n\n'use strict';\n\nvar Blockly = Blockly || { Msg: Object.create(null) };\n\nBlockly.Msg[\"ABORT_MOVE\"] = \"Abort move\";\nBlockly.Msg[\"ADD_COMMENT\"] = \"Add Comment\";\nBlockly.Msg[\"ALT_KEY\"] = \"Alt\";\nBlockly.Msg[\"CANNOT_DELETE_VARIABLE_PROCEDURE\"] = \"Can't delete the variable '%1' because it's part of the definition of the function '%2'\";\nBlockly.Msg[\"CHANGE_VALUE_TITLE\"] = \"Change value:\";\nBlockly.Msg[\"CHROME_OS\"] = \"ChromeOS\";\nBlockly.Msg[\"CLEAN_UP\"] = \"Clean up Blocks\";\nBlockly.Msg[\"CLOSE\"] = \"Close\";\nBlockly.Msg[\"COLLAPSED_WARNINGS_WARNING\"] = \"Collapsed blocks contain warnings.\";\nBlockly.Msg[\"COLLAPSE_ALL\"] = \"Collapse Blocks\";\nBlockly.Msg[\"COLLAPSE_BLOCK\"] = \"Collapse Block\";\nBlockly.Msg[\"COLOUR_BLEND_COLOUR1\"] = \"colour 1\";\nBlockly.Msg[\"COLOUR_BLEND_COLOUR2\"] = \"colour 2\";\nBlockly.Msg[\"COLOUR_BLEND_HELPURL\"] = \"https://meyerweb.com/eric/tools/color-blend/#:::rgbp\";\nBlockly.Msg[\"COLOUR_BLEND_RATIO\"] = \"ratio\";\nBlockly.Msg[\"COLOUR_BLEND_TITLE\"] = \"blend\";\nBlockly.Msg[\"COLOUR_BLEND_TOOLTIP\"] = \"Blends two colours together with a given ratio (0.0 - 1.0).\";\nBlockly.Msg[\"COLOUR_PICKER_HELPURL\"] = \"https://en.wikipedia.org/wiki/Color\";\nBlockly.Msg[\"COLOUR_PICKER_TOOLTIP\"] = \"Choose a colour from the palette.\";\nBlockly.Msg[\"COLOUR_RANDOM_HELPURL\"] = \"http://randomcolour.com\";\nBlockly.Msg[\"COLOUR_RANDOM_TITLE\"] = \"random colour\";\nBlockly.Msg[\"COLOUR_RANDOM_TOOLTIP\"] = \"Choose a colour at random.\";\nBlockly.Msg[\"COLOUR_RGB_BLUE\"] = \"blue\";\nBlockly.Msg[\"COLOUR_RGB_GREEN\"] = \"green\";\nBlockly.Msg[\"COLOUR_RGB_HELPURL\"] = \"https://www.december.com/html/spec/colorpercompact.html\";\nBlockly.Msg[\"COLOUR_RGB_RED\"] = \"red\";\nBlockly.Msg[\"COLOUR_RGB_TITLE\"] = \"colour with\";\nBlockly.Msg[\"COLOUR_RGB_TOOLTIP\"] = \"Create a colour with the specified amount of red, green, and blue. All values must be between 0 and 100.\";\nBlockly.Msg[\"COMMAND_KEY\"] = \"⌘ Command\";\nBlockly.Msg[\"CONTROLS_FLOW_STATEMENTS_HELPURL\"] = \"https://github.com/google/blockly/wiki/Loops#loop-termination-blocks\";\nBlockly.Msg[\"CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK\"] = \"break out of loop\";\nBlockly.Msg[\"CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE\"] = \"continue with next iteration of loop\";\nBlockly.Msg[\"CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK\"] = \"Break out of the containing loop.\";\nBlockly.Msg[\"CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE\"] = \"Skip the rest of this loop, and continue with the next iteration.\";\nBlockly.Msg[\"CONTROLS_FLOW_STATEMENTS_WARNING\"] = \"Warning: This block may only be used within a loop.\";\nBlockly.Msg[\"CONTROLS_FOREACH_HELPURL\"] = \"https://github.com/google/blockly/wiki/Loops#for-each\";\nBlockly.Msg[\"CONTROLS_FOREACH_TITLE\"] = \"for each item %1 in list %2\";\nBlockly.Msg[\"CONTROLS_FOREACH_TOOLTIP\"] = \"For each item in a list, set the variable '%1' to the item, and then do some statements.\";\nBlockly.Msg[\"CONTROLS_FOR_HELPURL\"] = \"https://github.com/google/blockly/wiki/Loops#count-with\";\nBlockly.Msg[\"CONTROLS_FOR_TITLE\"] = \"count with %1 from %2 to %3 by %4\";\nBlockly.Msg[\"CONTROLS_FOR_TOOLTIP\"] = \"Have the variable '%1' take on the values from the start number to the end number, counting by the specified interval, and do the specified blocks.\";\nBlockly.Msg[\"CONTROLS_IF_ELSEIF_TOOLTIP\"] = \"Add a condition to the if block.\";\nBlockly.Msg[\"CONTROLS_IF_ELSE_TOOLTIP\"] = \"Add a final, catch-all condition to the if block.\";\nBlockly.Msg[\"CONTROLS_IF_HELPURL\"] = \"https://github.com/google/blockly/wiki/IfElse\";\nBlockly.Msg[\"CONTROLS_IF_IF_TOOLTIP\"] = \"Add, remove, or reorder sections to reconfigure this if block.\";\nBlockly.Msg[\"CONTROLS_IF_MSG_ELSE\"] = \"else\";\nBlockly.Msg[\"CONTROLS_IF_MSG_ELSEIF\"] = \"else if\";\nBlockly.Msg[\"CONTROLS_IF_MSG_IF\"] = \"if\";\nBlockly.Msg[\"CONTROLS_IF_TOOLTIP_1\"] = \"If a value is true, then do some statements.\";\nBlockly.Msg[\"CONTROLS_IF_TOOLTIP_2\"] = \"If a value is true, then do the first block of statements. Otherwise, do the second block of statements.\";\nBlockly.Msg[\"CONTROLS_IF_TOOLTIP_3\"] = \"If the first value is true, then do the first block of statements. Otherwise, if the second value is true, do the second block of statements.\";\nBlockly.Msg[\"CONTROLS_IF_TOOLTIP_4\"] = \"If the first value is true, then do the first block of statements. Otherwise, if the second value is true, do the second block of statements. If none of the values are true, do the last block of statements.\";\nBlockly.Msg[\"CONTROLS_REPEAT_HELPURL\"] = \"https://en.wikipedia.org/wiki/For_loop\";\nBlockly.Msg[\"CONTROLS_REPEAT_INPUT_DO\"] = \"do\";\nBlockly.Msg[\"CONTROLS_REPEAT_TITLE\"] = \"repeat %1 times\";\nBlockly.Msg[\"CONTROLS_REPEAT_TOOLTIP\"] = \"Do some statements several times.\";\nBlockly.Msg[\"CONTROLS_WHILEUNTIL_HELPURL\"] = \"https://github.com/google/blockly/wiki/Loops#repeat\";\nBlockly.Msg[\"CONTROLS_WHILEUNTIL_OPERATOR_UNTIL\"] = \"repeat until\";\nBlockly.Msg[\"CONTROLS_WHILEUNTIL_OPERATOR_WHILE\"] = \"repeat while\";\nBlockly.Msg[\"CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL\"] = \"While a value is false, then do some statements.\";\nBlockly.Msg[\"CONTROLS_WHILEUNTIL_TOOLTIP_WHILE\"] = \"While a value is true, then do some statements.\";\nBlockly.Msg[\"CONTROL_KEY\"] = \"Ctrl\";\nBlockly.Msg[\"COPY_SHORTCUT\"] = \"Copy (%1)\";\nBlockly.Msg[\"CUT_SHORTCUT\"] = \"Cut (%1)\";\nBlockly.Msg[\"DELETE_ALL_BLOCKS\"] = \"Delete all %1 blocks?\";\nBlockly.Msg[\"DELETE_BLOCK\"] = \"Delete Block\";\nBlockly.Msg[\"DELETE_KEY\"] = \"Del\";\nBlockly.Msg[\"DELETE_SHORTCUT\"] = \"Delete block (%1)\";\nBlockly.Msg[\"DELETE_VARIABLE\"] = \"Delete the '%1' variable\";\nBlockly.Msg[\"DELETE_VARIABLE_CONFIRMATION\"] = \"Delete %1 uses of the '%2' variable?\";\nBlockly.Msg[\"DELETE_X_BLOCKS\"] = \"Delete %1 Blocks\";\nBlockly.Msg[\"DIALOG_CANCEL\"] = \"Cancel\";\nBlockly.Msg[\"DIALOG_OK\"] = \"OK\";\nBlockly.Msg[\"DISABLE_BLOCK\"] = \"Disable Block\";\nBlockly.Msg[\"DUPLICATE_BLOCK\"] = \"Duplicate\";\nBlockly.Msg[\"DUPLICATE_COMMENT\"] = \"Duplicate Comment\";\nBlockly.Msg[\"EDIT_BLOCK_CONTENTS\"] = \"Edit Block contents (%1)\";\nBlockly.Msg[\"ENABLE_BLOCK\"] = \"Enable Block\";\nBlockly.Msg[\"EXPAND_ALL\"] = \"Expand Blocks\";\nBlockly.Msg[\"EXPAND_BLOCK\"] = \"Expand Block\";\nBlockly.Msg[\"EXTERNAL_INPUTS\"] = \"External Inputs\";\nBlockly.Msg[\"FINISH_MOVE\"] = \"Finish move\";\nBlockly.Msg[\"HELP\"] = \"Help\";\nBlockly.Msg[\"HELP_PROMPT\"] = \"Press %1 for help on keyboard controls\";\nBlockly.Msg[\"INLINE_INPUTS\"] = \"Inline Inputs\";\nBlockly.Msg[\"INSERT_BLOCK\"] = \"Insert Block (%1)\";\nBlockly.Msg[\"LINUX\"] = \"Linux\";\nBlockly.Msg[\"LISTS_CREATE_EMPTY_HELPURL\"] = \"https://github.com/google/blockly/wiki/Lists#create-empty-list\";\nBlockly.Msg[\"LISTS_CREATE_EMPTY_TITLE\"] = \"create empty list\";\nBlockly.Msg[\"LISTS_CREATE_EMPTY_TOOLTIP\"] = \"Returns a list, of length 0, containing no data records\";\nBlockly.Msg[\"LISTS_CREATE_WITH_CONTAINER_TITLE_ADD\"] = \"list\";\nBlockly.Msg[\"LISTS_CREATE_WITH_CONTAINER_TOOLTIP\"] = \"Add, remove, or reorder sections to reconfigure this list block.\";\nBlockly.Msg[\"LISTS_CREATE_WITH_HELPURL\"] = \"https://github.com/google/blockly/wiki/Lists#create-list-with\";\nBlockly.Msg[\"LISTS_CREATE_WITH_INPUT_WITH\"] = \"create list with\";\nBlockly.Msg[\"LISTS_CREATE_WITH_ITEM_TOOLTIP\"] = \"Add an item to the list.\";\nBlockly.Msg[\"LISTS_CREATE_WITH_TOOLTIP\"] = \"Create a list with any number of items.\";\nBlockly.Msg[\"LISTS_GET_INDEX_FIRST\"] = \"first\";\nBlockly.Msg[\"LISTS_GET_INDEX_FROM_END\"] = \"# from end\";\nBlockly.Msg[\"LISTS_GET_INDEX_FROM_START\"] = \"#\";\nBlockly.Msg[\"LISTS_GET_INDEX_GET\"] = \"get\";\nBlockly.Msg[\"LISTS_GET_INDEX_GET_REMOVE\"] = \"get and remove\";\nBlockly.Msg[\"LISTS_GET_INDEX_HELPURL\"] = \"https://github.com/google/blockly/wiki/Lists#getting-items-from-a-list\";\nBlockly.Msg[\"LISTS_GET_INDEX_LAST\"] = \"last\";\nBlockly.Msg[\"LISTS_GET_INDEX_RANDOM\"] = \"random\";\nBlockly.Msg[\"LISTS_GET_INDEX_REMOVE\"] = \"remove\";\nBlockly.Msg[\"LISTS_GET_INDEX_TAIL\"] = \"\";\nBlockly.Msg[\"LISTS_GET_INDEX_TOOLTIP_GET_FIRST\"] = \"Returns the first item in a list.\";\nBlockly.Msg[\"LISTS_GET_INDEX_TOOLTIP_GET_FROM\"] = \"Returns the item at the specified position in a list.\";\nBlockly.Msg[\"LISTS_GET_INDEX_TOOLTIP_GET_LAST\"] = \"Returns the last item in a list.\";\nBlockly.Msg[\"LISTS_GET_INDEX_TOOLTIP_GET_RANDOM\"] = \"Returns a random item in a list.\";\nBlockly.Msg[\"LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST\"] = \"Removes and returns the first item in a list.\";\nBlockly.Msg[\"LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM\"] = \"Removes and returns the item at the specified position in a list.\";\nBlockly.Msg[\"LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST\"] = \"Removes and returns the last item in a list.\";\nBlockly.Msg[\"LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM\"] = \"Removes and returns a random item in a list.\";\nBlockly.Msg[\"LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST\"] = \"Removes the first item in a list.\";\nBlockly.Msg[\"LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM\"] = \"Removes the item at the specified position in a list.\";\nBlockly.Msg[\"LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST\"] = \"Removes the last item in a list.\";\nBlockly.Msg[\"LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM\"] = \"Removes a random item in a list.\";\nBlockly.Msg[\"LISTS_GET_SUBLIST_END_FROM_END\"] = \"to # from end\";\nBlockly.Msg[\"LISTS_GET_SUBLIST_END_FROM_START\"] = \"to #\";\nBlockly.Msg[\"LISTS_GET_SUBLIST_END_LAST\"] = \"to last\";\nBlockly.Msg[\"LISTS_GET_SUBLIST_HELPURL\"] = \"https://github.com/google/blockly/wiki/Lists#getting-a-sublist\";\nBlockly.Msg[\"LISTS_GET_SUBLIST_START_FIRST\"] = \"get sub-list from first\";\nBlockly.Msg[\"LISTS_GET_SUBLIST_START_FROM_END\"] = \"get sub-list from # from end\";\nBlockly.Msg[\"LISTS_GET_SUBLIST_START_FROM_START\"] = \"get sub-list from #\";\nBlockly.Msg[\"LISTS_GET_SUBLIST_TAIL\"] = \"\";\nBlockly.Msg[\"LISTS_GET_SUBLIST_TOOLTIP\"] = \"Creates a copy of the specified portion of a list.\";\nBlockly.Msg[\"LISTS_INDEX_FROM_END_TOOLTIP\"] = \"%1 is the last item.\";\nBlockly.Msg[\"LISTS_INDEX_FROM_START_TOOLTIP\"] = \"%1 is the first item.\";\nBlockly.Msg[\"LISTS_INDEX_OF_FIRST\"] = \"find first occurrence of item\";\nBlockly.Msg[\"LISTS_INDEX_OF_HELPURL\"] = \"https://github.com/google/blockly/wiki/Lists#finding-items-in-a-list\";\nBlockly.Msg[\"LISTS_INDEX_OF_LAST\"] = \"find last occurrence of item\";\nBlockly.Msg[\"LISTS_INDEX_OF_TOOLTIP\"] = \"Returns the index of the first/last occurrence of the item in the list. Returns %1 if item is not found.\";\nBlockly.Msg[\"LISTS_INLIST\"] = \"in list\";\nBlockly.Msg[\"LISTS_ISEMPTY_HELPURL\"] = \"https://github.com/google/blockly/wiki/Lists#is-empty\";\nBlockly.Msg[\"LISTS_ISEMPTY_TITLE\"] = \"%1 is empty\";\nBlockly.Msg[\"LISTS_ISEMPTY_TOOLTIP\"] = \"Returns true if the list is empty.\";\nBlockly.Msg[\"LISTS_LENGTH_HELPURL\"] = \"https://github.com/google/blockly/wiki/Lists#length-of\";\nBlockly.Msg[\"LISTS_LENGTH_TITLE\"] = \"length of %1\";\nBlockly.Msg[\"LISTS_LENGTH_TOOLTIP\"] = \"Returns the length of a list.\";\nBlockly.Msg[\"LISTS_REPEAT_HELPURL\"] = \"https://github.com/google/blockly/wiki/Lists#create-list-with\";\nBlockly.Msg[\"LISTS_REPEAT_TITLE\"] = \"create list with item %1 repeated %2 times\";\nBlockly.Msg[\"LISTS_REPEAT_TOOLTIP\"] = \"Creates a list consisting of the given value repeated the specified number of times.\";\nBlockly.Msg[\"LISTS_REVERSE_HELPURL\"] = \"https://github.com/google/blockly/wiki/Lists#reversing-a-list\";\nBlockly.Msg[\"LISTS_REVERSE_MESSAGE0\"] = \"reverse %1\";\nBlockly.Msg[\"LISTS_REVERSE_TOOLTIP\"] = \"Reverse a copy of a list.\";\nBlockly.Msg[\"LISTS_SET_INDEX_HELPURL\"] = \"https://github.com/google/blockly/wiki/Lists#in-list--set\";\nBlockly.Msg[\"LISTS_SET_INDEX_INPUT_TO\"] = \"as\";\nBlockly.Msg[\"LISTS_SET_INDEX_INSERT\"] = \"insert at\";\nBlockly.Msg[\"LISTS_SET_INDEX_SET\"] = \"set\";\nBlockly.Msg[\"LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST\"] = \"Inserts the item at the start of a list.\";\nBlockly.Msg[\"LISTS_SET_INDEX_TOOLTIP_INSERT_FROM\"] = \"Inserts the item at the specified position in a list.\";\nBlockly.Msg[\"LISTS_SET_INDEX_TOOLTIP_INSERT_LAST\"] = \"Append the item to the end of a list.\";\nBlockly.Msg[\"LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM\"] = \"Inserts the item randomly in a list.\";\nBlockly.Msg[\"LISTS_SET_INDEX_TOOLTIP_SET_FIRST\"] = \"Sets the first item in a list.\";\nBlockly.Msg[\"LISTS_SET_INDEX_TOOLTIP_SET_FROM\"] = \"Sets the item at the specified position in a list.\";\nBlockly.Msg[\"LISTS_SET_INDEX_TOOLTIP_SET_LAST\"] = \"Sets the last item in a list.\";\nBlockly.Msg[\"LISTS_SET_INDEX_TOOLTIP_SET_RANDOM\"] = \"Sets a random item in a list.\";\nBlockly.Msg[\"LISTS_SORT_HELPURL\"] = \"https://github.com/google/blockly/wiki/Lists#sorting-a-list\";\nBlockly.Msg[\"LISTS_SORT_ORDER_ASCENDING\"] = \"ascending\";\nBlockly.Msg[\"LISTS_SORT_ORDER_DESCENDING\"] = \"descending\";\nBlockly.Msg[\"LISTS_SORT_TITLE\"] = \"sort %1 %2 %3\";\nBlockly.Msg[\"LISTS_SORT_TOOLTIP\"] = \"Sort a copy of a list.\";\nBlockly.Msg[\"LISTS_SORT_TYPE_IGNORECASE\"] = \"alphabetic, ignore case\";\nBlockly.Msg[\"LISTS_SORT_TYPE_NUMERIC\"] = \"numeric\";\nBlockly.Msg[\"LISTS_SORT_TYPE_TEXT\"] = \"alphabetic\";\nBlockly.Msg[\"LISTS_SPLIT_HELPURL\"] = \"https://github.com/google/blockly/wiki/Lists#splitting-strings-and-joining-lists\";\nBlockly.Msg[\"LISTS_SPLIT_LIST_FROM_TEXT\"] = \"make list from text\";\nBlockly.Msg[\"LISTS_SPLIT_TEXT_FROM_LIST\"] = \"make text from list\";\nBlockly.Msg[\"LISTS_SPLIT_TOOLTIP_JOIN\"] = \"Join a list of texts into one text, separated by a delimiter.\";\nBlockly.Msg[\"LISTS_SPLIT_TOOLTIP_SPLIT\"] = \"Split text into a list of texts, breaking at each delimiter.\";\nBlockly.Msg[\"LISTS_SPLIT_WITH_DELIMITER\"] = \"with delimiter\";\nBlockly.Msg[\"LOGIC_BOOLEAN_FALSE\"] = \"false\";\nBlockly.Msg[\"LOGIC_BOOLEAN_HELPURL\"] = \"https://github.com/google/blockly/wiki/Logic#values\";\nBlockly.Msg[\"LOGIC_BOOLEAN_TOOLTIP\"] = \"Returns either true or false.\";\nBlockly.Msg[\"LOGIC_BOOLEAN_TRUE\"] = \"true\";\nBlockly.Msg[\"LOGIC_COMPARE_HELPURL\"] = \"https://en.wikipedia.org/wiki/Inequality_(mathematics)\";\nBlockly.Msg[\"LOGIC_COMPARE_TOOLTIP_EQ\"] = \"Return true if both inputs equal each other.\";\nBlockly.Msg[\"LOGIC_COMPARE_TOOLTIP_GT\"] = \"Return true if the first input is greater than the second input.\";\nBlockly.Msg[\"LOGIC_COMPARE_TOOLTIP_GTE\"] = \"Return true if the first input is greater than or equal to the second input.\";\nBlockly.Msg[\"LOGIC_COMPARE_TOOLTIP_LT\"] = \"Return true if the first input is smaller than the second input.\";\nBlockly.Msg[\"LOGIC_COMPARE_TOOLTIP_LTE\"] = \"Return true if the first input is smaller than or equal to the second input.\";\nBlockly.Msg[\"LOGIC_COMPARE_TOOLTIP_NEQ\"] = \"Return true if both inputs are not equal to each other.\";\nBlockly.Msg[\"LOGIC_NEGATE_HELPURL\"] = \"https://github.com/google/blockly/wiki/Logic#not\";\nBlockly.Msg[\"LOGIC_NEGATE_TITLE\"] = \"not %1\";\nBlockly.Msg[\"LOGIC_NEGATE_TOOLTIP\"] = \"Returns true if the input is false. Returns false if the input is true.\";\nBlockly.Msg[\"LOGIC_NULL\"] = \"null\";\nBlockly.Msg[\"LOGIC_NULL_HELPURL\"] = \"https://en.wikipedia.org/wiki/Nullable_type\";\nBlockly.Msg[\"LOGIC_NULL_TOOLTIP\"] = \"Returns null.\";\nBlockly.Msg[\"LOGIC_OPERATION_AND\"] = \"and\";\nBlockly.Msg[\"LOGIC_OPERATION_HELPURL\"] = \"https://github.com/google/blockly/wiki/Logic#logical-operations\";\nBlockly.Msg[\"LOGIC_OPERATION_OR\"] = \"or\";\nBlockly.Msg[\"LOGIC_OPERATION_TOOLTIP_AND\"] = \"Return true if both inputs are true.\";\nBlockly.Msg[\"LOGIC_OPERATION_TOOLTIP_OR\"] = \"Return true if at least one of the inputs is true.\";\nBlockly.Msg[\"LOGIC_TERNARY_CONDITION\"] = \"test\";\nBlockly.Msg[\"LOGIC_TERNARY_HELPURL\"] = \"https://en.wikipedia.org/wiki/%3F:\";\nBlockly.Msg[\"LOGIC_TERNARY_IF_FALSE\"] = \"if false\";\nBlockly.Msg[\"LOGIC_TERNARY_IF_TRUE\"] = \"if true\";\nBlockly.Msg[\"LOGIC_TERNARY_TOOLTIP\"] = \"Check the condition in 'test'. If the condition is true, returns the 'if true' value; otherwise returns the 'if false' value.\";\nBlockly.Msg[\"MAC_OS\"] = \"macOS\";\nBlockly.Msg[\"MATH_ADDITION_SYMBOL\"] = \"+\";\nBlockly.Msg[\"MATH_ARITHMETIC_HELPURL\"] = \"https://en.wikipedia.org/wiki/Arithmetic\";\nBlockly.Msg[\"MATH_ARITHMETIC_TOOLTIP_ADD\"] = \"Return the sum of the two numbers.\";\nBlockly.Msg[\"MATH_ARITHMETIC_TOOLTIP_DIVIDE\"] = \"Return the quotient of the two numbers.\";\nBlockly.Msg[\"MATH_ARITHMETIC_TOOLTIP_MINUS\"] = \"Return the difference of the two numbers.\";\nBlockly.Msg[\"MATH_ARITHMETIC_TOOLTIP_MULTIPLY\"] = \"Return the product of the two numbers.\";\nBlockly.Msg[\"MATH_ARITHMETIC_TOOLTIP_POWER\"] = \"Return the first number raised to the power of the second number.\";\nBlockly.Msg[\"MATH_ATAN2_HELPURL\"] = \"https://en.wikipedia.org/wiki/Atan2\";\nBlockly.Msg[\"MATH_ATAN2_TITLE\"] = \"atan2 of X:%1 Y:%2\";\nBlockly.Msg[\"MATH_ATAN2_TOOLTIP\"] = \"Return the arctangent of point (X, Y) in degrees from -180 to 180.\";\nBlockly.Msg[\"MATH_CHANGE_HELPURL\"] = \"https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter\";\nBlockly.Msg[\"MATH_CHANGE_TITLE\"] = \"change %1 by %2\";\nBlockly.Msg[\"MATH_CHANGE_TOOLTIP\"] = \"Add a number to variable '%1'.\";\nBlockly.Msg[\"MATH_CONSTANT_HELPURL\"] = \"https://en.wikipedia.org/wiki/Mathematical_constant\";\nBlockly.Msg[\"MATH_CONSTANT_TOOLTIP\"] = \"Return one of the common constants: π (3.141…), e (2.718…), φ (1.618…), sqrt(2) (1.414…), sqrt(½) (0.707…), or ∞ (infinity).\";\nBlockly.Msg[\"MATH_CONSTRAIN_HELPURL\"] = \"https://en.wikipedia.org/wiki/Clamping_(graphics)\";\nBlockly.Msg[\"MATH_CONSTRAIN_TITLE\"] = \"constrain %1 low %2 high %3\";\nBlockly.Msg[\"MATH_CONSTRAIN_TOOLTIP\"] = \"Constrain a number to be between the specified limits (inclusive).\";\nBlockly.Msg[\"MATH_DIVISION_SYMBOL\"] = \"÷\";\nBlockly.Msg[\"MATH_IS_DIVISIBLE_BY\"] = \"is divisible by\";\nBlockly.Msg[\"MATH_IS_EVEN\"] = \"is even\";\nBlockly.Msg[\"MATH_IS_NEGATIVE\"] = \"is negative\";\nBlockly.Msg[\"MATH_IS_ODD\"] = \"is odd\";\nBlockly.Msg[\"MATH_IS_POSITIVE\"] = \"is positive\";\nBlockly.Msg[\"MATH_IS_PRIME\"] = \"is prime\";\nBlockly.Msg[\"MATH_IS_TOOLTIP\"] = \"Check if a number is an even, odd, prime, whole, positive, negative, or if it is divisible by certain number. Returns true or false.\";\nBlockly.Msg[\"MATH_IS_WHOLE\"] = \"is whole\";\nBlockly.Msg[\"MATH_MODULO_HELPURL\"] = \"https://en.wikipedia.org/wiki/Modulo_operation\";\nBlockly.Msg[\"MATH_MODULO_TITLE\"] = \"remainder of %1 ÷ %2\";\nBlockly.Msg[\"MATH_MODULO_TOOLTIP\"] = \"Return the remainder from dividing the two numbers.\";\nBlockly.Msg[\"MATH_MULTIPLICATION_SYMBOL\"] = \"×\";\nBlockly.Msg[\"MATH_NUMBER_HELPURL\"] = \"https://en.wikipedia.org/wiki/Number\";\nBlockly.Msg[\"MATH_NUMBER_TOOLTIP\"] = \"A number.\";\nBlockly.Msg[\"MATH_ONLIST_HELPURL\"] = \"\";\nBlockly.Msg[\"MATH_ONLIST_OPERATOR_AVERAGE\"] = \"average of list\";\nBlockly.Msg[\"MATH_ONLIST_OPERATOR_MAX\"] = \"max of list\";\nBlockly.Msg[\"MATH_ONLIST_OPERATOR_MEDIAN\"] = \"median of list\";\nBlockly.Msg[\"MATH_ONLIST_OPERATOR_MIN\"] = \"min of list\";\nBlockly.Msg[\"MATH_ONLIST_OPERATOR_MODE\"] = \"modes of list\";\nBlockly.Msg[\"MATH_ONLIST_OPERATOR_RANDOM\"] = \"random item of list\";\nBlockly.Msg[\"MATH_ONLIST_OPERATOR_STD_DEV\"] = \"standard deviation of list\";\nBlockly.Msg[\"MATH_ONLIST_OPERATOR_SUM\"] = \"sum of list\";\nBlockly.Msg[\"MATH_ONLIST_TOOLTIP_AVERAGE\"] = \"Return the average (arithmetic mean) of the numeric values in the list.\";\nBlockly.Msg[\"MATH_ONLIST_TOOLTIP_MAX\"] = \"Return the largest number in the list.\";\nBlockly.Msg[\"MATH_ONLIST_TOOLTIP_MEDIAN\"] = \"Return the median number in the list.\";\nBlockly.Msg[\"MATH_ONLIST_TOOLTIP_MIN\"] = \"Return the smallest number in the list.\";\nBlockly.Msg[\"MATH_ONLIST_TOOLTIP_MODE\"] = \"Return a list of the most common item(s) in the list.\";\nBlockly.Msg[\"MATH_ONLIST_TOOLTIP_RANDOM\"] = \"Return a random element from the list.\";\nBlockly.Msg[\"MATH_ONLIST_TOOLTIP_STD_DEV\"] = \"Return the standard deviation of the list.\";\nBlockly.Msg[\"MATH_ONLIST_TOOLTIP_SUM\"] = \"Return the sum of all the numbers in the list.\";\nBlockly.Msg[\"MATH_POWER_SYMBOL\"] = \"^\";\nBlockly.Msg[\"MATH_RANDOM_FLOAT_HELPURL\"] = \"https://en.wikipedia.org/wiki/Random_number_generation\";\nBlockly.Msg[\"MATH_RANDOM_FLOAT_TITLE_RANDOM\"] = \"random fraction\";\nBlockly.Msg[\"MATH_RANDOM_FLOAT_TOOLTIP\"] = \"Return a random fraction between 0.0 (inclusive) and 1.0 (exclusive).\";\nBlockly.Msg[\"MATH_RANDOM_INT_HELPURL\"] = \"https://en.wikipedia.org/wiki/Random_number_generation\";\nBlockly.Msg[\"MATH_RANDOM_INT_TITLE\"] = \"random integer from %1 to %2\";\nBlockly.Msg[\"MATH_RANDOM_INT_TOOLTIP\"] = \"Return a random integer between the two specified limits, inclusive.\";\nBlockly.Msg[\"MATH_ROUND_HELPURL\"] = \"https://en.wikipedia.org/wiki/Rounding\";\nBlockly.Msg[\"MATH_ROUND_OPERATOR_ROUND\"] = \"round\";\nBlockly.Msg[\"MATH_ROUND_OPERATOR_ROUNDDOWN\"] = \"round down\";\nBlockly.Msg[\"MATH_ROUND_OPERATOR_ROUNDUP\"] = \"round up\";\nBlockly.Msg[\"MATH_ROUND_TOOLTIP\"] = \"Round a number up or down.\";\nBlockly.Msg[\"MATH_SINGLE_HELPURL\"] = \"https://en.wikipedia.org/wiki/Square_root\";\nBlockly.Msg[\"MATH_SINGLE_OP_ABSOLUTE\"] = \"absolute\";\nBlockly.Msg[\"MATH_SINGLE_OP_ROOT\"] = \"square root\";\nBlockly.Msg[\"MATH_SINGLE_TOOLTIP_ABS\"] = \"Return the absolute value of a number.\";\nBlockly.Msg[\"MATH_SINGLE_TOOLTIP_EXP\"] = \"Return e to the power of a number.\";\nBlockly.Msg[\"MATH_SINGLE_TOOLTIP_LN\"] = \"Return the natural logarithm of a number.\";\nBlockly.Msg[\"MATH_SINGLE_TOOLTIP_LOG10\"] = \"Return the base 10 logarithm of a number.\";\nBlockly.Msg[\"MATH_SINGLE_TOOLTIP_NEG\"] = \"Return the negation of a number.\";\nBlockly.Msg[\"MATH_SINGLE_TOOLTIP_POW10\"] = \"Return 10 to the power of a number.\";\nBlockly.Msg[\"MATH_SINGLE_TOOLTIP_ROOT\"] = \"Return the square root of a number.\";\nBlockly.Msg[\"MATH_SUBTRACTION_SYMBOL\"] = \"-\";\nBlockly.Msg[\"MATH_TRIG_ACOS\"] = \"acos\";\nBlockly.Msg[\"MATH_TRIG_ASIN\"] = \"asin\";\nBlockly.Msg[\"MATH_TRIG_ATAN\"] = \"atan\";\nBlockly.Msg[\"MATH_TRIG_COS\"] = \"cos\";\nBlockly.Msg[\"MATH_TRIG_HELPURL\"] = \"https://en.wikipedia.org/wiki/Trigonometric_functions\";\nBlockly.Msg[\"MATH_TRIG_SIN\"] = \"sin\";\nBlockly.Msg[\"MATH_TRIG_TAN\"] = \"tan\";\nBlockly.Msg[\"MATH_TRIG_TOOLTIP_ACOS\"] = \"Return the arccosine of a number.\";\nBlockly.Msg[\"MATH_TRIG_TOOLTIP_ASIN\"] = \"Return the arcsine of a number.\";\nBlockly.Msg[\"MATH_TRIG_TOOLTIP_ATAN\"] = \"Return the arctangent of a number.\";\nBlockly.Msg[\"MATH_TRIG_TOOLTIP_COS\"] = \"Return the cosine of a degree (not radian).\";\nBlockly.Msg[\"MATH_TRIG_TOOLTIP_SIN\"] = \"Return the sine of a degree (not radian).\";\nBlockly.Msg[\"MATH_TRIG_TOOLTIP_TAN\"] = \"Return the tangent of a degree (not radian).\";\nBlockly.Msg[\"MOVE_BLOCK\"] = \"Move Block (%1)\";\nBlockly.Msg[\"MOVE_DOWN_CONSTRAINED\"] = \"Move down constrained\";\nBlockly.Msg[\"MOVE_DOWN_UNCONSTRAINED\"] = \"Move down, unconstrained\";\nBlockly.Msg[\"MOVE_LEFT_CONSTRAINED\"] = \"Move left, constrained\";\nBlockly.Msg[\"MOVE_LEFT_UNCONSTRAINED\"] = \"Move left, unconstrained\";\nBlockly.Msg[\"MOVE_RIGHT_CONSTRAINED\"] = \"Move right constrained\";\nBlockly.Msg[\"MOVE_RIGHT_UNCONSTRAINED\"] = \"Move right, unconstrained\";\nBlockly.Msg[\"MOVE_UP_CONSTRAINED\"] = \"Move up, constrained\";\nBlockly.Msg[\"MOVE_UP_UNCONSTRAINED\"] = \"Move up unconstrained\";\nBlockly.Msg[\"NEW_COLOUR_VARIABLE\"] = \"Create colour variable...\";\nBlockly.Msg[\"NEW_NUMBER_VARIABLE\"] = \"Create number variable...\";\nBlockly.Msg[\"NEW_STRING_VARIABLE\"] = \"Create string variable...\";\nBlockly.Msg[\"NEW_VARIABLE\"] = \"Create variable...\";\nBlockly.Msg[\"NEW_VARIABLE_TITLE\"] = \"New variable name:\";\nBlockly.Msg[\"NEW_VARIABLE_TYPE_TITLE\"] = \"New variable type:\";\nBlockly.Msg[\"OPTION_KEY\"] = \"⌥ Option\";\nBlockly.Msg[\"ORDINAL_NUMBER_SUFFIX\"] = \"\";\nBlockly.Msg[\"PASTE_SHORTCUT\"] = \"Paste (%1)\";\nBlockly.Msg[\"PROCEDURES_ALLOW_STATEMENTS\"] = \"allow statements\";\nBlockly.Msg[\"PROCEDURES_BEFORE_PARAMS\"] = \"with:\";\nBlockly.Msg[\"PROCEDURES_CALLNORETURN_HELPURL\"] = \"https://en.wikipedia.org/wiki/Subroutine\";\nBlockly.Msg[\"PROCEDURES_CALLNORETURN_TOOLTIP\"] = \"Run the user-defined function '%1'.\";\nBlockly.Msg[\"PROCEDURES_CALLRETURN_HELPURL\"] = \"https://en.wikipedia.org/wiki/Subroutine\";\nBlockly.Msg[\"PROCEDURES_CALLRETURN_TOOLTIP\"] = \"Run the user-defined function '%1' and use its output.\";\nBlockly.Msg[\"PROCEDURES_CALL_BEFORE_PARAMS\"] = \"with:\";\nBlockly.Msg[\"PROCEDURES_CALL_DISABLED_DEF_WARNING\"] = \"Can't run the user-defined function '%1' because the definition block is disabled.\";\nBlockly.Msg[\"PROCEDURES_CREATE_DO\"] = \"Create '%1'\";\nBlockly.Msg[\"PROCEDURES_DEFNORETURN_COMMENT\"] = \"Describe this function...\";\nBlockly.Msg[\"PROCEDURES_DEFNORETURN_DO\"] = \"\";\nBlockly.Msg[\"PROCEDURES_DEFNORETURN_HELPURL\"] = \"https://en.wikipedia.org/wiki/Subroutine\";\nBlockly.Msg[\"PROCEDURES_DEFNORETURN_PROCEDURE\"] = \"do something\";\nBlockly.Msg[\"PROCEDURES_DEFNORETURN_TITLE\"] = \"to\";\nBlockly.Msg[\"PROCEDURES_DEFNORETURN_TOOLTIP\"] = \"Creates a function with no output.\";\nBlockly.Msg[\"PROCEDURES_DEFRETURN_HELPURL\"] = \"https://en.wikipedia.org/wiki/Subroutine\";\nBlockly.Msg[\"PROCEDURES_DEFRETURN_RETURN\"] = \"return\";\nBlockly.Msg[\"PROCEDURES_DEFRETURN_TOOLTIP\"] = \"Creates a function with an output.\";\nBlockly.Msg[\"PROCEDURES_DEF_DUPLICATE_WARNING\"] = \"Warning: This function has duplicate parameters.\";\nBlockly.Msg[\"PROCEDURES_HIGHLIGHT_DEF\"] = \"Highlight function definition\";\nBlockly.Msg[\"PROCEDURES_IFRETURN_HELPURL\"] = \"https://c2.com/cgi/wiki?GuardClause\";\nBlockly.Msg[\"PROCEDURES_IFRETURN_TOOLTIP\"] = \"If a value is true, then return a second value.\";\nBlockly.Msg[\"PROCEDURES_IFRETURN_WARNING\"] = \"Warning: This block may be used only within a function definition.\";\nBlockly.Msg[\"PROCEDURES_MUTATORARG_TITLE\"] = \"input name:\";\nBlockly.Msg[\"PROCEDURES_MUTATORARG_TOOLTIP\"] = \"Add an input to the function.\";\nBlockly.Msg[\"PROCEDURES_MUTATORCONTAINER_TITLE\"] = \"inputs\";\nBlockly.Msg[\"PROCEDURES_MUTATORCONTAINER_TOOLTIP\"] = \"Add, remove, or reorder inputs to this function.\";\nBlockly.Msg[\"REDO\"] = \"Redo\";\nBlockly.Msg[\"REMOVE_COMMENT\"] = \"Remove Comment\";\nBlockly.Msg[\"RENAME_VARIABLE\"] = \"Rename variable...\";\nBlockly.Msg[\"RENAME_VARIABLE_TITLE\"] = \"Rename all '%1' variables to:\";\nBlockly.Msg[\"SHORTCUTS_CODE_NAVIGATION\"] = \"Code navigation\";\nBlockly.Msg[\"SHORTCUTS_EDITING\"] = \"Editing\";\nBlockly.Msg[\"SHORTCUTS_GENERAL\"] = \"General\";\nBlockly.Msg[\"START_MOVE\"] = \"Start move\";\nBlockly.Msg[\"TEXT_APPEND_HELPURL\"] = \"https://github.com/google/blockly/wiki/Text#text-modification\";\nBlockly.Msg[\"TEXT_APPEND_TITLE\"] = \"to %1 append text %2\";\nBlockly.Msg[\"TEXT_APPEND_TOOLTIP\"] = \"Append some text to variable '%1'.\";\nBlockly.Msg[\"TEXT_CHANGECASE_HELPURL\"] = \"https://github.com/google/blockly/wiki/Text#adjusting-text-case\";\nBlockly.Msg[\"TEXT_CHANGECASE_OPERATOR_LOWERCASE\"] = \"to lower case\";\nBlockly.Msg[\"TEXT_CHANGECASE_OPERATOR_TITLECASE\"] = \"to Title Case\";\nBlockly.Msg[\"TEXT_CHANGECASE_OPERATOR_UPPERCASE\"] = \"to UPPER CASE\";\nBlockly.Msg[\"TEXT_CHANGECASE_TOOLTIP\"] = \"Return a copy of the text in a different case.\";\nBlockly.Msg[\"TEXT_CHARAT_FIRST\"] = \"get first letter\";\nBlockly.Msg[\"TEXT_CHARAT_FROM_END\"] = \"get letter # from end\";\nBlockly.Msg[\"TEXT_CHARAT_FROM_START\"] = \"get letter #\";\nBlockly.Msg[\"TEXT_CHARAT_HELPURL\"] = \"https://github.com/google/blockly/wiki/Text#extracting-text\";\nBlockly.Msg[\"TEXT_CHARAT_LAST\"] = \"get last letter\";\nBlockly.Msg[\"TEXT_CHARAT_RANDOM\"] = \"get random letter\";\nBlockly.Msg[\"TEXT_CHARAT_TAIL\"] = \"\";\nBlockly.Msg[\"TEXT_CHARAT_TITLE\"] = \"in text %1 %2\";\nBlockly.Msg[\"TEXT_CHARAT_TOOLTIP\"] = \"Returns the letter at the specified position.\";\nBlockly.Msg[\"TEXT_COUNT_HELPURL\"] = \"https://github.com/google/blockly/wiki/Text#counting-substrings\";\nBlockly.Msg[\"TEXT_COUNT_MESSAGE0\"] = \"count %1 in %2\";\nBlockly.Msg[\"TEXT_COUNT_TOOLTIP\"] = \"Count how many times some text occurs within some other text.\";\nBlockly.Msg[\"TEXT_CREATE_JOIN_ITEM_TOOLTIP\"] = \"Add an item to the text.\";\nBlockly.Msg[\"TEXT_CREATE_JOIN_TITLE_JOIN\"] = \"join\";\nBlockly.Msg[\"TEXT_CREATE_JOIN_TOOLTIP\"] = \"Add, remove, or reorder sections to reconfigure this text block.\";\nBlockly.Msg[\"TEXT_GET_SUBSTRING_END_FROM_END\"] = \"to letter # from end\";\nBlockly.Msg[\"TEXT_GET_SUBSTRING_END_FROM_START\"] = \"to letter #\";\nBlockly.Msg[\"TEXT_GET_SUBSTRING_END_LAST\"] = \"to last letter\";\nBlockly.Msg[\"TEXT_GET_SUBSTRING_HELPURL\"] = \"https://github.com/google/blockly/wiki/Text#extracting-a-region-of-text\";\nBlockly.Msg[\"TEXT_GET_SUBSTRING_INPUT_IN_TEXT\"] = \"in text\";\nBlockly.Msg[\"TEXT_GET_SUBSTRING_START_FIRST\"] = \"get substring from first letter\";\nBlockly.Msg[\"TEXT_GET_SUBSTRING_START_FROM_END\"] = \"get substring from letter # from end\";\nBlockly.Msg[\"TEXT_GET_SUBSTRING_START_FROM_START\"] = \"get substring from letter #\";\nBlockly.Msg[\"TEXT_GET_SUBSTRING_TAIL\"] = \"\";\nBlockly.Msg[\"TEXT_GET_SUBSTRING_TOOLTIP\"] = \"Returns a specified portion of the text.\";\nBlockly.Msg[\"TEXT_INDEXOF_HELPURL\"] = \"https://github.com/google/blockly/wiki/Text#finding-text\";\nBlockly.Msg[\"TEXT_INDEXOF_OPERATOR_FIRST\"] = \"find first occurrence of text\";\nBlockly.Msg[\"TEXT_INDEXOF_OPERATOR_LAST\"] = \"find last occurrence of text\";\nBlockly.Msg[\"TEXT_INDEXOF_TITLE\"] = \"in text %1 %2 %3\";\nBlockly.Msg[\"TEXT_INDEXOF_TOOLTIP\"] = \"Returns the index of the first/last occurrence of the first text in the second text. Returns %1 if text is not found.\";\nBlockly.Msg[\"TEXT_ISEMPTY_HELPURL\"] = \"https://github.com/google/blockly/wiki/Text#checking-for-empty-text\";\nBlockly.Msg[\"TEXT_ISEMPTY_TITLE\"] = \"%1 is empty\";\nBlockly.Msg[\"TEXT_ISEMPTY_TOOLTIP\"] = \"Returns true if the provided text is empty.\";\nBlockly.Msg[\"TEXT_JOIN_HELPURL\"] = \"https://github.com/google/blockly/wiki/Text#text-creation\";\nBlockly.Msg[\"TEXT_JOIN_TITLE_CREATEWITH\"] = \"create text with\";\nBlockly.Msg[\"TEXT_JOIN_TOOLTIP\"] = \"Create a piece of text by joining together any number of items.\";\nBlockly.Msg[\"TEXT_LENGTH_HELPURL\"] = \"https://github.com/google/blockly/wiki/Text#text-modification\";\nBlockly.Msg[\"TEXT_LENGTH_TITLE\"] = \"length of %1\";\nBlockly.Msg[\"TEXT_LENGTH_TOOLTIP\"] = \"Returns the number of letters (including spaces) in the provided text.\";\nBlockly.Msg[\"TEXT_PRINT_HELPURL\"] = \"https://github.com/google/blockly/wiki/Text#printing-text\";\nBlockly.Msg[\"TEXT_PRINT_TITLE\"] = \"print %1\";\nBlockly.Msg[\"TEXT_PRINT_TOOLTIP\"] = \"Print the specified text, number or other value.\";\nBlockly.Msg[\"TEXT_PROMPT_HELPURL\"] = \"https://github.com/google/blockly/wiki/Text#getting-input-from-the-user\";\nBlockly.Msg[\"TEXT_PROMPT_TOOLTIP_NUMBER\"] = \"Prompt for user for a number.\";\nBlockly.Msg[\"TEXT_PROMPT_TOOLTIP_TEXT\"] = \"Prompt for user for some text.\";\nBlockly.Msg[\"TEXT_PROMPT_TYPE_NUMBER\"] = \"prompt for number with message\";\nBlockly.Msg[\"TEXT_PROMPT_TYPE_TEXT\"] = \"prompt for text with message\";\nBlockly.Msg[\"TEXT_REPLACE_HELPURL\"] = \"https://github.com/google/blockly/wiki/Text#replacing-substrings\";\nBlockly.Msg[\"TEXT_REPLACE_MESSAGE0\"] = \"replace %1 with %2 in %3\";\nBlockly.Msg[\"TEXT_REPLACE_TOOLTIP\"] = \"Replace all occurances of some text within some other text.\";\nBlockly.Msg[\"TEXT_REVERSE_HELPURL\"] = \"https://github.com/google/blockly/wiki/Text#reversing-text\";\nBlockly.Msg[\"TEXT_REVERSE_MESSAGE0\"] = \"reverse %1\";\nBlockly.Msg[\"TEXT_REVERSE_TOOLTIP\"] = \"Reverses the order of the characters in the text.\";\nBlockly.Msg[\"TEXT_TEXT_HELPURL\"] = \"https://en.wikipedia.org/wiki/String_(computer_science)\";\nBlockly.Msg[\"TEXT_TEXT_TOOLTIP\"] = \"A letter, word, or line of text.\";\nBlockly.Msg[\"TEXT_TRIM_HELPURL\"] = \"https://github.com/google/blockly/wiki/Text#trimming-removing-spaces\";\nBlockly.Msg[\"TEXT_TRIM_OPERATOR_BOTH\"] = \"trim spaces from both sides of\";\nBlockly.Msg[\"TEXT_TRIM_OPERATOR_LEFT\"] = \"trim spaces from left side of\";\nBlockly.Msg[\"TEXT_TRIM_OPERATOR_RIGHT\"] = \"trim spaces from right side of\";\nBlockly.Msg[\"TEXT_TRIM_TOOLTIP\"] = \"Return a copy of the text with spaces removed from one or both ends.\";\nBlockly.Msg[\"TODAY\"] = \"Today\";\nBlockly.Msg[\"UNDO\"] = \"Undo\";\nBlockly.Msg[\"UNKNOWN\"] = \"Unknown\";\nBlockly.Msg[\"UNNAMED_KEY\"] = \"unnamed\";\nBlockly.Msg[\"VARIABLES_DEFAULT_NAME\"] = \"item\";\nBlockly.Msg[\"VARIABLES_GET_CREATE_SET\"] = \"Create 'set %1'\";\nBlockly.Msg[\"VARIABLES_GET_HELPURL\"] = \"https://github.com/google/blockly/wiki/Variables#get\";\nBlockly.Msg[\"VARIABLES_GET_TOOLTIP\"] = \"Returns the value of this variable.\";\nBlockly.Msg[\"VARIABLES_SET\"] = \"set %1 to %2\";\nBlockly.Msg[\"VARIABLES_SET_CREATE_GET\"] = \"Create 'get %1'\";\nBlockly.Msg[\"VARIABLES_SET_HELPURL\"] = \"https://github.com/google/blockly/wiki/Variables#set\";\nBlockly.Msg[\"VARIABLES_SET_TOOLTIP\"] = \"Sets this variable to be equal to the input.\";\nBlockly.Msg[\"VARIABLE_ALREADY_EXISTS\"] = \"A variable named '%1' already exists.\";\nBlockly.Msg[\"VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE\"] = \"A variable named '%1' already exists for another type: '%2'.\";\nBlockly.Msg[\"VARIABLE_ALREADY_EXISTS_FOR_A_PARAMETER\"] = \"A variable named '%1' already exists as a parameter in the procedure '%2'.\";\nBlockly.Msg[\"WINDOWS\"] = \"Windows\";\nBlockly.Msg[\"WORKSPACE_ARIA_LABEL\"] = \"Blockly Workspace\";\nBlockly.Msg[\"WORKSPACE_COMMENT_DEFAULT_TEXT\"] = \"Say something...\";\nBlockly.Msg[\"CONTROLS_FOREACH_INPUT_DO\"] = Blockly.Msg[\"CONTROLS_REPEAT_INPUT_DO\"];\nBlockly.Msg[\"CONTROLS_FOR_INPUT_DO\"] = Blockly.Msg[\"CONTROLS_REPEAT_INPUT_DO\"];\nBlockly.Msg[\"CONTROLS_IF_ELSEIF_TITLE_ELSEIF\"] = Blockly.Msg[\"CONTROLS_IF_MSG_ELSEIF\"];\nBlockly.Msg[\"CONTROLS_IF_ELSE_TITLE_ELSE\"] = Blockly.Msg[\"CONTROLS_IF_MSG_ELSE\"];\nBlockly.Msg[\"CONTROLS_IF_IF_TITLE_IF\"] = Blockly.Msg[\"CONTROLS_IF_MSG_IF\"];\nBlockly.Msg[\"CONTROLS_IF_MSG_THEN\"] = Blockly.Msg[\"CONTROLS_REPEAT_INPUT_DO\"];\nBlockly.Msg[\"CONTROLS_WHILEUNTIL_INPUT_DO\"] = Blockly.Msg[\"CONTROLS_REPEAT_INPUT_DO\"];\nBlockly.Msg[\"LISTS_CREATE_WITH_ITEM_TITLE\"] = Blockly.Msg[\"VARIABLES_DEFAULT_NAME\"];\nBlockly.Msg[\"LISTS_GET_INDEX_HELPURL\"] = Blockly.Msg[\"LISTS_INDEX_OF_HELPURL\"];\nBlockly.Msg[\"LISTS_GET_INDEX_INPUT_IN_LIST\"] = Blockly.Msg[\"LISTS_INLIST\"];\nBlockly.Msg[\"LISTS_GET_SUBLIST_INPUT_IN_LIST\"] = Blockly.Msg[\"LISTS_INLIST\"];\nBlockly.Msg[\"LISTS_INDEX_OF_INPUT_IN_LIST\"] = Blockly.Msg[\"LISTS_INLIST\"];\nBlockly.Msg[\"LISTS_SET_INDEX_INPUT_IN_LIST\"] = Blockly.Msg[\"LISTS_INLIST\"];\nBlockly.Msg[\"MATH_CHANGE_TITLE_ITEM\"] = Blockly.Msg[\"VARIABLES_DEFAULT_NAME\"];\nBlockly.Msg[\"PROCEDURES_DEFRETURN_COMMENT\"] = Blockly.Msg[\"PROCEDURES_DEFNORETURN_COMMENT\"];\nBlockly.Msg[\"PROCEDURES_DEFRETURN_DO\"] = Blockly.Msg[\"PROCEDURES_DEFNORETURN_DO\"];\nBlockly.Msg[\"PROCEDURES_DEFRETURN_PROCEDURE\"] = Blockly.Msg[\"PROCEDURES_DEFNORETURN_PROCEDURE\"];\nBlockly.Msg[\"PROCEDURES_DEFRETURN_TITLE\"] = Blockly.Msg[\"PROCEDURES_DEFNORETURN_TITLE\"];\nBlockly.Msg[\"TEXT_APPEND_VARIABLE\"] = Blockly.Msg[\"VARIABLES_DEFAULT_NAME\"];\nBlockly.Msg[\"TEXT_CREATE_JOIN_ITEM_TITLE_ITEM\"] = Blockly.Msg[\"VARIABLES_DEFAULT_NAME\"];\n\nBlockly.Msg[\"MATH_HUE\"] = \"230\";\nBlockly.Msg[\"LOOPS_HUE\"] = \"120\";\nBlockly.Msg[\"LISTS_HUE\"] = \"260\";\nBlockly.Msg[\"LOGIC_HUE\"] = \"210\";\nBlockly.Msg[\"VARIABLES_HUE\"] = \"330\";\nBlockly.Msg[\"TEXTS_HUE\"] = \"160\";\nBlockly.Msg[\"PROCEDURES_HUE\"] = \"290\";\nBlockly.Msg[\"COLOUR_HUE\"] = \"20\";\nBlockly.Msg[\"VARIABLES_DYNAMIC_HUE\"] = \"310\";\nreturn Blockly.Msg;\n}));\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n// Former goog.module ID: Blockly.libraryBlocks.variablesDynamic\n\nimport type {Block} from '../core/block.js';\nimport {\n  createBlockDefinitionsFromJsonArray,\n  defineBlocks,\n} from '../core/common.js';\nimport * as ContextMenu from '../core/contextmenu.js';\nimport type {\n  ContextMenuOption,\n  LegacyContextMenuOption,\n} from '../core/contextmenu_registry.js';\nimport {Abstract as AbstractEvent} from '../core/events/events_abstract.js';\nimport * as Extensions from '../core/extensions.js';\nimport '../core/field_label.js';\nimport {FieldVariable} from '../core/field_variable.js';\nimport {Msg} from '../core/msg.js';\nimport * as Variables from '../core/variables.js';\n\n/**\n * A dictionary of the block definitions provided by this module.\n */\nexport const blocks = createBlockDefinitionsFromJsonArray([\n  // Block for variable getter.\n  {\n    'type': 'variables_get_dynamic',\n    'message0': '%1',\n    'args0': [\n      {\n        'type': 'field_variable',\n        'name': 'VAR',\n        'variable': '%{BKY_VARIABLES_DEFAULT_NAME}',\n      },\n    ],\n    'output': null,\n    'style': 'variable_dynamic_blocks',\n    'helpUrl': '%{BKY_VARIABLES_GET_HELPURL}',\n    'tooltip': '%{BKY_VARIABLES_GET_TOOLTIP}',\n    'extensions': ['contextMenu_variableDynamicSetterGetter'],\n  },\n  // Block for variable setter.\n  {\n    'type': 'variables_set_dynamic',\n    'message0': '%{BKY_VARIABLES_SET}',\n    'args0': [\n      {\n        'type': 'field_variable',\n        'name': 'VAR',\n        'variable': '%{BKY_VARIABLES_DEFAULT_NAME}',\n      },\n      {\n        'type': 'input_value',\n        'name': 'VALUE',\n      },\n    ],\n    'previousStatement': null,\n    'nextStatement': null,\n    'style': 'variable_dynamic_blocks',\n    'tooltip': '%{BKY_VARIABLES_SET_TOOLTIP}',\n    'helpUrl': '%{BKY_VARIABLES_SET_HELPURL}',\n    'extensions': ['contextMenu_variableDynamicSetterGetter'],\n  },\n]);\n\n/** Type of a block that has CUSTOM_CONTEXT_MENU_VARIABLE_GETTER_SETTER_MIXIN */\ntype VariableBlock = Block & VariableMixin;\ninterface VariableMixin extends VariableMixinType {}\ntype VariableMixinType =\n  typeof CUSTOM_CONTEXT_MENU_VARIABLE_GETTER_SETTER_MIXIN;\n\n/**\n * Mixin to add context menu items to create getter/setter blocks for this\n * setter/getter.\n * Used by blocks 'variables_set_dynamic' and 'variables_get_dynamic'.\n */\nconst CUSTOM_CONTEXT_MENU_VARIABLE_GETTER_SETTER_MIXIN = {\n  /**\n   * Add menu option to create getter/setter block for this setter/getter.\n   *\n   * @param options List of menu options to add to.\n   */\n  customContextMenu: function (\n    this: VariableBlock,\n    options: Array<ContextMenuOption | LegacyContextMenuOption>,\n  ) {\n    // Getter blocks have the option to create a setter block, and vice versa.\n    if (!this.isInFlyout) {\n      let oppositeType;\n      let contextMenuMsg;\n      if (this.type === 'variables_get_dynamic') {\n        oppositeType = 'variables_set_dynamic';\n        contextMenuMsg = Msg['VARIABLES_GET_CREATE_SET'];\n      } else {\n        oppositeType = 'variables_get_dynamic';\n        contextMenuMsg = Msg['VARIABLES_SET_CREATE_GET'];\n      }\n\n      const varField = this.getField('VAR')!;\n      const newVarBlockState = {\n        type: oppositeType,\n        fields: {VAR: varField.saveState(true)},\n      };\n\n      options.push({\n        enabled: this.workspace.remainingCapacity() > 0,\n        text: contextMenuMsg.replace('%1', varField.getText()),\n        callback: ContextMenu.callbackFactory(this, newVarBlockState),\n      });\n    } else {\n      if (\n        this.type === 'variables_get_dynamic' ||\n        this.type === 'variables_get_reporter_dynamic'\n      ) {\n        const renameOption = {\n          text: Msg['RENAME_VARIABLE'],\n          enabled: true,\n          callback: renameOptionCallbackFactory(this),\n        };\n        const name = this.getField('VAR')!.getText();\n        const deleteOption = {\n          text: Msg['DELETE_VARIABLE'].replace('%1', name),\n          enabled: true,\n          callback: deleteOptionCallbackFactory(this),\n        };\n        options.unshift(renameOption);\n        options.unshift(deleteOption);\n      }\n    }\n  },\n  /**\n   * Called whenever anything on the workspace changes.\n   * Set the connection type for this block.\n   *\n   * @param _e Change event.\n   */\n  onchange: function (this: VariableBlock, _e: AbstractEvent) {\n    const id = this.getFieldValue('VAR');\n    const variableModel = Variables.getVariable(this.workspace, id)!;\n    if (this.type === 'variables_get_dynamic') {\n      this.outputConnection!.setCheck(variableModel.getType());\n    } else {\n      this.getInput('VALUE')!.connection!.setCheck(variableModel.getType());\n    }\n  },\n};\n\n/**\n * Factory for callbacks for rename variable dropdown menu option\n * associated with a variable getter block.\n *\n * @param block The block with the variable to rename.\n * @returns A function that renames the variable.\n */\nconst renameOptionCallbackFactory = function (block: VariableBlock) {\n  return function () {\n    const workspace = block.workspace;\n    const variableField = block.getField('VAR') as FieldVariable;\n    const variable = variableField.getVariable()!;\n    Variables.renameVariable(workspace, variable);\n  };\n};\n\n/**\n * Factory for callbacks for delete variable dropdown menu option\n * associated with a variable getter block.\n *\n * @param block The block with the variable to delete.\n * @returns A function that deletes the variable.\n */\nconst deleteOptionCallbackFactory = function (block: VariableBlock) {\n  return function () {\n    const variableField = block.getField('VAR') as FieldVariable;\n    const variable = variableField.getVariable();\n    if (variable) {\n      Variables.deleteVariable(variable.getWorkspace(), variable, block);\n    }\n  };\n};\n\nExtensions.registerMixin(\n  'contextMenu_variableDynamicSetterGetter',\n  CUSTOM_CONTEXT_MENU_VARIABLE_GETTER_SETTER_MIXIN,\n);\n\n// Register provided blocks.\ndefineBlocks(blocks);\n", "/**\n * @license\n * Copyright 2012 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n// Former goog.module ID: Blockly.libraryBlocks.variables\n\nimport type {Block} from '../core/block.js';\nimport {\n  createBlockDefinitionsFromJsonArray,\n  defineBlocks,\n} from '../core/common.js';\nimport * as ContextMenu from '../core/contextmenu.js';\nimport type {\n  ContextMenuOption,\n  LegacyContextMenuOption,\n} from '../core/contextmenu_registry.js';\nimport * as Extensions from '../core/extensions.js';\nimport '../core/field_label.js';\nimport {FieldVariable} from '../core/field_variable.js';\nimport {Msg} from '../core/msg.js';\nimport * as Variables from '../core/variables.js';\n\n/**\n * A dictionary of the block definitions provided by this module.\n */\nexport const blocks = createBlockDefinitionsFromJsonArray([\n  // Block for variable getter.\n  {\n    'type': 'variables_get',\n    'message0': '%1',\n    'args0': [\n      {\n        'type': 'field_variable',\n        'name': 'VAR',\n        'variable': '%{BKY_VARIABLES_DEFAULT_NAME}',\n      },\n    ],\n    'output': null,\n    'style': 'variable_blocks',\n    'helpUrl': '%{BKY_VARIABLES_GET_HELPURL}',\n    'tooltip': '%{BKY_VARIABLES_GET_TOOLTIP}',\n    'extensions': ['contextMenu_variableSetterGetter'],\n  },\n  // Block for variable setter.\n  {\n    'type': 'variables_set',\n    'message0': '%{BKY_VARIABLES_SET}',\n    'args0': [\n      {\n        'type': 'field_variable',\n        'name': 'VAR',\n        'variable': '%{BKY_VARIABLES_DEFAULT_NAME}',\n      },\n      {\n        'type': 'input_value',\n        'name': 'VALUE',\n      },\n    ],\n    'previousStatement': null,\n    'nextStatement': null,\n    'style': 'variable_blocks',\n    'tooltip': '%{BKY_VARIABLES_SET_TOOLTIP}',\n    'helpUrl': '%{BKY_VARIABLES_SET_HELPURL}',\n    'extensions': ['contextMenu_variableSetterGetter'],\n  },\n]);\n\n/** Type of a block that has CUSTOM_CONTEXT_MENU_VARIABLE_GETTER_SETTER_MIXIN */\ntype VariableBlock = Block & VariableMixin;\ninterface VariableMixin extends VariableMixinType {}\ntype VariableMixinType =\n  typeof CUSTOM_CONTEXT_MENU_VARIABLE_GETTER_SETTER_MIXIN;\n\n/**\n * Mixin to add context menu items to create getter/setter blocks for this\n * setter/getter.\n * Used by blocks 'variables_set' and 'variables_get'.\n */\nconst CUSTOM_CONTEXT_MENU_VARIABLE_GETTER_SETTER_MIXIN = {\n  /**\n   * Add menu option to create getter/setter block for this setter/getter.\n   *\n   * @param options List of menu options to add to.\n   */\n  customContextMenu: function (\n    this: VariableBlock,\n    options: Array<ContextMenuOption | LegacyContextMenuOption>,\n  ) {\n    if (!this.isInFlyout) {\n      let oppositeType;\n      let contextMenuMsg;\n      // Getter blocks have the option to create a setter block, and vice versa.\n      if (this.type === 'variables_get') {\n        oppositeType = 'variables_set';\n        contextMenuMsg = Msg['VARIABLES_GET_CREATE_SET'];\n      } else {\n        oppositeType = 'variables_get';\n        contextMenuMsg = Msg['VARIABLES_SET_CREATE_GET'];\n      }\n\n      const varField = this.getField('VAR')!;\n      const newVarBlockState = {\n        type: oppositeType,\n        fields: {VAR: varField.saveState(true)},\n      };\n\n      options.push({\n        enabled: this.workspace.remainingCapacity() > 0,\n        text: contextMenuMsg.replace('%1', varField.getText()),\n        callback: ContextMenu.callbackFactory(this, newVarBlockState),\n      });\n      // Getter blocks have the option to rename or delete that variable.\n    } else {\n      if (\n        this.type === 'variables_get' ||\n        this.type === 'variables_get_reporter'\n      ) {\n        const renameOption = {\n          text: Msg['RENAME_VARIABLE'],\n          enabled: true,\n          callback: renameOptionCallbackFactory(this),\n        };\n        const name = this.getField('VAR')!.getText();\n        const deleteOption = {\n          text: Msg['DELETE_VARIABLE'].replace('%1', name),\n          enabled: true,\n          callback: deleteOptionCallbackFactory(this),\n        };\n        options.unshift(renameOption);\n        options.unshift(deleteOption);\n      }\n    }\n  },\n};\n\n/**\n * Factory for callbacks for rename variable dropdown menu option\n * associated with a variable getter block.\n *\n * @param block The block with the variable to rename.\n * @returns A function that renames the variable.\n */\nconst renameOptionCallbackFactory = function (\n  block: VariableBlock,\n): () => void {\n  return function () {\n    const workspace = block.workspace;\n    const variableField = block.getField('VAR') as FieldVariable;\n    const variable = variableField.getVariable()!;\n    Variables.renameVariable(workspace, variable);\n  };\n};\n\n/**\n * Factory for callbacks for delete variable dropdown menu option\n * associated with a variable getter block.\n *\n * @param block The block with the variable to delete.\n * @returns A function that deletes the variable.\n */\nconst deleteOptionCallbackFactory = function (\n  block: VariableBlock,\n): () => void {\n  return function () {\n    const variableField = block.getField('VAR') as FieldVariable;\n    const variable = variableField.getVariable();\n    if (variable) {\n      Variables.deleteVariable(variable.getWorkspace(), variable, block);\n    }\n  };\n};\n\nExtensions.registerMixin(\n  'contextMenu_variableSetterGetter',\n  CUSTOM_CONTEXT_MENU_VARIABLE_GETTER_SETTER_MIXIN,\n);\n\n// Register provided blocks.\ndefineBlocks(blocks);\n", "/**\n * @license\n * Copyright 2012 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n// Former goog.module ID: Blockly.libraryBlocks.texts\n\nimport type {Block} from '../core/block.js';\nimport type {BlockSvg} from '../core/block_svg.js';\nimport {\n  createBlockDefinitionsFromJsonArray,\n  defineBlocks,\n} from '../core/common.js';\nimport {Connection} from '../core/connection.js';\nimport * as Extensions from '../core/extensions.js';\nimport {FieldDropdown} from '../core/field_dropdown.js';\nimport {FieldImage} from '../core/field_image.js';\nimport * as fieldRegistry from '../core/field_registry.js';\nimport {FieldTextInput} from '../core/field_textinput.js';\nimport '../core/field_variable.js';\nimport {MutatorIcon} from '../core/icons/mutator_icon.js';\nimport {Align} from '../core/inputs/align.js';\nimport {ValueInput} from '../core/inputs/value_input.js';\nimport {Msg} from '../core/msg.js';\nimport * as xmlUtils from '../core/utils/xml.js';\nimport type {Workspace} from '../core/workspace.js';\n\n/**\n * A dictionary of the block definitions provided by this module.\n */\nexport const blocks = createBlockDefinitionsFromJsonArray([\n  // Block for text value\n  {\n    'type': 'text',\n    'message0': '%1',\n    'args0': [\n      {\n        'type': 'field_input',\n        'name': 'TEXT',\n        'text': '',\n      },\n    ],\n    'output': 'String',\n    'style': 'text_blocks',\n    'helpUrl': '%{BKY_TEXT_TEXT_HELPURL}',\n    'tooltip': '%{BKY_TEXT_TEXT_TOOLTIP}',\n    'extensions': ['text_quotes', 'parent_tooltip_when_inline'],\n  },\n  {\n    'type': 'text_join',\n    'message0': '',\n    'output': 'String',\n    'style': 'text_blocks',\n    'helpUrl': '%{BKY_TEXT_JOIN_HELPURL}',\n    'tooltip': '%{BKY_TEXT_JOIN_TOOLTIP}',\n    'mutator': 'text_join_mutator',\n  },\n  {\n    'type': 'text_create_join_container',\n    'message0': '%{BKY_TEXT_CREATE_JOIN_TITLE_JOIN} %1 %2',\n    'args0': [\n      {\n        'type': 'input_dummy',\n      },\n      {\n        'type': 'input_statement',\n        'name': 'STACK',\n      },\n    ],\n    'style': 'text_blocks',\n    'tooltip': '%{BKY_TEXT_CREATE_JOIN_TOOLTIP}',\n    'enableContextMenu': false,\n  },\n  {\n    'type': 'text_create_join_item',\n    'message0': '%{BKY_TEXT_CREATE_JOIN_ITEM_TITLE_ITEM}',\n    'previousStatement': null,\n    'nextStatement': null,\n    'style': 'text_blocks',\n    'tooltip': '%{BKY_TEXT_CREATE_JOIN_ITEM_TOOLTIP}',\n    'enableContextMenu': false,\n  },\n  {\n    'type': 'text_append',\n    'message0': '%{BKY_TEXT_APPEND_TITLE}',\n    'args0': [\n      {\n        'type': 'field_variable',\n        'name': 'VAR',\n        'variable': '%{BKY_TEXT_APPEND_VARIABLE}',\n      },\n      {\n        'type': 'input_value',\n        'name': 'TEXT',\n      },\n    ],\n    'previousStatement': null,\n    'nextStatement': null,\n    'style': 'text_blocks',\n    'extensions': ['text_append_tooltip'],\n  },\n  {\n    'type': 'text_length',\n    'message0': '%{BKY_TEXT_LENGTH_TITLE}',\n    'args0': [\n      {\n        'type': 'input_value',\n        'name': 'VALUE',\n        'check': ['String', 'Array'],\n      },\n    ],\n    'output': 'Number',\n    'style': 'text_blocks',\n    'tooltip': '%{BKY_TEXT_LENGTH_TOOLTIP}',\n    'helpUrl': '%{BKY_TEXT_LENGTH_HELPURL}',\n  },\n  {\n    'type': 'text_isEmpty',\n    'message0': '%{BKY_TEXT_ISEMPTY_TITLE}',\n    'args0': [\n      {\n        'type': 'input_value',\n        'name': 'VALUE',\n        'check': ['String', 'Array'],\n      },\n    ],\n    'output': 'Boolean',\n    'style': 'text_blocks',\n    'tooltip': '%{BKY_TEXT_ISEMPTY_TOOLTIP}',\n    'helpUrl': '%{BKY_TEXT_ISEMPTY_HELPURL}',\n  },\n  {\n    'type': 'text_indexOf',\n    'message0': '%{BKY_TEXT_INDEXOF_TITLE}',\n    'args0': [\n      {\n        'type': 'input_value',\n        'name': 'VALUE',\n        'check': 'String',\n      },\n      {\n        'type': 'field_dropdown',\n        'name': 'END',\n        'options': [\n          ['%{BKY_TEXT_INDEXOF_OPERATOR_FIRST}', 'FIRST'],\n          ['%{BKY_TEXT_INDEXOF_OPERATOR_LAST}', 'LAST'],\n        ],\n      },\n      {\n        'type': 'input_value',\n        'name': 'FIND',\n        'check': 'String',\n      },\n    ],\n    'output': 'Number',\n    'style': 'text_blocks',\n    'helpUrl': '%{BKY_TEXT_INDEXOF_HELPURL}',\n    'inputsInline': true,\n    'extensions': ['text_indexOf_tooltip'],\n  },\n  {\n    'type': 'text_charAt',\n    'message0': '%{BKY_TEXT_CHARAT_TITLE}', // \"in text %1 %2\"\n    'args0': [\n      {\n        'type': 'input_value',\n        'name': 'VALUE',\n        'check': 'String',\n      },\n      {\n        'type': 'field_dropdown',\n        'name': 'WHERE',\n        'options': [\n          ['%{BKY_TEXT_CHARAT_FROM_START}', 'FROM_START'],\n          ['%{BKY_TEXT_CHARAT_FROM_END}', 'FROM_END'],\n          ['%{BKY_TEXT_CHARAT_FIRST}', 'FIRST'],\n          ['%{BKY_TEXT_CHARAT_LAST}', 'LAST'],\n          ['%{BKY_TEXT_CHARAT_RANDOM}', 'RANDOM'],\n        ],\n      },\n    ],\n    'output': 'String',\n    'style': 'text_blocks',\n    'helpUrl': '%{BKY_TEXT_CHARAT_HELPURL}',\n    'inputsInline': true,\n    'mutator': 'text_charAt_mutator',\n  },\n]);\n\n/** Type of a 'text_get_substring' block. */\ntype GetSubstringBlock = Block & GetSubstringMixin;\ninterface GetSubstringMixin extends GetSubstringType {\n  WHERE_OPTIONS_1: Array<[string, string]>;\n  WHERE_OPTIONS_2: Array<[string, string]>;\n}\ntype GetSubstringType = typeof GET_SUBSTRING_BLOCK;\n\nconst GET_SUBSTRING_BLOCK = {\n  /**\n   * Block for getting substring.\n   */\n  init: function (this: GetSubstringBlock) {\n    this['WHERE_OPTIONS_1'] = [\n      [Msg['TEXT_GET_SUBSTRING_START_FROM_START'], 'FROM_START'],\n      [Msg['TEXT_GET_SUBSTRING_START_FROM_END'], 'FROM_END'],\n      [Msg['TEXT_GET_SUBSTRING_START_FIRST'], 'FIRST'],\n    ];\n    this['WHERE_OPTIONS_2'] = [\n      [Msg['TEXT_GET_SUBSTRING_END_FROM_START'], 'FROM_START'],\n      [Msg['TEXT_GET_SUBSTRING_END_FROM_END'], 'FROM_END'],\n      [Msg['TEXT_GET_SUBSTRING_END_LAST'], 'LAST'],\n    ];\n    this.setHelpUrl(Msg['TEXT_GET_SUBSTRING_HELPURL']);\n    this.setStyle('text_blocks');\n    this.appendValueInput('STRING')\n      .setCheck('String')\n      .appendField(Msg['TEXT_GET_SUBSTRING_INPUT_IN_TEXT']);\n    const createMenu = (n: 1 | 2): FieldDropdown => {\n      const menu = fieldRegistry.fromJson({\n        type: 'field_dropdown',\n        options:\n          this[('WHERE_OPTIONS_' + n) as 'WHERE_OPTIONS_1' | 'WHERE_OPTIONS_2'],\n      }) as FieldDropdown;\n      menu.setValidator(\n        /** @param value The input value. */\n        function (this: FieldDropdown, value: any): null | undefined {\n          const oldValue: string | null = this.getValue();\n          const oldAt = oldValue === 'FROM_START' || oldValue === 'FROM_END';\n          const newAt = value === 'FROM_START' || value === 'FROM_END';\n          if (newAt !== oldAt) {\n            const block = this.getSourceBlock() as GetSubstringBlock;\n            block.updateAt_(n, newAt);\n          }\n          return undefined;\n        },\n      );\n      return menu;\n    };\n    this.appendDummyInput('WHERE1_INPUT').appendField(createMenu(1), 'WHERE1');\n    this.appendDummyInput('AT1');\n    this.appendDummyInput('WHERE2_INPUT').appendField(createMenu(2), 'WHERE2');\n    this.appendDummyInput('AT2');\n    if (Msg['TEXT_GET_SUBSTRING_TAIL']) {\n      this.appendDummyInput('TAIL').appendField(Msg['TEXT_GET_SUBSTRING_TAIL']);\n    }\n    this.setInputsInline(true);\n    this.setOutput(true, 'String');\n    this.updateAt_(1, true);\n    this.updateAt_(2, true);\n    this.setTooltip(Msg['TEXT_GET_SUBSTRING_TOOLTIP']);\n  },\n  /**\n   * Create XML to represent whether there are 'AT' inputs.\n   * Backwards compatible serialization implementation.\n   *\n   * @returns XML storage element.\n   */\n  mutationToDom: function (this: GetSubstringBlock): Element {\n    const container = xmlUtils.createElement('mutation');\n    const isAt1 = this.getInput('AT1') instanceof ValueInput;\n    container.setAttribute('at1', `${isAt1}`);\n    const isAt2 = this.getInput('AT2') instanceof ValueInput;\n    container.setAttribute('at2', `${isAt2}`);\n    return container;\n  },\n  /**\n   * Parse XML to restore the 'AT' inputs.\n   * Backwards compatible serialization implementation.\n   *\n   * @param xmlElement XML storage element.\n   */\n  domToMutation: function (this: GetSubstringBlock, xmlElement: Element) {\n    const isAt1 = xmlElement.getAttribute('at1') === 'true';\n    const isAt2 = xmlElement.getAttribute('at2') === 'true';\n    this.updateAt_(1, isAt1);\n    this.updateAt_(2, isAt2);\n  },\n\n  // This block does not need JSO serialization hooks (saveExtraState and\n  // loadExtraState) because the state of this object is already encoded in the\n  // dropdown values.\n  // XML hooks are kept for backwards compatibility.\n\n  /**\n   * Create or delete an input for a numeric index.\n   * This block has two such inputs, independent of each other.\n   *\n   * @internal\n   * @param n Which input to modify (either 1 or 2).\n   * @param isAt True if the input includes a value connection, false otherwise.\n   */\n  updateAt_: function (this: GetSubstringBlock, n: 1 | 2, isAt: boolean) {\n    // Create or delete an input for the numeric index.\n    // Destroy old 'AT' and 'ORDINAL' inputs.\n    this.removeInput('AT' + n);\n    this.removeInput('ORDINAL' + n, true);\n    // Create either a value 'AT' input or a dummy input.\n    if (isAt) {\n      this.appendValueInput('AT' + n).setCheck('Number');\n      if (Msg['ORDINAL_NUMBER_SUFFIX']) {\n        this.appendDummyInput('ORDINAL' + n).appendField(\n          Msg['ORDINAL_NUMBER_SUFFIX'],\n        );\n      }\n    } else {\n      this.appendDummyInput('AT' + n);\n    }\n    // Move tail, if present, to end of block.\n    if (n === 2 && Msg['TEXT_GET_SUBSTRING_TAIL']) {\n      this.removeInput('TAIL', true);\n      this.appendDummyInput('TAIL').appendField(Msg['TEXT_GET_SUBSTRING_TAIL']);\n    }\n    if (n === 1) {\n      this.moveInputBefore('AT1', 'WHERE2_INPUT');\n      if (this.getInput('ORDINAL1')) {\n        this.moveInputBefore('ORDINAL1', 'WHERE2_INPUT');\n      }\n    }\n  },\n};\n\nblocks['text_getSubstring'] = GET_SUBSTRING_BLOCK;\n\nblocks['text_changeCase'] = {\n  /**\n   * Block for changing capitalization.\n   */\n  init: function (this: Block) {\n    const OPERATORS = [\n      [Msg['TEXT_CHANGECASE_OPERATOR_UPPERCASE'], 'UPPERCASE'],\n      [Msg['TEXT_CHANGECASE_OPERATOR_LOWERCASE'], 'LOWERCASE'],\n      [Msg['TEXT_CHANGECASE_OPERATOR_TITLECASE'], 'TITLECASE'],\n    ];\n    this.setHelpUrl(Msg['TEXT_CHANGECASE_HELPURL']);\n    this.setStyle('text_blocks');\n    this.appendValueInput('TEXT')\n      .setCheck('String')\n      .appendField(\n        fieldRegistry.fromJson({\n          type: 'field_dropdown',\n          options: OPERATORS,\n        }) as FieldDropdown,\n        'CASE',\n      );\n    this.setOutput(true, 'String');\n    this.setTooltip(Msg['TEXT_CHANGECASE_TOOLTIP']);\n  },\n};\n\nblocks['text_trim'] = {\n  /**\n   * Block for trimming spaces.\n   */\n  init: function (this: Block) {\n    const OPERATORS = [\n      [Msg['TEXT_TRIM_OPERATOR_BOTH'], 'BOTH'],\n      [Msg['TEXT_TRIM_OPERATOR_LEFT'], 'LEFT'],\n      [Msg['TEXT_TRIM_OPERATOR_RIGHT'], 'RIGHT'],\n    ];\n    this.setHelpUrl(Msg['TEXT_TRIM_HELPURL']);\n    this.setStyle('text_blocks');\n    this.appendValueInput('TEXT')\n      .setCheck('String')\n      .appendField(\n        fieldRegistry.fromJson({\n          type: 'field_dropdown',\n          options: OPERATORS,\n        }) as FieldDropdown,\n        'MODE',\n      );\n    this.setOutput(true, 'String');\n    this.setTooltip(Msg['TEXT_TRIM_TOOLTIP']);\n  },\n};\n\nblocks['text_print'] = {\n  /**\n   * Block for print statement.\n   */\n  init: function (this: Block) {\n    this.jsonInit({\n      'message0': Msg['TEXT_PRINT_TITLE'],\n      'args0': [\n        {\n          'type': 'input_value',\n          'name': 'TEXT',\n        },\n      ],\n      'previousStatement': null,\n      'nextStatement': null,\n      'style': 'text_blocks',\n      'tooltip': Msg['TEXT_PRINT_TOOLTIP'],\n      'helpUrl': Msg['TEXT_PRINT_HELPURL'],\n    });\n  },\n};\n\ntype PromptCommonBlock = Block & PromptCommonMixin;\ninterface PromptCommonMixin extends PromptCommonType {}\ntype PromptCommonType = typeof PROMPT_COMMON;\n\n/**\n * Common properties for the text_prompt_ext and text_prompt blocks\n * definitions.\n */\nconst PROMPT_COMMON = {\n  /**\n   * Modify this block to have the correct output type.\n   *\n   * @internal\n   * @param newOp The new output type. Should be either 'TEXT' or 'NUMBER'.\n   */\n  updateType_: function (this: PromptCommonBlock, newOp: string) {\n    this.outputConnection!.setCheck(newOp === 'NUMBER' ? 'Number' : 'String');\n  },\n  /**\n   * Create XML to represent the output type.\n   * Backwards compatible serialization implementation.\n   *\n   * @returns XML storage element.\n   */\n  mutationToDom: function (this: PromptCommonBlock): Element {\n    const container = xmlUtils.createElement('mutation');\n    container.setAttribute('type', this.getFieldValue('TYPE'));\n    return container;\n  },\n  /**\n   * Parse XML to restore the output type.\n   * Backwards compatible serialization implementation.\n   *\n   * @param xmlElement XML storage element.\n   */\n  domToMutation: function (this: PromptCommonBlock, xmlElement: Element) {\n    this.updateType_(xmlElement.getAttribute('type')!);\n  },\n\n  // These blocks do not need JSO serialization hooks (saveExtraState\n  // and loadExtraState) because the state of this object is already\n  // encoded in the dropdown values.\n  // XML hooks are kept for backwards compatibility.\n};\n\nblocks['text_prompt_ext'] = {\n  ...PROMPT_COMMON,\n  /**\n   * Block for prompt function (external message).\n   */\n  init: function (this: PromptCommonBlock) {\n    const TYPES = [\n      [Msg['TEXT_PROMPT_TYPE_TEXT'], 'TEXT'],\n      [Msg['TEXT_PROMPT_TYPE_NUMBER'], 'NUMBER'],\n    ];\n    this.setHelpUrl(Msg['TEXT_PROMPT_HELPURL']);\n    this.setStyle('text_blocks');\n    const dropdown = fieldRegistry.fromJson({\n      type: 'field_dropdown',\n      options: TYPES,\n    }) as FieldDropdown;\n    dropdown.setValidator((newOp: string) => {\n      this.updateType_(newOp);\n      return undefined; // FieldValidators can't be void.  Use option as-is.\n    });\n    this.appendValueInput('TEXT').appendField(dropdown, 'TYPE');\n    this.setOutput(true, 'String');\n    this.setTooltip(() => {\n      return this.getFieldValue('TYPE') === 'TEXT'\n        ? Msg['TEXT_PROMPT_TOOLTIP_TEXT']\n        : Msg['TEXT_PROMPT_TOOLTIP_NUMBER'];\n    });\n  },\n};\n\ntype PromptBlock = Block & PromptCommonMixin & QuoteImageMixin;\n\nblocks['text_prompt'] = {\n  ...PROMPT_COMMON,\n  /**\n   * Block for prompt function (internal message).\n   * The 'text_prompt_ext' block is preferred as it is more flexible.\n   */\n  init: function (this: PromptBlock) {\n    this.mixin(QUOTE_IMAGE_MIXIN);\n    const TYPES = [\n      [Msg['TEXT_PROMPT_TYPE_TEXT'], 'TEXT'],\n      [Msg['TEXT_PROMPT_TYPE_NUMBER'], 'NUMBER'],\n    ];\n\n    this.setHelpUrl(Msg['TEXT_PROMPT_HELPURL']);\n    this.setStyle('text_blocks');\n    const dropdown = fieldRegistry.fromJson({\n      type: 'field_dropdown',\n      options: TYPES,\n    }) as FieldDropdown;\n    dropdown.setValidator((newOp: string) => {\n      this.updateType_(newOp);\n      return undefined; // FieldValidators can't be void.  Use option as-is.\n    });\n    this.appendDummyInput()\n      .appendField(dropdown, 'TYPE')\n      .appendField(this.newQuote_(true))\n      .appendField(\n        fieldRegistry.fromJson({\n          type: 'field_input',\n          text: '',\n        }) as FieldTextInput,\n        'TEXT',\n      )\n      .appendField(this.newQuote_(false));\n    this.setOutput(true, 'String');\n    this.setTooltip(() => {\n      return this.getFieldValue('TYPE') === 'TEXT'\n        ? Msg['TEXT_PROMPT_TOOLTIP_TEXT']\n        : Msg['TEXT_PROMPT_TOOLTIP_NUMBER'];\n    });\n  },\n};\n\nblocks['text_count'] = {\n  /**\n   * Block for counting how many times one string appears within another string.\n   */\n  init: function (this: Block) {\n    this.jsonInit({\n      'message0': Msg['TEXT_COUNT_MESSAGE0'],\n      'args0': [\n        {\n          'type': 'input_value',\n          'name': 'SUB',\n          'check': 'String',\n        },\n        {\n          'type': 'input_value',\n          'name': 'TEXT',\n          'check': 'String',\n        },\n      ],\n      'output': 'Number',\n      'inputsInline': true,\n      'style': 'text_blocks',\n      'tooltip': Msg['TEXT_COUNT_TOOLTIP'],\n      'helpUrl': Msg['TEXT_COUNT_HELPURL'],\n    });\n  },\n};\n\nblocks['text_replace'] = {\n  /**\n   * Block for replacing one string with another in the text.\n   */\n  init: function (this: Block) {\n    this.jsonInit({\n      'message0': Msg['TEXT_REPLACE_MESSAGE0'],\n      'args0': [\n        {\n          'type': 'input_value',\n          'name': 'FROM',\n          'check': 'String',\n        },\n        {\n          'type': 'input_value',\n          'name': 'TO',\n          'check': 'String',\n        },\n        {\n          'type': 'input_value',\n          'name': 'TEXT',\n          'check': 'String',\n        },\n      ],\n      'output': 'String',\n      'inputsInline': true,\n      'style': 'text_blocks',\n      'tooltip': Msg['TEXT_REPLACE_TOOLTIP'],\n      'helpUrl': Msg['TEXT_REPLACE_HELPURL'],\n    });\n  },\n};\n\nblocks['text_reverse'] = {\n  /**\n   * Block for reversing a string.\n   */\n  init: function (this: Block) {\n    this.jsonInit({\n      'message0': Msg['TEXT_REVERSE_MESSAGE0'],\n      'args0': [\n        {\n          'type': 'input_value',\n          'name': 'TEXT',\n          'check': 'String',\n        },\n      ],\n      'output': 'String',\n      'inputsInline': true,\n      'style': 'text_blocks',\n      'tooltip': Msg['TEXT_REVERSE_TOOLTIP'],\n      'helpUrl': Msg['TEXT_REVERSE_HELPURL'],\n    });\n  },\n};\n\n/** Type of a block that has QUOTE_IMAGE_MIXIN */\ntype QuoteImageBlock = Block & QuoteImageMixin;\ninterface QuoteImageMixin extends QuoteImageMixinType {}\ntype QuoteImageMixinType = typeof QUOTE_IMAGE_MIXIN;\n\nconst QUOTE_IMAGE_MIXIN = {\n  /**\n   * Image data URI of an LTR opening double quote (same as RTL closing double\n   * quote).\n   */\n  QUOTE_IMAGE_LEFT_DATAURI:\n    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAKCAQAAAAqJXdxAAAA' +\n    'n0lEQVQI1z3OMa5BURSF4f/cQhAKjUQhuQmFNwGJEUi0RKN5rU7FHKhpjEH3TEMtkdBSCY' +\n    '1EIv8r7nFX9e29V7EBAOvu7RPjwmWGH/VuF8CyN9/OAdvqIXYLvtRaNjx9mMTDyo+NjAN1' +\n    'HNcl9ZQ5oQMM3dgDUqDo1l8DzvwmtZN7mnD+PkmLa+4mhrxVA9fRowBWmVBhFy5gYEjKMf' +\n    'z9AylsaRRgGzvZAAAAAElFTkSuQmCC',\n  /**\n   * Image data URI of an LTR closing double quote (same as RTL opening double\n   * quote).\n   */\n  QUOTE_IMAGE_RIGHT_DATAURI:\n    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAKCAQAAAAqJXdxAAAA' +\n    'qUlEQVQI1z3KvUpCcRiA8ef9E4JNHhI0aFEacm1o0BsI0Slx8wa8gLauoDnoBhq7DcfWhg' +\n    'gONDmJJgqCPA7neJ7p934EOOKOnM8Q7PDElo/4x4lFb2DmuUjcUzS3URnGib9qaPNbuXvB' +\n    'O3sGPHJDRG6fGVdMSeWDP2q99FQdFrz26Gu5Tq7dFMzUvbXy8KXeAj57cOklgA+u1B5Aos' +\n    'lLtGIHQMaCVnwDnADZIFIrXsoXrgAAAABJRU5ErkJggg==',\n  /**\n   * Pixel width of QUOTE_IMAGE_LEFT_DATAURI and QUOTE_IMAGE_RIGHT_DATAURI.\n   */\n  QUOTE_IMAGE_WIDTH: 12,\n  /**\n   * Pixel height of QUOTE_IMAGE_LEFT_DATAURI and QUOTE_IMAGE_RIGHT_DATAURI.\n   */\n  QUOTE_IMAGE_HEIGHT: 12,\n\n  /**\n   * Inserts appropriate quote images before and after the named field.\n   *\n   * @param fieldName The name of the field to wrap with quotes.\n   */\n  quoteField_: function (this: QuoteImageBlock, fieldName: string) {\n    for (let i = 0, input; (input = this.inputList[i]); i++) {\n      for (let j = 0, field; (field = input.fieldRow[j]); j++) {\n        if (fieldName === field.name) {\n          input.insertFieldAt(j, this.newQuote_(true));\n          input.insertFieldAt(j + 2, this.newQuote_(false));\n          return;\n        }\n      }\n    }\n    console.warn(\n      'field named \"' + fieldName + '\" not found in ' + this.toDevString(),\n    );\n  },\n\n  /**\n   * A helper function that generates a FieldImage of an opening or\n   * closing double quote. The selected quote will be adapted for RTL blocks.\n   *\n   * @param open If the image should be open quote (“ in LTR).\n   *     Otherwise, a closing quote is used (” in LTR).\n   * @returns The new field.\n   */\n  newQuote_: function (this: QuoteImageBlock, open: boolean): FieldImage {\n    const isLeft = this.RTL ? !open : open;\n    const dataUri = isLeft\n      ? this.QUOTE_IMAGE_LEFT_DATAURI\n      : this.QUOTE_IMAGE_RIGHT_DATAURI;\n    return fieldRegistry.fromJson({\n      type: 'field_image',\n      src: dataUri,\n      width: this.QUOTE_IMAGE_WIDTH,\n      height: this.QUOTE_IMAGE_HEIGHT,\n      alt: isLeft ? '\\u201C' : '\\u201D',\n    }) as FieldImage;\n  },\n};\n\n/**\n * Wraps TEXT field with images of double quote characters.\n */\nconst QUOTES_EXTENSION = function (this: QuoteImageBlock) {\n  this.mixin(QUOTE_IMAGE_MIXIN);\n  this.quoteField_('TEXT');\n};\n\n/**\n * Type of a block that has TEXT_JOIN_MUTATOR_MIXIN\n *\n * @internal\n */\nexport type JoinMutatorBlock = BlockSvg & JoinMutatorMixin & QuoteImageMixin;\ninterface JoinMutatorMixin extends JoinMutatorMixinType {}\ntype JoinMutatorMixinType = typeof JOIN_MUTATOR_MIXIN;\n\n/** Type of a item block in the text_join_mutator bubble. */\ntype JoinItemBlock = BlockSvg & JoinItemMixin;\ninterface JoinItemMixin {\n  valueConnection_: Connection | null;\n}\n\n/**\n * Mixin for mutator functions in the 'text_join_mutator' extension.\n */\nconst JOIN_MUTATOR_MIXIN = {\n  itemCount_: 0,\n  /**\n   * Create XML to represent number of text inputs.\n   * Backwards compatible serialization implementation.\n   *\n   * @returns XML storage element.\n   */\n  mutationToDom: function (this: JoinMutatorBlock): Element {\n    const container = xmlUtils.createElement('mutation');\n    container.setAttribute('items', `${this.itemCount_}`);\n    return container;\n  },\n  /**\n   * Parse XML to restore the text inputs.\n   * Backwards compatible serialization implementation.\n   *\n   * @param xmlElement XML storage element.\n   */\n  domToMutation: function (this: JoinMutatorBlock, xmlElement: Element) {\n    this.itemCount_ = parseInt(xmlElement.getAttribute('items')!, 10);\n    this.updateShape_();\n  },\n  /**\n   * Returns the state of this block as a JSON serializable object.\n   *\n   * @returns The state of this block, ie the item count.\n   */\n  saveExtraState: function (this: JoinMutatorBlock): {itemCount: number} {\n    return {\n      'itemCount': this.itemCount_,\n    };\n  },\n  /**\n   * Applies the given state to this block.\n   *\n   * @param state The state to apply to this block, ie the item count.\n   */\n  loadExtraState: function (this: JoinMutatorBlock, state: {[x: string]: any}) {\n    this.itemCount_ = state['itemCount'];\n    this.updateShape_();\n  },\n  /**\n   * Populate the mutator's dialog with this block's components.\n   *\n   * @param workspace Mutator's workspace.\n   * @returns Root block in mutator.\n   */\n  decompose: function (this: JoinMutatorBlock, workspace: Workspace): Block {\n    const containerBlock = workspace.newBlock(\n      'text_create_join_container',\n    ) as BlockSvg;\n    containerBlock.initSvg();\n    let connection = containerBlock.getInput('STACK')!.connection!;\n    for (let i = 0; i < this.itemCount_; i++) {\n      const itemBlock = workspace.newBlock(\n        'text_create_join_item',\n      ) as JoinItemBlock;\n      itemBlock.initSvg();\n      connection.connect(itemBlock.previousConnection);\n      connection = itemBlock.nextConnection;\n    }\n    return containerBlock;\n  },\n  /**\n   * Reconfigure this block based on the mutator dialog's components.\n   *\n   * @param containerBlock Root block in mutator.\n   */\n  compose: function (this: JoinMutatorBlock, containerBlock: Block) {\n    let itemBlock = containerBlock.getInputTargetBlock(\n      'STACK',\n    ) as JoinItemBlock;\n    // Count number of inputs.\n    const connections = [];\n    while (itemBlock) {\n      if (itemBlock.isInsertionMarker()) {\n        itemBlock = itemBlock.getNextBlock() as JoinItemBlock;\n        continue;\n      }\n      connections.push(itemBlock.valueConnection_);\n      itemBlock = itemBlock.getNextBlock() as JoinItemBlock;\n    }\n    // Disconnect any children that don't belong.\n    for (let i = 0; i < this.itemCount_; i++) {\n      const connection = this.getInput('ADD' + i)!.connection!.targetConnection;\n      if (connection && !connections.includes(connection)) {\n        connection.disconnect();\n      }\n    }\n    this.itemCount_ = connections.length;\n    this.updateShape_();\n    // Reconnect any child blocks.\n    for (let i = 0; i < this.itemCount_; i++) {\n      connections[i]?.reconnect(this, 'ADD' + i);\n    }\n  },\n  /**\n   * Store pointers to any connected child blocks.\n   *\n   * @param containerBlock Root block in mutator.\n   */\n  saveConnections: function (this: JoinMutatorBlock, containerBlock: Block) {\n    let itemBlock = containerBlock.getInputTargetBlock('STACK');\n    let i = 0;\n    while (itemBlock) {\n      if (itemBlock.isInsertionMarker()) {\n        itemBlock = itemBlock.getNextBlock();\n        continue;\n      }\n      const input = this.getInput('ADD' + i);\n      (itemBlock as JoinItemBlock).valueConnection_ =\n        input && input.connection!.targetConnection;\n      itemBlock = itemBlock.getNextBlock();\n      i++;\n    }\n  },\n  /**\n   * Modify this block to have the correct number of inputs.\n   *\n   */\n  updateShape_: function (this: JoinMutatorBlock) {\n    if (this.itemCount_ && this.getInput('EMPTY')) {\n      this.removeInput('EMPTY');\n    } else if (!this.itemCount_ && !this.getInput('EMPTY')) {\n      this.appendDummyInput('EMPTY')\n        .appendField(this.newQuote_(true))\n        .appendField(this.newQuote_(false));\n    }\n    // Add new inputs.\n    for (let i = 0; i < this.itemCount_; i++) {\n      if (!this.getInput('ADD' + i)) {\n        const input = this.appendValueInput('ADD' + i).setAlign(Align.RIGHT);\n        if (i === 0) {\n          input.appendField(Msg['TEXT_JOIN_TITLE_CREATEWITH']);\n        }\n      }\n    }\n    // Remove deleted inputs.\n    for (let i = this.itemCount_; this.getInput('ADD' + i); i++) {\n      this.removeInput('ADD' + i);\n    }\n  },\n};\n\n/**\n * Performs final setup of a text_join block.\n */\nconst JOIN_EXTENSION = function (this: JoinMutatorBlock) {\n  // Add the quote mixin for the itemCount_ = 0 case.\n  this.mixin(QUOTE_IMAGE_MIXIN);\n  // Initialize the mutator values.\n  this.itemCount_ = 2;\n  this.updateShape_();\n  // Configure the mutator UI.\n  this.setMutator(new MutatorIcon(['text_create_join_item'], this));\n};\n\n// Update the tooltip of 'text_append' block to reference the variable.\nExtensions.register(\n  'text_append_tooltip',\n  Extensions.buildTooltipWithFieldText('%{BKY_TEXT_APPEND_TOOLTIP}', 'VAR'),\n);\n\n/**\n * Update the tooltip of 'text_append' block to reference the variable.\n */\nconst INDEXOF_TOOLTIP_EXTENSION = function (this: Block) {\n  this.setTooltip(() => {\n    return Msg['TEXT_INDEXOF_TOOLTIP'].replace(\n      '%1',\n      this.workspace.options.oneBasedIndex ? '0' : '-1',\n    );\n  });\n};\n\n/** Type of a block that has TEXT_CHARAT_MUTATOR_MIXIN */\ntype CharAtBlock = Block & CharAtMixin;\ninterface CharAtMixin extends CharAtMixinType {}\ntype CharAtMixinType = typeof CHARAT_MUTATOR_MIXIN;\n\n/**\n * Mixin for mutator functions in the 'text_charAt_mutator' extension.\n */\nconst CHARAT_MUTATOR_MIXIN = {\n  isAt_: false,\n  /**\n   * Create XML to represent whether there is an 'AT' input.\n   * Backwards compatible serialization implementation.\n   *\n   * @returns XML storage element.\n   */\n  mutationToDom: function (this: CharAtBlock): Element {\n    const container = xmlUtils.createElement('mutation');\n    container.setAttribute('at', `${this.isAt_}`);\n    return container;\n  },\n  /**\n   * Parse XML to restore the 'AT' input.\n   * Backwards compatible serialization implementation.\n   *\n   * @param xmlElement XML storage element.\n   */\n  domToMutation: function (this: CharAtBlock, xmlElement: Element) {\n    // Note: Until January 2013 this block did not have mutations,\n    // so 'at' defaults to true.\n    const isAt = xmlElement.getAttribute('at') !== 'false';\n    this.updateAt_(isAt);\n  },\n\n  // This block does not need JSO serialization hooks (saveExtraState and\n  // loadExtraState) because the state of this object is already encoded in the\n  // dropdown values.\n  // XML hooks are kept for backwards compatibility.\n\n  /**\n   * Create or delete an input for the numeric index.\n   *\n   * @internal\n   * @param isAt True if the input should exist.\n   */\n  updateAt_: function (this: CharAtBlock, isAt: boolean) {\n    // Destroy old 'AT' and 'ORDINAL' inputs.\n    this.removeInput('AT', true);\n    this.removeInput('ORDINAL', true);\n    // Create either a value 'AT' input or a dummy input.\n    if (isAt) {\n      this.appendValueInput('AT').setCheck('Number');\n      if (Msg['ORDINAL_NUMBER_SUFFIX']) {\n        this.appendDummyInput('ORDINAL').appendField(\n          Msg['ORDINAL_NUMBER_SUFFIX'],\n        );\n      }\n    }\n    if (Msg['TEXT_CHARAT_TAIL']) {\n      this.removeInput('TAIL', true);\n      this.appendDummyInput('TAIL').appendField(Msg['TEXT_CHARAT_TAIL']);\n    }\n\n    this.isAt_ = isAt;\n  },\n};\n\n/**\n * Does the initial mutator update of text_charAt and adds the tooltip\n */\nconst CHARAT_EXTENSION = function (this: CharAtBlock) {\n  const dropdown = this.getField('WHERE') as FieldDropdown;\n  dropdown.setValidator(function (this: FieldDropdown, value: any) {\n    const newAt = value === 'FROM_START' || value === 'FROM_END';\n    const block = this.getSourceBlock() as CharAtBlock;\n    if (newAt !== block.isAt_) {\n      block.updateAt_(newAt);\n    }\n    return undefined; // FieldValidators can't be void.  Use option as-is.\n  });\n  this.updateAt_(true);\n  this.setTooltip(() => {\n    const where = this.getFieldValue('WHERE');\n    let tooltip = Msg['TEXT_CHARAT_TOOLTIP'];\n    if (where === 'FROM_START' || where === 'FROM_END') {\n      const msg =\n        where === 'FROM_START'\n          ? Msg['LISTS_INDEX_FROM_START_TOOLTIP']\n          : Msg['LISTS_INDEX_FROM_END_TOOLTIP'];\n      if (msg) {\n        tooltip +=\n          '  ' +\n          msg.replace('%1', this.workspace.options.oneBasedIndex ? '#1' : '#0');\n      }\n    }\n    return tooltip;\n  });\n};\n\nExtensions.register('text_indexOf_tooltip', INDEXOF_TOOLTIP_EXTENSION);\n\nExtensions.register('text_quotes', QUOTES_EXTENSION);\n\nExtensions.registerMixin('quote_image_mixin', QUOTE_IMAGE_MIXIN);\n\nExtensions.registerMutator(\n  'text_join_mutator',\n  JOIN_MUTATOR_MIXIN,\n  JOIN_EXTENSION,\n);\n\nExtensions.registerMutator(\n  'text_charAt_mutator',\n  CHARAT_MUTATOR_MIXIN,\n  CHARAT_EXTENSION,\n);\n\n// Register provided blocks.\ndefineBlocks(blocks);\n", "/**\n * @license\n * Copyright 2012 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n// Former goog.module ID: Blockly.libraryBlocks.procedures\n\nimport type {Block} from '../core/block.js';\nimport type {BlockSvg} from '../core/block_svg.js';\nimport type {BlockDefinition} from '../core/blocks.js';\nimport {defineBlocks} from '../core/common.js';\nimport {config} from '../core/config.js';\nimport type {Connection} from '../core/connection.js';\nimport * as ContextMenu from '../core/contextmenu.js';\nimport type {\n  ContextMenuOption,\n  LegacyContextMenuOption,\n} from '../core/contextmenu_registry.js';\nimport * as Events from '../core/events/events.js';\nimport type {Abstract as AbstractEvent} from '../core/events/events_abstract.js';\nimport type {BlockChange} from '../core/events/events_block_change.js';\nimport type {BlockCreate} from '../core/events/events_block_create.js';\nimport * as eventUtils from '../core/events/utils.js';\nimport {FieldCheckbox} from '../core/field_checkbox.js';\nimport {FieldLabel} from '../core/field_label.js';\nimport * as fieldRegistry from '../core/field_registry.js';\nimport {FieldTextInput} from '../core/field_textinput.js';\nimport {getFocusManager} from '../core/focus_manager.js';\nimport '../core/icons/comment_icon.js';\nimport {MutatorIcon as Mutator} from '../core/icons/mutator_icon.js';\nimport '../core/icons/warning_icon.js';\nimport {Align} from '../core/inputs/align.js';\nimport type {\n  IVariableModel,\n  IVariableState,\n} from '../core/interfaces/i_variable_model.js';\nimport {Msg} from '../core/msg.js';\nimport {Names} from '../core/names.js';\nimport * as Procedures from '../core/procedures.js';\nimport * as xmlUtils from '../core/utils/xml.js';\nimport * as Variables from '../core/variables.js';\nimport type {Workspace} from '../core/workspace.js';\nimport type {WorkspaceSvg} from '../core/workspace_svg.js';\nimport * as Xml from '../core/xml.js';\n\n/** A dictionary of the block definitions provided by this module. */\nexport const blocks: {[key: string]: BlockDefinition} = {};\n\n/** Type of a block using the PROCEDURE_DEF_COMMON mixin. */\ntype ProcedureBlock = Block & ProcedureMixin;\ninterface ProcedureMixin extends ProcedureMixinType {\n  arguments_: string[];\n  argumentVarModels_: IVariableModel<IVariableState>[];\n  callType_: string;\n  paramIds_: string[];\n  hasStatements_: boolean;\n  statementConnection_: Connection | null;\n}\ntype ProcedureMixinType = typeof PROCEDURE_DEF_COMMON;\n\n/** Extra state for serialising procedure blocks. */\ntype ProcedureExtraState = {\n  params?: Array<{name: string; id: string}>;\n  hasStatements: boolean;\n};\n\n/**\n * Common properties for the procedure_defnoreturn and\n * procedure_defreturn blocks.\n */\nconst PROCEDURE_DEF_COMMON = {\n  /**\n   * Add or remove the statement block from this function definition.\n   *\n   * @param hasStatements True if a statement block is needed.\n   */\n  setStatements_: function (this: ProcedureBlock, hasStatements: boolean) {\n    if (this.hasStatements_ === hasStatements) {\n      return;\n    }\n    if (hasStatements) {\n      this.appendStatementInput('STACK').appendField(\n        Msg['PROCEDURES_DEFNORETURN_DO'],\n      );\n      if (this.getInput('RETURN')) {\n        this.moveInputBefore('STACK', 'RETURN');\n      }\n    } else {\n      this.removeInput('STACK', true);\n    }\n    this.hasStatements_ = hasStatements;\n  },\n  /**\n   * Update the display of parameters for this procedure definition block.\n   *\n   * @internal\n   */\n  updateParams_: function (this: ProcedureBlock) {\n    // Merge the arguments into a human-readable list.\n    let paramString = '';\n    if (this.arguments_.length) {\n      paramString =\n        Msg['PROCEDURES_BEFORE_PARAMS'] + ' ' + this.arguments_.join(', ');\n    }\n    // The params field is deterministic based on the mutation,\n    // no need to fire a change event.\n    Events.disable();\n    try {\n      this.setFieldValue(paramString, 'PARAMS');\n    } finally {\n      Events.enable();\n    }\n  },\n  /**\n   * Create XML to represent the argument inputs.\n   * Backwards compatible serialization implementation.\n   *\n   * @param opt_paramIds If true include the IDs of the parameter\n   *     quarks.  Used by Procedures.mutateCallers for reconnection.\n   * @returns  XML storage element.\n   */\n  mutationToDom: function (\n    this: ProcedureBlock,\n    opt_paramIds: boolean,\n  ): Element {\n    const container = xmlUtils.createElement('mutation');\n    if (opt_paramIds) {\n      container.setAttribute('name', this.getFieldValue('NAME'));\n    }\n    for (let i = 0; i < this.argumentVarModels_.length; i++) {\n      const parameter = xmlUtils.createElement('arg');\n      const argModel = this.argumentVarModels_[i];\n      parameter.setAttribute('name', argModel.getName());\n      parameter.setAttribute('varid', argModel.getId());\n      if (opt_paramIds && this.paramIds_) {\n        parameter.setAttribute('paramId', this.paramIds_[i]);\n      }\n      container.appendChild(parameter);\n    }\n\n    // Save whether the statement input is visible.\n    if (!this.hasStatements_) {\n      container.setAttribute('statements', 'false');\n    }\n    return container;\n  },\n  /**\n   * Parse XML to restore the argument inputs.\n   * Backwards compatible serialization implementation.\n   *\n   * @param xmlElement XML storage element.\n   */\n  domToMutation: function (this: ProcedureBlock, xmlElement: Element) {\n    this.arguments_ = [];\n    this.argumentVarModels_ = [];\n    for (let i = 0, childNode; (childNode = xmlElement.childNodes[i]); i++) {\n      if (childNode.nodeName.toLowerCase() === 'arg') {\n        const childElement = childNode as Element;\n        const varName = childElement.getAttribute('name')!;\n        const varId =\n          childElement.getAttribute('varid') ||\n          childElement.getAttribute('varId');\n        this.arguments_.push(varName);\n        const variable = Variables.getOrCreateVariablePackage(\n          this.workspace,\n          varId,\n          varName,\n          '',\n        );\n        if (variable !== null) {\n          this.argumentVarModels_.push(variable);\n        } else {\n          console.log(\n            `Failed to create a variable named \"${varName}\", ignoring.`,\n          );\n        }\n      }\n    }\n    this.updateParams_();\n    Procedures.mutateCallers(this);\n\n    // Show or hide the statement input.\n    this.setStatements_(xmlElement.getAttribute('statements') !== 'false');\n  },\n  /**\n   * Returns the state of this block as a JSON serializable object.\n   *\n   * @returns The state of this block, eg the parameters and statements.\n   */\n  saveExtraState: function (this: ProcedureBlock): ProcedureExtraState | null {\n    if (!this.argumentVarModels_.length && this.hasStatements_) {\n      return null;\n    }\n    const state = Object.create(null);\n    if (this.argumentVarModels_.length) {\n      state['params'] = [];\n      for (let i = 0; i < this.argumentVarModels_.length; i++) {\n        state['params'].push({\n          // We don't need to serialize the name, but just in case we decide\n          // to separate params from variables.\n          'name': this.argumentVarModels_[i].getName(),\n          'id': this.argumentVarModels_[i].getId(),\n        });\n      }\n    }\n    if (!this.hasStatements_) {\n      state['hasStatements'] = false;\n    }\n    return state;\n  },\n  /**\n   * Applies the given state to this block.\n   *\n   * @param state The state to apply to this block, eg the parameters\n   *     and statements.\n   */\n  loadExtraState: function (this: ProcedureBlock, state: ProcedureExtraState) {\n    this.arguments_ = [];\n    this.argumentVarModels_ = [];\n    if (state['params']) {\n      for (let i = 0; i < state['params'].length; i++) {\n        const param = state['params'][i];\n        const variable = Variables.getOrCreateVariablePackage(\n          this.workspace,\n          param['id'],\n          param['name'],\n          '',\n        );\n        this.arguments_.push(variable.getName());\n        this.argumentVarModels_.push(variable);\n      }\n    }\n    this.updateParams_();\n    Procedures.mutateCallers(this);\n    this.setStatements_(state['hasStatements'] === false ? false : true);\n  },\n  /**\n   * Populate the mutator's dialog with this block's components.\n   *\n   * @param  workspace Mutator's workspace.\n   * @returns Root block in mutator.\n   */\n  decompose: function (\n    this: ProcedureBlock,\n    workspace: Workspace,\n  ): ContainerBlock {\n    /*\n     * Creates the following XML:\n     * <block type=\"procedures_mutatorcontainer\">\n     *   <statement name=\"STACK\">\n     *     <block type=\"procedures_mutatorarg\">\n     *       <field name=\"NAME\">arg1_name</field>\n     *       <next>etc...</next>\n     *     </block>\n     *   </statement>\n     * </block>\n     */\n\n    const containerBlockNode = xmlUtils.createElement('block');\n    containerBlockNode.setAttribute('type', 'procedures_mutatorcontainer');\n    const statementNode = xmlUtils.createElement('statement');\n    statementNode.setAttribute('name', 'STACK');\n    containerBlockNode.appendChild(statementNode);\n\n    let node = statementNode;\n    for (let i = 0; i < this.arguments_.length; i++) {\n      const argBlockNode = xmlUtils.createElement('block');\n      argBlockNode.setAttribute('type', 'procedures_mutatorarg');\n      const fieldNode = xmlUtils.createElement('field');\n      fieldNode.setAttribute('name', 'NAME');\n      const argumentName = xmlUtils.createTextNode(this.arguments_[i]);\n      fieldNode.appendChild(argumentName);\n      argBlockNode.appendChild(fieldNode);\n      const nextNode = xmlUtils.createElement('next');\n      argBlockNode.appendChild(nextNode);\n\n      node.appendChild(argBlockNode);\n      node = nextNode;\n    }\n\n    const containerBlock = Xml.domToBlock(\n      containerBlockNode,\n      workspace,\n    ) as ContainerBlock;\n\n    if (this.type === 'procedures_defreturn') {\n      containerBlock.setFieldValue(this.hasStatements_, 'STATEMENTS');\n    } else {\n      containerBlock.removeInput('STATEMENT_INPUT');\n    }\n\n    // Initialize procedure's callers with blank IDs.\n    Procedures.mutateCallers(this);\n    return containerBlock;\n  },\n  /**\n   * Reconfigure this block based on the mutator dialog's components.\n   *\n   * @param containerBlock Root block in mutator.\n   */\n  compose: function (this: ProcedureBlock, containerBlock: ContainerBlock) {\n    // Parameter list.\n    this.arguments_ = [];\n    this.paramIds_ = [];\n    this.argumentVarModels_ = [];\n    let paramBlock = containerBlock.getInputTargetBlock('STACK');\n    while (paramBlock && !paramBlock.isInsertionMarker()) {\n      const varName = paramBlock.getFieldValue('NAME');\n      this.arguments_.push(varName);\n      const variable = this.workspace.getVariable(varName, '')!;\n      this.argumentVarModels_.push(variable);\n\n      this.paramIds_.push(paramBlock.id);\n      paramBlock =\n        paramBlock.nextConnection && paramBlock.nextConnection.targetBlock();\n    }\n    this.updateParams_();\n    Procedures.mutateCallers(this);\n\n    // Show/hide the statement input.\n    let hasStatements = containerBlock.getFieldValue('STATEMENTS');\n    if (hasStatements !== null) {\n      hasStatements = hasStatements === 'TRUE';\n      if (this.hasStatements_ !== hasStatements) {\n        if (hasStatements) {\n          this.setStatements_(true);\n          // Restore the stack, if one was saved.\n          this.statementConnection_?.reconnect(this, 'STACK');\n          this.statementConnection_ = null;\n        } else {\n          // Save the stack, then disconnect it.\n          const stackConnection = this.getInput('STACK')!.connection;\n          this.statementConnection_ = stackConnection!.targetConnection;\n          if (this.statementConnection_) {\n            const stackBlock = stackConnection!.targetBlock()!;\n            stackBlock.unplug();\n            stackBlock.bumpNeighbours();\n          }\n          this.setStatements_(false);\n        }\n      }\n    }\n  },\n  /**\n   * Return all variables referenced by this block.\n   *\n   * @returns List of variable names.\n   */\n  getVars: function (this: ProcedureBlock): string[] {\n    return this.arguments_;\n  },\n  /**\n   * Return all variables referenced by this block.\n   *\n   * @returns List of variable models.\n   */\n  getVarModels: function (\n    this: ProcedureBlock,\n  ): IVariableModel<IVariableState>[] {\n    return this.argumentVarModels_;\n  },\n  /**\n   * Notification that a variable is renaming.\n   * If the ID matches one of this block's variables, rename it.\n   *\n   * @param oldId ID of variable to rename.\n   * @param newId ID of new variable.  May be the same as oldId, but\n   *     with an updated name.  Guaranteed to be the same type as the\n   *     old variable.\n   */\n  renameVarById: function (\n    this: ProcedureBlock & BlockSvg,\n    oldId: string,\n    newId: string,\n  ) {\n    const oldVariable = this.workspace.getVariableById(oldId)!;\n    if (oldVariable.getType() !== '') {\n      // Procedure arguments always have the empty type.\n      return;\n    }\n    const oldName = oldVariable.getName();\n    const newVar = this.workspace.getVariableById(newId)!;\n\n    let change = false;\n    for (let i = 0; i < this.argumentVarModels_.length; i++) {\n      if (this.argumentVarModels_[i].getId() === oldId) {\n        this.arguments_[i] = newVar.getName();\n        this.argumentVarModels_[i] = newVar;\n        change = true;\n      }\n    }\n    if (change) {\n      this.displayRenamedVar_(oldName, newVar.getName());\n      Procedures.mutateCallers(this);\n    }\n  },\n  /**\n   * Notification that a variable is renaming but keeping the same ID.  If the\n   * variable is in use on this block, rerender to show the new name.\n   *\n   * @param variable The variable being renamed.\n   */\n  updateVarName: function (\n    this: ProcedureBlock & BlockSvg,\n    variable: IVariableModel<IVariableState>,\n  ) {\n    const newName = variable.getName();\n    let change = false;\n    let oldName;\n    for (let i = 0; i < this.argumentVarModels_.length; i++) {\n      if (this.argumentVarModels_[i].getId() === variable.getId()) {\n        oldName = this.arguments_[i];\n        this.arguments_[i] = newName;\n        change = true;\n      }\n    }\n    if (change) {\n      this.displayRenamedVar_(oldName as string, newName);\n      Procedures.mutateCallers(this);\n    }\n  },\n  /**\n   * Update the display to reflect a newly renamed argument.\n   *\n   * @internal\n   * @param oldName The old display name of the argument.\n   * @param newName The new display name of the argument.\n   */\n  displayRenamedVar_: function (\n    this: ProcedureBlock & BlockSvg,\n    oldName: string,\n    newName: string,\n  ) {\n    this.updateParams_();\n    // Update the mutator's variables if the mutator is open.\n    const mutator = this.getIcon(Mutator.TYPE);\n    if (mutator && mutator.bubbleIsVisible()) {\n      const blocks = mutator.getWorkspace()!.getAllBlocks(false);\n      for (let i = 0, block; (block = blocks[i]); i++) {\n        if (\n          block.type === 'procedures_mutatorarg' &&\n          Names.equals(oldName, block.getFieldValue('NAME'))\n        ) {\n          block.setFieldValue(newName, 'NAME');\n        }\n      }\n    }\n  },\n  /**\n   * Add custom menu options to this block's context menu.\n   *\n   * @param options List of menu options to add to.\n   */\n  customContextMenu: function (\n    this: ProcedureBlock,\n    options: Array<ContextMenuOption | LegacyContextMenuOption>,\n  ) {\n    if (this.isInFlyout) {\n      return;\n    }\n    // Add option to create caller.\n    const name = this.getFieldValue('NAME');\n    const callProcedureBlockState = {\n      type: (this as AnyDuringMigration).callType_,\n      extraState: {name: name, params: this.arguments_},\n    };\n    options.push({\n      enabled: true,\n      text: Msg['PROCEDURES_CREATE_DO'].replace('%1', name),\n      callback: ContextMenu.callbackFactory(this, callProcedureBlockState),\n    });\n\n    // Add options to create getters for each parameter.\n    if (!this.isCollapsed()) {\n      for (let i = 0; i < this.argumentVarModels_.length; i++) {\n        const argVar = this.argumentVarModels_[i];\n        const getVarBlockState = {\n          type: 'variables_get',\n          fields: {\n            VAR: {\n              name: argVar.getName(),\n              id: argVar.getId(),\n              type: argVar.getType(),\n            },\n          },\n        };\n        options.push({\n          enabled: true,\n          text: Msg['VARIABLES_SET_CREATE_GET'].replace('%1', argVar.getName()),\n          callback: ContextMenu.callbackFactory(this, getVarBlockState),\n        });\n      }\n    }\n  },\n};\n\nblocks['procedures_defnoreturn'] = {\n  ...PROCEDURE_DEF_COMMON,\n  /**\n   * Block for defining a procedure with no return value.\n   */\n  init: function (this: ProcedureBlock & BlockSvg) {\n    const initName = Procedures.findLegalName('', this);\n    const nameField = fieldRegistry.fromJson({\n      type: 'field_input',\n      text: initName,\n    }) as FieldTextInput;\n    nameField!.setValidator(Procedures.rename);\n    nameField.setSpellcheck(false);\n    this.appendDummyInput()\n      .appendField(Msg['PROCEDURES_DEFNORETURN_TITLE'])\n      .appendField(nameField, 'NAME')\n      .appendField('', 'PARAMS');\n    this.setMutator(new Mutator(['procedures_mutatorarg'], this));\n    if (\n      (this.workspace.options.comments ||\n        (this.workspace.options.parentWorkspace &&\n          this.workspace.options.parentWorkspace.options.comments)) &&\n      Msg['PROCEDURES_DEFNORETURN_COMMENT']\n    ) {\n      this.setCommentText(Msg['PROCEDURES_DEFNORETURN_COMMENT']);\n    }\n    this.setStyle('procedure_blocks');\n    this.setTooltip(Msg['PROCEDURES_DEFNORETURN_TOOLTIP']);\n    this.setHelpUrl(Msg['PROCEDURES_DEFNORETURN_HELPURL']);\n    this.arguments_ = [];\n    this.argumentVarModels_ = [];\n    this.setStatements_(true);\n    this.statementConnection_ = null;\n  },\n  /**\n   * Return the signature of this procedure definition.\n   *\n   * @returns Tuple containing three elements:\n   *     - the name of the defined procedure,\n   *     - a list of all its arguments,\n   *     - that it DOES NOT have a return value.\n   */\n  getProcedureDef: function (this: ProcedureBlock): [string, string[], false] {\n    return [this.getFieldValue('NAME'), this.arguments_, false];\n  },\n  callType_: 'procedures_callnoreturn',\n};\n\nblocks['procedures_defreturn'] = {\n  ...PROCEDURE_DEF_COMMON,\n  /**\n   * Block for defining a procedure with a return value.\n   */\n  init: function (this: ProcedureBlock & BlockSvg) {\n    const initName = Procedures.findLegalName('', this);\n    const nameField = fieldRegistry.fromJson({\n      type: 'field_input',\n      text: initName,\n    }) as FieldTextInput;\n    nameField.setValidator(Procedures.rename);\n    nameField.setSpellcheck(false);\n    this.appendDummyInput()\n      .appendField(Msg['PROCEDURES_DEFRETURN_TITLE'])\n      .appendField(nameField, 'NAME')\n      .appendField('', 'PARAMS');\n    this.appendValueInput('RETURN')\n      .setAlign(Align.RIGHT)\n      .appendField(Msg['PROCEDURES_DEFRETURN_RETURN']);\n    this.setMutator(new Mutator(['procedures_mutatorarg'], this));\n    if (\n      (this.workspace.options.comments ||\n        (this.workspace.options.parentWorkspace &&\n          this.workspace.options.parentWorkspace.options.comments)) &&\n      Msg['PROCEDURES_DEFRETURN_COMMENT']\n    ) {\n      this.setCommentText(Msg['PROCEDURES_DEFRETURN_COMMENT']);\n    }\n    this.setStyle('procedure_blocks');\n    this.setTooltip(Msg['PROCEDURES_DEFRETURN_TOOLTIP']);\n    this.setHelpUrl(Msg['PROCEDURES_DEFRETURN_HELPURL']);\n    this.arguments_ = [];\n    this.argumentVarModels_ = [];\n    this.setStatements_(true);\n    this.statementConnection_ = null;\n  },\n  /**\n   * Return the signature of this procedure definition.\n   *\n   * @returns Tuple containing three elements:\n   *     - the name of the defined procedure,\n   *     - a list of all its arguments,\n   *     - that it DOES have a return value.\n   */\n  getProcedureDef: function (this: ProcedureBlock): [string, string[], true] {\n    return [this.getFieldValue('NAME'), this.arguments_, true];\n  },\n  callType_: 'procedures_callreturn',\n};\n\n/** Type of a procedures_mutatorcontainer block. */\ntype ContainerBlock = Block & ContainerMixin;\ninterface ContainerMixin extends ContainerMixinType {}\ntype ContainerMixinType = typeof PROCEDURES_MUTATORCONTAINER;\n\nconst PROCEDURES_MUTATORCONTAINER = {\n  /**\n   * Mutator block for procedure container.\n   */\n  init: function (this: ContainerBlock) {\n    this.appendDummyInput().appendField(\n      Msg['PROCEDURES_MUTATORCONTAINER_TITLE'],\n    );\n    this.appendStatementInput('STACK');\n    this.appendDummyInput('STATEMENT_INPUT')\n      .appendField(Msg['PROCEDURES_ALLOW_STATEMENTS'])\n      .appendField(\n        fieldRegistry.fromJson({\n          type: 'field_checkbox',\n          checked: true,\n        }) as FieldCheckbox,\n        'STATEMENTS',\n      );\n    this.setStyle('procedure_blocks');\n    this.setTooltip(Msg['PROCEDURES_MUTATORCONTAINER_TOOLTIP']);\n    this.contextMenu = false;\n  },\n};\nblocks['procedures_mutatorcontainer'] = PROCEDURES_MUTATORCONTAINER;\n\n/** Type of a procedures_mutatorarg block. */\ntype ArgumentBlock = Block & ArgumentMixin;\ninterface ArgumentMixin extends ArgumentMixinType {}\ntype ArgumentMixinType = typeof PROCEDURES_MUTATORARGUMENT;\n\n/**\n * Field responsible for editing procedure argument names.\n */\nclass ProcedureArgumentField extends FieldTextInput {\n  /**\n   * Whether or not this field is currently being edited interactively.\n   */\n  editingInteractively = false;\n\n  /**\n   * The procedure argument variable whose name is being interactively edited.\n   */\n  editingVariable?: IVariableModel<IVariableState>;\n\n  /**\n   * Displays the field editor.\n   *\n   * @param e The event that triggered display of the field editor.\n   */\n  protected override showEditor_(e?: Event) {\n    super.showEditor_(e);\n    this.editingInteractively = true;\n    this.editingVariable = undefined;\n  }\n\n  /**\n   * Handles cleanup when the field editor is dismissed.\n   */\n  override onFinishEditing_(value: string) {\n    super.onFinishEditing_(value);\n    this.editingInteractively = false;\n  }\n}\n\nconst PROCEDURES_MUTATORARGUMENT = {\n  /**\n   * Mutator block for procedure argument.\n   */\n  init: function (this: ArgumentBlock) {\n    const field = new ProcedureArgumentField(\n      Procedures.DEFAULT_ARG,\n      this.validator_,\n    );\n\n    this.appendDummyInput()\n      .appendField(Msg['PROCEDURES_MUTATORARG_TITLE'])\n      .appendField(field, 'NAME');\n    this.setPreviousStatement(true);\n    this.setNextStatement(true);\n    this.setStyle('procedure_blocks');\n    this.setTooltip(Msg['PROCEDURES_MUTATORARG_TOOLTIP']);\n    this.contextMenu = false;\n  },\n\n  /**\n   * Obtain a valid name for the procedure argument. Create a variable if\n   * necessary.\n   * Merge runs of whitespace.  Strip leading and trailing whitespace.\n   * Beyond this, all names are legal.\n   *\n   * @internal\n   * @param varName User-supplied name.\n   * @returns Valid name, or null if a name was not specified.\n   */\n  validator_: function (\n    this: ProcedureArgumentField,\n    varName: string,\n  ): string | null {\n    const sourceBlock = this.getSourceBlock()!;\n    const outerWs = sourceBlock.workspace.getRootWorkspace()!;\n    varName = varName.replace(/[\\s\\xa0]+/g, ' ').replace(/^ | $/g, '');\n    if (!varName) {\n      return null;\n    }\n\n    // Prevents duplicate parameter names in functions\n    const workspace =\n      (sourceBlock.workspace as WorkspaceSvg).targetWorkspace ||\n      sourceBlock.workspace;\n    const blocks = workspace.getAllBlocks(false);\n    const caselessName = varName.toLowerCase();\n    for (let i = 0; i < blocks.length; i++) {\n      if (blocks[i].id === this.getSourceBlock()!.id) {\n        continue;\n      }\n      // Other blocks values may not be set yet when this is loaded.\n      const otherVar = blocks[i].getFieldValue('NAME');\n      if (otherVar && otherVar.toLowerCase() === caselessName) {\n        return null;\n      }\n    }\n\n    // Don't create variables for arg blocks that\n    // only exist in the mutator's flyout.\n    if (sourceBlock.isInFlyout) {\n      return varName;\n    }\n\n    const model = outerWs.getVariable(varName, '');\n    if (model && model.getName() !== varName) {\n      // Rename the variable (case change)\n      outerWs.renameVariableById(model.getId(), varName);\n    }\n    if (!model) {\n      if (this.editingInteractively) {\n        if (!this.editingVariable) {\n          this.editingVariable = outerWs.createVariable(varName, '');\n        } else {\n          outerWs.renameVariableById(this.editingVariable.getId(), varName);\n        }\n      } else {\n        outerWs.createVariable(varName, '');\n      }\n    }\n    return varName;\n  },\n};\nblocks['procedures_mutatorarg'] = PROCEDURES_MUTATORARGUMENT;\n\n/** Type of a block using the PROCEDURE_CALL_COMMON mixin. */\ntype CallBlock = Block & CallMixin;\ninterface CallMixin extends CallMixinType {\n  argumentVarModels_: IVariableModel<IVariableState>[];\n  arguments_: string[];\n  defType_: string;\n  quarkIds_: string[] | null;\n  quarkConnections_: {[id: string]: Connection};\n}\ntype CallMixinType = typeof PROCEDURE_CALL_COMMON;\n\n/** Extra state for serialising call blocks. */\ntype CallExtraState = {\n  name: string;\n  params?: string[];\n};\n\n/**\n * The language-neutral ID for when the reason why a block is disabled is\n * because the block's corresponding procedure definition is disabled.\n */\nconst DISABLED_PROCEDURE_DEFINITION_DISABLED_REASON =\n  'DISABLED_PROCEDURE_DEFINITION';\n\n/**\n * Common properties for the procedure_callnoreturn and\n * procedure_callreturn blocks.\n */\nconst PROCEDURE_CALL_COMMON = {\n  /**\n   * Returns the name of the procedure this block calls.\n   *\n   * @returns Procedure name.\n   */\n  getProcedureCall: function (this: CallBlock): string {\n    // The NAME field is guaranteed to exist, null will never be returned.\n    return this.getFieldValue('NAME');\n  },\n  /**\n   * Notification that a procedure is renaming.\n   * If the name matches this block's procedure, rename it.\n   *\n   * @param oldName Previous name of procedure.\n   * @param newName Renamed procedure.\n   */\n  renameProcedure: function (\n    this: CallBlock,\n    oldName: string,\n    newName: string,\n  ) {\n    if (Names.equals(oldName, this.getProcedureCall())) {\n      this.setFieldValue(newName, 'NAME');\n      const baseMsg = this.outputConnection\n        ? Msg['PROCEDURES_CALLRETURN_TOOLTIP']\n        : Msg['PROCEDURES_CALLNORETURN_TOOLTIP'];\n      this.setTooltip(baseMsg.replace('%1', newName));\n    }\n  },\n  /**\n   * Notification that the procedure's parameters have changed.\n   *\n   * @internal\n   * @param paramNames New param names, e.g. ['x', 'y', 'z'].\n   * @param paramIds IDs of params (consistent for each parameter\n   *     through the life of a mutator, regardless of param renaming),\n   *     e.g. ['piua', 'f8b_', 'oi.o'].\n   */\n  setProcedureParameters_: function (\n    this: CallBlock,\n    paramNames: string[],\n    paramIds: string[],\n  ) {\n    // Data structures:\n    // this.arguments = ['x', 'y']\n    //     Existing param names.\n    // this.quarkConnections_ {piua: null, f8b_: Connection}\n    //     Look-up of paramIds to connections plugged into the call block.\n    // this.quarkIds_ = ['piua', 'f8b_']\n    //     Existing param IDs.\n    // Note that quarkConnections_ may include IDs that no longer exist, but\n    // which might reappear if a param is reattached in the mutator.\n    const defBlock = Procedures.getDefinition(\n      this.getProcedureCall(),\n      this.workspace,\n    );\n    const mutatorIcon = defBlock && defBlock.getIcon(Mutator.TYPE);\n    const mutatorOpen = mutatorIcon && mutatorIcon.bubbleIsVisible();\n    if (!mutatorOpen) {\n      this.quarkConnections_ = {};\n      this.quarkIds_ = null;\n    } else {\n      // fix #6091 - this call could cause an error when outside if-else\n      // expanding block while mutating prevents another error (ancient fix)\n      this.setCollapsed(false);\n    }\n    // Test arguments (arrays of strings) for changes. '\\n' is not a valid\n    // argument name character, so it is a valid delimiter here.\n    if (paramNames.join('\\n') === this.arguments_.join('\\n')) {\n      // No change.\n      this.quarkIds_ = paramIds;\n      return;\n    }\n    if (paramIds.length !== paramNames.length) {\n      throw RangeError('paramNames and paramIds must be the same length.');\n    }\n    if (!this.quarkIds_) {\n      // Initialize tracking for this block.\n      this.quarkConnections_ = {};\n      this.quarkIds_ = [];\n    }\n    // Update the quarkConnections_ with existing connections.\n    for (let i = 0; i < this.arguments_.length; i++) {\n      const input = this.getInput('ARG' + i);\n      if (input) {\n        const connection = input.connection!.targetConnection!;\n        this.quarkConnections_[this.quarkIds_[i]] = connection;\n        if (\n          mutatorOpen &&\n          connection &&\n          !paramIds.includes(this.quarkIds_[i])\n        ) {\n          // This connection should no longer be attached to this block.\n          connection.disconnect();\n          connection.getSourceBlock().bumpNeighbours();\n        }\n      }\n    }\n    // Rebuild the block's arguments.\n    this.arguments_ = ([] as string[]).concat(paramNames);\n    // And rebuild the argument model list.\n    this.argumentVarModels_ = [];\n    for (let i = 0; i < this.arguments_.length; i++) {\n      const variable = Variables.getOrCreateVariablePackage(\n        this.workspace,\n        null,\n        this.arguments_[i],\n        '',\n      );\n      this.argumentVarModels_.push(variable);\n    }\n\n    this.updateShape_();\n    this.quarkIds_ = paramIds;\n    // Reconnect any child blocks.\n    if (this.quarkIds_) {\n      for (let i = 0; i < this.arguments_.length; i++) {\n        const quarkId: string = this.quarkIds_[i]; // TODO(#6920)\n        if (quarkId in this.quarkConnections_) {\n          // TODO(#6920): investigate claimed circular initialisers.\n          const connection: Connection = this.quarkConnections_[quarkId];\n          if (!connection?.reconnect(this, 'ARG' + i)) {\n            // Block no longer exists or has been attached elsewhere.\n            delete this.quarkConnections_[quarkId];\n          }\n        }\n      }\n    }\n  },\n  /**\n   * Modify this block to have the correct number of arguments.\n   *\n   * @internal\n   */\n  updateShape_: function (this: CallBlock) {\n    for (let i = 0; i < this.arguments_.length; i++) {\n      const argField = this.getField('ARGNAME' + i);\n      if (argField) {\n        // Ensure argument name is up to date.\n        // The argument name field is deterministic based on the mutation,\n        // no need to fire a change event.\n        Events.disable();\n        try {\n          argField.setValue(this.arguments_[i]);\n        } finally {\n          Events.enable();\n        }\n      } else {\n        // Add new input.\n        const newField = fieldRegistry.fromJson({\n          type: 'field_label',\n          text: this.arguments_[i],\n        }) as FieldLabel;\n        this.appendValueInput('ARG' + i)\n          .setAlign(Align.RIGHT)\n          .appendField(newField, 'ARGNAME' + i);\n      }\n    }\n    // Remove deleted inputs.\n    for (let i = this.arguments_.length; this.getInput('ARG' + i); i++) {\n      this.removeInput('ARG' + i);\n    }\n    // Add 'with:' if there are parameters, remove otherwise.\n    const topRow = this.getInput('TOPROW');\n    if (topRow) {\n      if (this.arguments_.length) {\n        if (!this.getField('WITH')) {\n          topRow.appendField(Msg['PROCEDURES_CALL_BEFORE_PARAMS'], 'WITH');\n        }\n      } else {\n        if (this.getField('WITH')) {\n          topRow.removeField('WITH');\n        }\n      }\n    }\n  },\n  /**\n   * Create XML to represent the (non-editable) name and arguments.\n   * Backwards compatible serialization implementation.\n   *\n   * @returns XML storage element.\n   */\n  mutationToDom: function (this: CallBlock): Element {\n    const container = xmlUtils.createElement('mutation');\n    container.setAttribute('name', this.getProcedureCall());\n    for (let i = 0; i < this.arguments_.length; i++) {\n      const parameter = xmlUtils.createElement('arg');\n      parameter.setAttribute('name', this.arguments_[i]);\n      container.appendChild(parameter);\n    }\n    return container;\n  },\n  /**\n   * Parse XML to restore the (non-editable) name and parameters.\n   * Backwards compatible serialization implementation.\n   *\n   * @param xmlElement XML storage element.\n   */\n  domToMutation: function (this: CallBlock, xmlElement: Element) {\n    const name = xmlElement.getAttribute('name')!;\n    this.renameProcedure(this.getProcedureCall(), name);\n    const args: string[] = [];\n    const paramIds = [];\n    for (let i = 0, childNode; (childNode = xmlElement.childNodes[i]); i++) {\n      if (childNode.nodeName.toLowerCase() === 'arg') {\n        args.push((childNode as Element).getAttribute('name')!);\n        paramIds.push((childNode as Element).getAttribute('paramId')!);\n      }\n    }\n    this.setProcedureParameters_(args, paramIds);\n  },\n  /**\n   * Returns the state of this block as a JSON serializable object.\n   *\n   * @returns The state of this block, ie the params and procedure name.\n   */\n  saveExtraState: function (this: CallBlock): CallExtraState {\n    const state = Object.create(null);\n    state['name'] = this.getProcedureCall();\n    if (this.arguments_.length) {\n      state['params'] = this.arguments_;\n    }\n    return state;\n  },\n  /**\n   * Applies the given state to this block.\n   *\n   * @param state The state to apply to this block, ie the params and\n   *     procedure name.\n   */\n  loadExtraState: function (this: CallBlock, state: CallExtraState) {\n    this.renameProcedure(this.getProcedureCall(), state['name']);\n    const params = state['params'];\n    if (params) {\n      const ids: string[] = [];\n      ids.length = params.length;\n      ids.fill(null as unknown as string); // TODO(#6920)\n      this.setProcedureParameters_(params, ids);\n    }\n  },\n  /**\n   * Return all variables referenced by this block.\n   *\n   * @returns List of variable names.\n   */\n  getVars: function (this: CallBlock): string[] {\n    return this.arguments_;\n  },\n  /**\n   * Return all variables referenced by this block.\n   *\n   * @returns List of variable models.\n   */\n  getVarModels: function (this: CallBlock): IVariableModel<IVariableState>[] {\n    return this.argumentVarModels_;\n  },\n  /**\n   * Procedure calls cannot exist without the corresponding procedure\n   * definition.  Enforce this link whenever an event is fired.\n   *\n   * @param event Change event.\n   */\n  onchange: function (this: CallBlock, event: AbstractEvent) {\n    if (!this.workspace || this.workspace.isFlyout) {\n      // Block is deleted or is in a flyout.\n      return;\n    }\n    if (!event.recordUndo) {\n      // Events not generated by user. Skip handling.\n      return;\n    }\n    if (\n      event.type === Events.BLOCK_CREATE &&\n      (event as BlockCreate).ids!.includes(this.id)\n    ) {\n      // Look for the case where a procedure call was created (usually through\n      // paste) and there is no matching definition.  In this case, create\n      // an empty definition block with the correct signature.\n      const name = this.getProcedureCall();\n      let def = Procedures.getDefinition(name, this.workspace);\n      if (\n        def &&\n        (def.type !== this.defType_ ||\n          JSON.stringify(def.getVars()) !== JSON.stringify(this.arguments_))\n      ) {\n        // The signatures don't match.\n        def = null;\n      }\n      if (!def) {\n        Events.setGroup(event.group);\n        /**\n         * Create matching definition block.\n         * <xml xmlns=\"https://developers.google.com/blockly/xml\">\n         *   <block type=\"procedures_defreturn\" x=\"10\" y=\"20\">\n         *     <mutation name=\"test\">\n         *       <arg name=\"x\"></arg>\n         *     </mutation>\n         *     <field name=\"NAME\">test</field>\n         *   </block>\n         * </xml>\n         */\n        const xml = xmlUtils.createElement('xml');\n        const block = xmlUtils.createElement('block');\n        block.setAttribute('type', this.defType_);\n        const xy = this.getRelativeToSurfaceXY();\n        const x = xy.x + config.snapRadius * (this.RTL ? -1 : 1);\n        const y = xy.y + config.snapRadius * 2;\n        block.setAttribute('x', `${x}`);\n        block.setAttribute('y', `${y}`);\n        const mutation = this.mutationToDom();\n        block.appendChild(mutation);\n        const field = xmlUtils.createElement('field');\n        field.setAttribute('name', 'NAME');\n        const callName = this.getProcedureCall();\n        const newName = Procedures.findLegalName(callName, this);\n        if (callName !== newName) {\n          this.renameProcedure(callName, newName);\n        }\n        field.appendChild(xmlUtils.createTextNode(callName));\n        block.appendChild(field);\n        xml.appendChild(block);\n        Xml.domToWorkspace(xml, this.workspace);\n        Events.setGroup(false);\n      } else if (!def.isEnabled()) {\n        this.setDisabledReason(\n          true,\n          DISABLED_PROCEDURE_DEFINITION_DISABLED_REASON,\n        );\n        this.setWarningText(\n          Msg['PROCEDURES_CALL_DISABLED_DEF_WARNING'].replace('%1', name),\n        );\n      }\n    } else if (event.type === Events.BLOCK_DELETE) {\n      // Look for the case where a procedure definition has been deleted,\n      // leaving this block (a procedure call) orphaned.  In this case, delete\n      // the orphan.\n      const name = this.getProcedureCall();\n      const def = Procedures.getDefinition(name, this.workspace);\n      if (!def) {\n        Events.setGroup(event.group);\n        this.dispose(true);\n        Events.setGroup(false);\n      }\n    } else if (\n      event.type === Events.BLOCK_CHANGE &&\n      (event as BlockChange).element === 'disabled'\n    ) {\n      const blockChangeEvent = event as BlockChange;\n      const name = this.getProcedureCall();\n      const def = Procedures.getDefinition(name, this.workspace);\n      if (def && def.id === blockChangeEvent.blockId) {\n        // in most cases the old group should be ''\n        const oldGroup = Events.getGroup();\n        if (oldGroup) {\n          // This should only be possible programmatically and may indicate a\n          // problem with event grouping. If you see this message please\n          // investigate. If the use ends up being valid we may need to reorder\n          // events in the undo stack.\n          console.log(\n            'Saw an existing group while responding to a definition change',\n          );\n        }\n        Events.setGroup(event.group);\n        const valid = def.isEnabled();\n        this.setDisabledReason(\n          !valid,\n          DISABLED_PROCEDURE_DEFINITION_DISABLED_REASON,\n        );\n        this.setWarningText(\n          valid\n            ? null\n            : Msg['PROCEDURES_CALL_DISABLED_DEF_WARNING'].replace('%1', name),\n        );\n        Events.setGroup(oldGroup);\n      }\n    }\n  },\n  /**\n   * Add menu option to find the definition block for this call.\n   *\n   * @param options List of menu options to add to.\n   */\n  customContextMenu: function (\n    this: CallBlock,\n    options: Array<ContextMenuOption | LegacyContextMenuOption>,\n  ) {\n    if (!(this.workspace as WorkspaceSvg).isMovable()) {\n      // If we center on the block and the workspace isn't movable we could\n      // loose blocks at the edges of the workspace.\n      return;\n    }\n\n    const name = this.getProcedureCall();\n    const workspace = this.workspace;\n    options.push({\n      enabled: true,\n      text: Msg['PROCEDURES_HIGHLIGHT_DEF'],\n      callback: function () {\n        const def = Procedures.getDefinition(name, workspace);\n        if (def) {\n          (workspace as WorkspaceSvg).centerOnBlock(def.id);\n          getFocusManager().focusNode(def as BlockSvg);\n        }\n      },\n    });\n  },\n};\n\nblocks['procedures_callnoreturn'] = {\n  ...PROCEDURE_CALL_COMMON,\n  /**\n   * Block for calling a procedure with no return value.\n   */\n  init: function (this: CallBlock) {\n    this.appendDummyInput('TOPROW').appendField('', 'NAME');\n    this.setPreviousStatement(true);\n    this.setNextStatement(true);\n    this.setStyle('procedure_blocks');\n    // Tooltip is set in renameProcedure.\n    this.setHelpUrl(Msg['PROCEDURES_CALLNORETURN_HELPURL']);\n    this.arguments_ = [];\n    this.argumentVarModels_ = [];\n    this.quarkConnections_ = {};\n    this.quarkIds_ = null;\n  },\n\n  defType_: 'procedures_defnoreturn',\n};\n\nblocks['procedures_callreturn'] = {\n  ...PROCEDURE_CALL_COMMON,\n  /**\n   * Block for calling a procedure with a return value.\n   */\n  init: function (this: CallBlock) {\n    this.appendDummyInput('TOPROW').appendField('', 'NAME');\n    this.setOutput(true);\n    this.setStyle('procedure_blocks');\n    // Tooltip is set in renameProcedure.\n    this.setHelpUrl(Msg['PROCEDURES_CALLRETURN_HELPURL']);\n    this.arguments_ = [];\n    this.argumentVarModels_ = [];\n    this.quarkConnections_ = {};\n    this.quarkIds_ = null;\n  },\n\n  defType_: 'procedures_defreturn',\n};\n\n/**\n * Type of a procedures_ifreturn block.\n *\n * @internal\n */\nexport type IfReturnBlock = Block & IfReturnMixin;\ninterface IfReturnMixin extends IfReturnMixinType {\n  hasReturnValue_: boolean;\n}\ntype IfReturnMixinType = typeof PROCEDURES_IFRETURN;\n\n/**\n * The language-neutral ID for when the reason why a block is disabled is\n * because the block is only valid inside of a procedure body.\n */\nconst UNPARENTED_IFRETURN_DISABLED_REASON = 'UNPARENTED_IFRETURN';\n\nconst PROCEDURES_IFRETURN = {\n  /**\n   * Block for conditionally returning a value from a procedure.\n   */\n  init: function (this: IfReturnBlock) {\n    this.appendValueInput('CONDITION')\n      .setCheck('Boolean')\n      .appendField(Msg['CONTROLS_IF_MSG_IF']);\n    this.appendValueInput('VALUE').appendField(\n      Msg['PROCEDURES_DEFRETURN_RETURN'],\n    );\n    this.setInputsInline(true);\n    this.setPreviousStatement(true);\n    this.setNextStatement(true);\n    this.setStyle('procedure_blocks');\n    this.setTooltip(Msg['PROCEDURES_IFRETURN_TOOLTIP']);\n    this.setHelpUrl(Msg['PROCEDURES_IFRETURN_HELPURL']);\n    this.hasReturnValue_ = true;\n  },\n  /**\n   * Create XML to represent whether this block has a return value.\n   *\n   * @returns XML storage element.\n   */\n  mutationToDom: function (this: IfReturnBlock): Element {\n    const container = xmlUtils.createElement('mutation');\n    container.setAttribute('value', String(Number(this.hasReturnValue_)));\n    return container;\n  },\n  /**\n   * Parse XML to restore whether this block has a return value.\n   *\n   * @param xmlElement XML storage element.\n   */\n  domToMutation: function (this: IfReturnBlock, xmlElement: Element) {\n    const value = xmlElement.getAttribute('value');\n    this.hasReturnValue_ = value === '1';\n    if (!this.hasReturnValue_) {\n      this.removeInput('VALUE');\n      this.appendDummyInput('VALUE').appendField(\n        Msg['PROCEDURES_DEFRETURN_RETURN'],\n      );\n    }\n  },\n\n  // This block does not need JSO serialization hooks (saveExtraState and\n  // loadExtraState) because the state of this block is already encoded in the\n  // block's position in the workspace.\n  // XML hooks are kept for backwards compatibility.\n\n  /**\n   * Called whenever anything on the workspace changes.\n   * Add warning if this flow block is not nested inside a loop.\n   *\n   * @param e Move event.\n   */\n  onchange: function (this: IfReturnBlock, e: AbstractEvent) {\n    if (\n      ((this.workspace as WorkspaceSvg).isDragging &&\n        (this.workspace as WorkspaceSvg).isDragging()) ||\n      (e.type !== Events.BLOCK_MOVE && e.type !== Events.BLOCK_CREATE)\n    ) {\n      return; // Don't change state at the start of a drag.\n    }\n    let legal = false;\n    // Is the block nested in a procedure?\n    let block = this; // eslint-disable-line @typescript-eslint/no-this-alias\n    do {\n      if (this.FUNCTION_TYPES.includes(block.type)) {\n        legal = true;\n        break;\n      }\n      block = block.getSurroundParent()!;\n    } while (block);\n    if (legal) {\n      // If needed, toggle whether this block has a return value.\n      if (block.type === 'procedures_defnoreturn' && this.hasReturnValue_) {\n        this.removeInput('VALUE');\n        this.appendDummyInput('VALUE').appendField(\n          Msg['PROCEDURES_DEFRETURN_RETURN'],\n        );\n        this.hasReturnValue_ = false;\n      } else if (\n        block.type === 'procedures_defreturn' &&\n        !this.hasReturnValue_\n      ) {\n        this.removeInput('VALUE');\n        this.appendValueInput('VALUE').appendField(\n          Msg['PROCEDURES_DEFRETURN_RETURN'],\n        );\n        this.hasReturnValue_ = true;\n      }\n      this.setWarningText(null);\n    } else {\n      this.setWarningText(Msg['PROCEDURES_IFRETURN_WARNING']);\n    }\n\n    if (!this.isInFlyout) {\n      try {\n        // There is no need to record the enable/disable change on the undo/redo\n        // list since the change will be automatically recreated when replayed.\n        eventUtils.setRecordUndo(false);\n        this.setDisabledReason(!legal, UNPARENTED_IFRETURN_DISABLED_REASON);\n      } finally {\n        eventUtils.setRecordUndo(true);\n      }\n    }\n  },\n  /**\n   * List of block types that are functions and thus do not need warnings.\n   * To add a new function type add this to your code:\n   * Blocks['procedures_ifreturn'].FUNCTION_TYPES.push('custom_func');\n   */\n  FUNCTION_TYPES: ['procedures_defnoreturn', 'procedures_defreturn'],\n};\nblocks['procedures_ifreturn'] = PROCEDURES_IFRETURN;\n\n// Register provided blocks.\ndefineBlocks(blocks);\n", "/**\n * @license\n * Copyright 2012 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n// Former goog.module ID: Blockly.libraryBlocks.math\n\nimport type {Block} from '../core/block.js';\nimport {\n  createBlockDefinitionsFromJsonArray,\n  defineBlocks,\n} from '../core/common.js';\nimport * as Extensions from '../core/extensions.js';\nimport '../core/field_dropdown.js';\nimport type {FieldDropdown} from '../core/field_dropdown.js';\nimport '../core/field_label.js';\nimport '../core/field_number.js';\nimport '../core/field_variable.js';\nimport * as xmlUtils from '../core/utils/xml.js';\n\n/**\n * A dictionary of the block definitions provided by this module.\n */\nexport const blocks = createBlockDefinitionsFromJsonArray([\n  // Block for numeric value.\n  {\n    'type': 'math_number',\n    'message0': '%1',\n    'args0': [\n      {\n        'type': 'field_number',\n        'name': 'NUM',\n        'value': 0,\n      },\n    ],\n    'output': 'Number',\n    'helpUrl': '%{BKY_MATH_NUMBER_HELPURL}',\n    'style': 'math_blocks',\n    'tooltip': '%{BKY_MATH_NUMBER_TOOLTIP}',\n    'extensions': ['parent_tooltip_when_inline'],\n  },\n\n  // Block for basic arithmetic operator.\n  {\n    'type': 'math_arithmetic',\n    'message0': '%1 %2 %3',\n    'args0': [\n      {\n        'type': 'input_value',\n        'name': 'A',\n        'check': 'Number',\n      },\n      {\n        'type': 'field_dropdown',\n        'name': 'OP',\n        'options': [\n          ['%{BKY_MATH_ADDITION_SYMBOL}', 'ADD'],\n          ['%{BKY_MATH_SUBTRACTION_SYMBOL}', 'MINUS'],\n          ['%{BKY_MATH_MULTIPLICATION_SYMBOL}', 'MULTIPLY'],\n          ['%{BKY_MATH_DIVISION_SYMBOL}', 'DIVIDE'],\n          ['%{BKY_MATH_POWER_SYMBOL}', 'POWER'],\n        ],\n      },\n      {\n        'type': 'input_value',\n        'name': 'B',\n        'check': 'Number',\n      },\n    ],\n    'inputsInline': true,\n    'output': 'Number',\n    'style': 'math_blocks',\n    'helpUrl': '%{BKY_MATH_ARITHMETIC_HELPURL}',\n    'extensions': ['math_op_tooltip'],\n  },\n\n  // Block for advanced math operators with single operand.\n  {\n    'type': 'math_single',\n    'message0': '%1 %2',\n    'args0': [\n      {\n        'type': 'field_dropdown',\n        'name': 'OP',\n        'options': [\n          ['%{BKY_MATH_SINGLE_OP_ROOT}', 'ROOT'],\n          ['%{BKY_MATH_SINGLE_OP_ABSOLUTE}', 'ABS'],\n          ['-', 'NEG'],\n          ['ln', 'LN'],\n          ['log10', 'LOG10'],\n          ['e^', 'EXP'],\n          ['10^', 'POW10'],\n        ],\n      },\n      {\n        'type': 'input_value',\n        'name': 'NUM',\n        'check': 'Number',\n      },\n    ],\n    'output': 'Number',\n    'style': 'math_blocks',\n    'helpUrl': '%{BKY_MATH_SINGLE_HELPURL}',\n    'extensions': ['math_op_tooltip'],\n  },\n\n  // Block for trigonometry operators.\n  {\n    'type': 'math_trig',\n    'message0': '%1 %2',\n    'args0': [\n      {\n        'type': 'field_dropdown',\n        'name': 'OP',\n        'options': [\n          ['%{BKY_MATH_TRIG_SIN}', 'SIN'],\n          ['%{BKY_MATH_TRIG_COS}', 'COS'],\n          ['%{BKY_MATH_TRIG_TAN}', 'TAN'],\n          ['%{BKY_MATH_TRIG_ASIN}', 'ASIN'],\n          ['%{BKY_MATH_TRIG_ACOS}', 'ACOS'],\n          ['%{BKY_MATH_TRIG_ATAN}', 'ATAN'],\n        ],\n      },\n      {\n        'type': 'input_value',\n        'name': 'NUM',\n        'check': 'Number',\n      },\n    ],\n    'output': 'Number',\n    'style': 'math_blocks',\n    'helpUrl': '%{BKY_MATH_TRIG_HELPURL}',\n    'extensions': ['math_op_tooltip'],\n  },\n\n  // Block for constants: PI, E, the Golden Ratio, sqrt(2), 1/sqrt(2), INFINITY.\n  {\n    'type': 'math_constant',\n    'message0': '%1',\n    'args0': [\n      {\n        'type': 'field_dropdown',\n        'name': 'CONSTANT',\n        'options': [\n          ['\\u03c0', 'PI'],\n          ['e', 'E'],\n          ['\\u03c6', 'GOLDEN_RATIO'],\n          ['sqrt(2)', 'SQRT2'],\n          ['sqrt(\\u00bd)', 'SQRT1_2'],\n          ['\\u221e', 'INFINITY'],\n        ],\n      },\n    ],\n    'output': 'Number',\n    'style': 'math_blocks',\n    'tooltip': '%{BKY_MATH_CONSTANT_TOOLTIP}',\n    'helpUrl': '%{BKY_MATH_CONSTANT_HELPURL}',\n  },\n\n  // Block for checking if a number is even, odd, prime, whole, positive,\n  // negative or if it is divisible by certain number.\n  {\n    'type': 'math_number_property',\n    'message0': '%1 %2',\n    'args0': [\n      {\n        'type': 'input_value',\n        'name': 'NUMBER_TO_CHECK',\n        'check': 'Number',\n      },\n      {\n        'type': 'field_dropdown',\n        'name': 'PROPERTY',\n        'options': [\n          ['%{BKY_MATH_IS_EVEN}', 'EVEN'],\n          ['%{BKY_MATH_IS_ODD}', 'ODD'],\n          ['%{BKY_MATH_IS_PRIME}', 'PRIME'],\n          ['%{BKY_MATH_IS_WHOLE}', 'WHOLE'],\n          ['%{BKY_MATH_IS_POSITIVE}', 'POSITIVE'],\n          ['%{BKY_MATH_IS_NEGATIVE}', 'NEGATIVE'],\n          ['%{BKY_MATH_IS_DIVISIBLE_BY}', 'DIVISIBLE_BY'],\n        ],\n      },\n    ],\n    'inputsInline': true,\n    'output': 'Boolean',\n    'style': 'math_blocks',\n    'tooltip': '%{BKY_MATH_IS_TOOLTIP}',\n    'mutator': 'math_is_divisibleby_mutator',\n  },\n\n  // Block for adding to a variable in place.\n  {\n    'type': 'math_change',\n    'message0': '%{BKY_MATH_CHANGE_TITLE}',\n    'args0': [\n      {\n        'type': 'field_variable',\n        'name': 'VAR',\n        'variable': '%{BKY_MATH_CHANGE_TITLE_ITEM}',\n      },\n      {\n        'type': 'input_value',\n        'name': 'DELTA',\n        'check': 'Number',\n      },\n    ],\n    'previousStatement': null,\n    'nextStatement': null,\n    'style': 'variable_blocks',\n    'helpUrl': '%{BKY_MATH_CHANGE_HELPURL}',\n    'extensions': ['math_change_tooltip'],\n  },\n\n  // Block for rounding functions.\n  {\n    'type': 'math_round',\n    'message0': '%1 %2',\n    'args0': [\n      {\n        'type': 'field_dropdown',\n        'name': 'OP',\n        'options': [\n          ['%{BKY_MATH_ROUND_OPERATOR_ROUND}', 'ROUND'],\n          ['%{BKY_MATH_ROUND_OPERATOR_ROUNDUP}', 'ROUNDUP'],\n          ['%{BKY_MATH_ROUND_OPERATOR_ROUNDDOWN}', 'ROUNDDOWN'],\n        ],\n      },\n      {\n        'type': 'input_value',\n        'name': 'NUM',\n        'check': 'Number',\n      },\n    ],\n    'output': 'Number',\n    'style': 'math_blocks',\n    'helpUrl': '%{BKY_MATH_ROUND_HELPURL}',\n    'tooltip': '%{BKY_MATH_ROUND_TOOLTIP}',\n  },\n\n  // Block for evaluating a list of numbers to return sum, average, min, max,\n  // etc.  Some functions also work on text (min, max, mode, median).\n  {\n    'type': 'math_on_list',\n    'message0': '%1 %2',\n    'args0': [\n      {\n        'type': 'field_dropdown',\n        'name': 'OP',\n        'options': [\n          ['%{BKY_MATH_ONLIST_OPERATOR_SUM}', 'SUM'],\n          ['%{BKY_MATH_ONLIST_OPERATOR_MIN}', 'MIN'],\n          ['%{BKY_MATH_ONLIST_OPERATOR_MAX}', 'MAX'],\n          ['%{BKY_MATH_ONLIST_OPERATOR_AVERAGE}', 'AVERAGE'],\n          ['%{BKY_MATH_ONLIST_OPERATOR_MEDIAN}', 'MEDIAN'],\n          ['%{BKY_MATH_ONLIST_OPERATOR_MODE}', 'MODE'],\n          ['%{BKY_MATH_ONLIST_OPERATOR_STD_DEV}', 'STD_DEV'],\n          ['%{BKY_MATH_ONLIST_OPERATOR_RANDOM}', 'RANDOM'],\n        ],\n      },\n      {\n        'type': 'input_value',\n        'name': 'LIST',\n        'check': 'Array',\n      },\n    ],\n    'output': 'Number',\n    'style': 'math_blocks',\n    'helpUrl': '%{BKY_MATH_ONLIST_HELPURL}',\n    'mutator': 'math_modes_of_list_mutator',\n    'extensions': ['math_op_tooltip'],\n  },\n\n  // Block for remainder of a division.\n  {\n    'type': 'math_modulo',\n    'message0': '%{BKY_MATH_MODULO_TITLE}',\n    'args0': [\n      {\n        'type': 'input_value',\n        'name': 'DIVIDEND',\n        'check': 'Number',\n      },\n      {\n        'type': 'input_value',\n        'name': 'DIVISOR',\n        'check': 'Number',\n      },\n    ],\n    'inputsInline': true,\n    'output': 'Number',\n    'style': 'math_blocks',\n    'tooltip': '%{BKY_MATH_MODULO_TOOLTIP}',\n    'helpUrl': '%{BKY_MATH_MODULO_HELPURL}',\n  },\n\n  // Block for constraining a number between two limits.\n  {\n    'type': 'math_constrain',\n    'message0': '%{BKY_MATH_CONSTRAIN_TITLE}',\n    'args0': [\n      {\n        'type': 'input_value',\n        'name': 'VALUE',\n        'check': 'Number',\n      },\n      {\n        'type': 'input_value',\n        'name': 'LOW',\n        'check': 'Number',\n      },\n      {\n        'type': 'input_value',\n        'name': 'HIGH',\n        'check': 'Number',\n      },\n    ],\n    'inputsInline': true,\n    'output': 'Number',\n    'style': 'math_blocks',\n    'tooltip': '%{BKY_MATH_CONSTRAIN_TOOLTIP}',\n    'helpUrl': '%{BKY_MATH_CONSTRAIN_HELPURL}',\n  },\n\n  // Block for random integer between [X] and [Y].\n  {\n    'type': 'math_random_int',\n    'message0': '%{BKY_MATH_RANDOM_INT_TITLE}',\n    'args0': [\n      {\n        'type': 'input_value',\n        'name': 'FROM',\n        'check': 'Number',\n      },\n      {\n        'type': 'input_value',\n        'name': 'TO',\n        'check': 'Number',\n      },\n    ],\n    'inputsInline': true,\n    'output': 'Number',\n    'style': 'math_blocks',\n    'tooltip': '%{BKY_MATH_RANDOM_INT_TOOLTIP}',\n    'helpUrl': '%{BKY_MATH_RANDOM_INT_HELPURL}',\n  },\n\n  // Block for random integer between [X] and [Y].\n  {\n    'type': 'math_random_float',\n    'message0': '%{BKY_MATH_RANDOM_FLOAT_TITLE_RANDOM}',\n    'output': 'Number',\n    'style': 'math_blocks',\n    'tooltip': '%{BKY_MATH_RANDOM_FLOAT_TOOLTIP}',\n    'helpUrl': '%{BKY_MATH_RANDOM_FLOAT_HELPURL}',\n  },\n\n  // Block for calculating atan2 of [X] and [Y].\n  {\n    'type': 'math_atan2',\n    'message0': '%{BKY_MATH_ATAN2_TITLE}',\n    'args0': [\n      {\n        'type': 'input_value',\n        'name': 'X',\n        'check': 'Number',\n      },\n      {\n        'type': 'input_value',\n        'name': 'Y',\n        'check': 'Number',\n      },\n    ],\n    'inputsInline': true,\n    'output': 'Number',\n    'style': 'math_blocks',\n    'tooltip': '%{BKY_MATH_ATAN2_TOOLTIP}',\n    'helpUrl': '%{BKY_MATH_ATAN2_HELPURL}',\n  },\n]);\n\n/**\n * Mapping of math block OP value to tooltip message for blocks\n * math_arithmetic, math_simple, math_trig, and math_on_lists.\n *\n * @see {Extensions#buildTooltipForDropdown}\n * @package\n * @readonly\n */\nconst TOOLTIPS_BY_OP = {\n  // math_arithmetic\n  'ADD': '%{BKY_MATH_ARITHMETIC_TOOLTIP_ADD}',\n  'MINUS': '%{BKY_MATH_ARITHMETIC_TOOLTIP_MINUS}',\n  'MULTIPLY': '%{BKY_MATH_ARITHMETIC_TOOLTIP_MULTIPLY}',\n  'DIVIDE': '%{BKY_MATH_ARITHMETIC_TOOLTIP_DIVIDE}',\n  'POWER': '%{BKY_MATH_ARITHMETIC_TOOLTIP_POWER}',\n\n  // math_simple\n  'ROOT': '%{BKY_MATH_SINGLE_TOOLTIP_ROOT}',\n  'ABS': '%{BKY_MATH_SINGLE_TOOLTIP_ABS}',\n  'NEG': '%{BKY_MATH_SINGLE_TOOLTIP_NEG}',\n  'LN': '%{BKY_MATH_SINGLE_TOOLTIP_LN}',\n  'LOG10': '%{BKY_MATH_SINGLE_TOOLTIP_LOG10}',\n  'EXP': '%{BKY_MATH_SINGLE_TOOLTIP_EXP}',\n  'POW10': '%{BKY_MATH_SINGLE_TOOLTIP_POW10}',\n\n  // math_trig\n  'SIN': '%{BKY_MATH_TRIG_TOOLTIP_SIN}',\n  'COS': '%{BKY_MATH_TRIG_TOOLTIP_COS}',\n  'TAN': '%{BKY_MATH_TRIG_TOOLTIP_TAN}',\n  'ASIN': '%{BKY_MATH_TRIG_TOOLTIP_ASIN}',\n  'ACOS': '%{BKY_MATH_TRIG_TOOLTIP_ACOS}',\n  'ATAN': '%{BKY_MATH_TRIG_TOOLTIP_ATAN}',\n\n  // math_on_lists\n  'SUM': '%{BKY_MATH_ONLIST_TOOLTIP_SUM}',\n  'MIN': '%{BKY_MATH_ONLIST_TOOLTIP_MIN}',\n  'MAX': '%{BKY_MATH_ONLIST_TOOLTIP_MAX}',\n  'AVERAGE': '%{BKY_MATH_ONLIST_TOOLTIP_AVERAGE}',\n  'MEDIAN': '%{BKY_MATH_ONLIST_TOOLTIP_MEDIAN}',\n  'MODE': '%{BKY_MATH_ONLIST_TOOLTIP_MODE}',\n  'STD_DEV': '%{BKY_MATH_ONLIST_TOOLTIP_STD_DEV}',\n  'RANDOM': '%{BKY_MATH_ONLIST_TOOLTIP_RANDOM}',\n};\n\nExtensions.register(\n  'math_op_tooltip',\n  Extensions.buildTooltipForDropdown('OP', TOOLTIPS_BY_OP),\n);\n\n/** Type of a block that has IS_DIVISBLEBY_MUTATOR_MIXIN */\ntype DivisiblebyBlock = Block & DivisiblebyMixin;\ninterface DivisiblebyMixin extends DivisiblebyMixinType {}\ntype DivisiblebyMixinType = typeof IS_DIVISIBLEBY_MUTATOR_MIXIN;\n\n/**\n * Mixin for mutator functions in the 'math_is_divisibleby_mutator'\n * extension.\n *\n * @mixin\n * @augments Block\n * @package\n */\nconst IS_DIVISIBLEBY_MUTATOR_MIXIN = {\n  /**\n   * Create XML to represent whether the 'divisorInput' should be present.\n   * Backwards compatible serialization implementation.\n   *\n   * @returns XML storage element.\n   */\n  mutationToDom: function (this: DivisiblebyBlock): Element {\n    const container = xmlUtils.createElement('mutation');\n    const divisorInput = this.getFieldValue('PROPERTY') === 'DIVISIBLE_BY';\n    container.setAttribute('divisor_input', String(divisorInput));\n    return container;\n  },\n  /**\n   * Parse XML to restore the 'divisorInput'.\n   * Backwards compatible serialization implementation.\n   *\n   * @param xmlElement XML storage element.\n   */\n  domToMutation: function (this: DivisiblebyBlock, xmlElement: Element) {\n    const divisorInput = xmlElement.getAttribute('divisor_input') === 'true';\n    this.updateShape_(divisorInput);\n  },\n\n  // This block does not need JSO serialization hooks (saveExtraState and\n  // loadExtraState) because the state of this object is already encoded in the\n  // dropdown values.\n  // XML hooks are kept for backwards compatibility.\n\n  /**\n   * Modify this block to have (or not have) an input for 'is divisible by'.\n   *\n   * @param divisorInput True if this block has a divisor input.\n   */\n  updateShape_: function (this: DivisiblebyBlock, divisorInput: boolean) {\n    // Add or remove a Value Input.\n    const inputExists = this.getInput('DIVISOR');\n    if (divisorInput) {\n      if (!inputExists) {\n        this.appendValueInput('DIVISOR').setCheck('Number');\n      }\n    } else if (inputExists) {\n      this.removeInput('DIVISOR');\n    }\n  },\n};\n\n/**\n * 'math_is_divisibleby_mutator' extension to the 'math_property' block that\n * can update the block shape (add/remove divisor input) based on whether\n * property is \"divisible by\".\n */\nconst IS_DIVISIBLE_MUTATOR_EXTENSION = function (this: DivisiblebyBlock) {\n  this.getField('PROPERTY')!.setValidator(\n    /** @param option The selected dropdown option. */\n    function (this: FieldDropdown, option: string) {\n      const divisorInput = option === 'DIVISIBLE_BY';\n      (this.getSourceBlock() as DivisiblebyBlock).updateShape_(divisorInput);\n      return undefined; // FieldValidators can't be void.  Use option as-is.\n    },\n  );\n};\n\nExtensions.registerMutator(\n  'math_is_divisibleby_mutator',\n  IS_DIVISIBLEBY_MUTATOR_MIXIN,\n  IS_DIVISIBLE_MUTATOR_EXTENSION,\n);\n\n// Update the tooltip of 'math_change' block to reference the variable.\nExtensions.register(\n  'math_change_tooltip',\n  Extensions.buildTooltipWithFieldText('%{BKY_MATH_CHANGE_TOOLTIP}', 'VAR'),\n);\n\n/** Type of a block that has LIST_MODES_MUTATOR_MIXIN */\ntype ListModesBlock = Block & ListModesMixin;\ninterface ListModesMixin extends ListModesMixinType {}\ntype ListModesMixinType = typeof LIST_MODES_MUTATOR_MIXIN;\n\n/**\n * Mixin with mutator methods to support alternate output based if the\n * 'math_on_list' block uses the 'MODE' operation.\n */\nconst LIST_MODES_MUTATOR_MIXIN = {\n  /**\n   * Modify this block to have the correct output type.\n   *\n   * @param newOp Either 'MODE' or some op than returns a number.\n   */\n  updateType_: function (this: ListModesBlock, newOp: string) {\n    if (newOp === 'MODE') {\n      this.outputConnection!.setCheck('Array');\n    } else {\n      this.outputConnection!.setCheck('Number');\n    }\n  },\n  /**\n   * Create XML to represent the output type.\n   * Backwards compatible serialization implementation.\n   *\n   * @returns XML storage element.\n   */\n  mutationToDom: function (this: ListModesBlock): Element {\n    const container = xmlUtils.createElement('mutation');\n    container.setAttribute('op', this.getFieldValue('OP'));\n    return container;\n  },\n  /**\n   * Parse XML to restore the output type.\n   * Backwards compatible serialization implementation.\n   *\n   * @param xmlElement XML storage element.\n   */\n  domToMutation: function (this: ListModesBlock, xmlElement: Element) {\n    const op = xmlElement.getAttribute('op');\n    if (op === null) throw new TypeError('xmlElement had no op attribute');\n    this.updateType_(op);\n  },\n\n  // This block does not need JSO serialization hooks (saveExtraState and\n  // loadExtraState) because the state of this object is already encoded in the\n  // dropdown values.\n  // XML hooks are kept for backwards compatibility.\n};\n\n/**\n * Extension to 'math_on_list' blocks that allows support of\n * modes operation (outputs a list of numbers).\n */\nconst LIST_MODES_MUTATOR_EXTENSION = function (this: ListModesBlock) {\n  this.getField('OP')!.setValidator(\n    function (this: ListModesBlock, newOp: string) {\n      this.updateType_(newOp);\n      return undefined;\n    }.bind(this),\n  );\n};\n\nExtensions.registerMutator(\n  'math_modes_of_list_mutator',\n  LIST_MODES_MUTATOR_MIXIN,\n  LIST_MODES_MUTATOR_EXTENSION,\n);\n\n// Register provided blocks.\ndefineBlocks(blocks);\n", "/**\n * @license\n * Copyright 2012 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n// Former goog.module ID: Blockly.libraryBlocks.loops\n\nimport type {Block} from '../core/block.js';\nimport {\n  createBlockDefinitionsFromJsonArray,\n  defineBlocks,\n} from '../core/common.js';\nimport * as ContextMenu from '../core/contextmenu.js';\nimport type {\n  ContextMenuOption,\n  LegacyContextMenuOption,\n} from '../core/contextmenu_registry.js';\nimport * as Events from '../core/events/events.js';\nimport type {Abstract as AbstractEvent} from '../core/events/events_abstract.js';\nimport * as eventUtils from '../core/events/utils.js';\nimport * as Extensions from '../core/extensions.js';\nimport '../core/field_dropdown.js';\nimport '../core/field_label.js';\nimport '../core/field_number.js';\nimport '../core/field_variable.js';\nimport {FieldVariable} from '../core/field_variable.js';\nimport '../core/icons/warning_icon.js';\nimport {Msg} from '../core/msg.js';\nimport {WorkspaceSvg} from '../core/workspace_svg.js';\n\n/**\n * A dictionary of the block definitions provided by this module.\n */\nexport const blocks = createBlockDefinitionsFromJsonArray([\n  // Block for repeat n times (external number).\n  {\n    'type': 'controls_repeat_ext',\n    'message0': '%{BKY_CONTROLS_REPEAT_TITLE}',\n    'args0': [\n      {\n        'type': 'input_value',\n        'name': 'TIMES',\n        'check': 'Number',\n      },\n    ],\n    'message1': '%{BKY_CONTROLS_REPEAT_INPUT_DO} %1',\n    'args1': [\n      {\n        'type': 'input_statement',\n        'name': 'DO',\n      },\n    ],\n    'previousStatement': null,\n    'nextStatement': null,\n    'style': 'loop_blocks',\n    'tooltip': '%{BKY_CONTROLS_REPEAT_TOOLTIP}',\n    'helpUrl': '%{BKY_CONTROLS_REPEAT_HELPURL}',\n  },\n  // Block for repeat n times (internal number).\n  // The 'controls_repeat_ext' block is preferred as it is more flexible.\n  {\n    'type': 'controls_repeat',\n    'message0': '%{BKY_CONTROLS_REPEAT_TITLE}',\n    'args0': [\n      {\n        'type': 'field_number',\n        'name': 'TIMES',\n        'value': 10,\n        'min': 0,\n        'precision': 1,\n      },\n    ],\n    'message1': '%{BKY_CONTROLS_REPEAT_INPUT_DO} %1',\n    'args1': [\n      {\n        'type': 'input_statement',\n        'name': 'DO',\n      },\n    ],\n    'previousStatement': null,\n    'nextStatement': null,\n    'style': 'loop_blocks',\n    'tooltip': '%{BKY_CONTROLS_REPEAT_TOOLTIP}',\n    'helpUrl': '%{BKY_CONTROLS_REPEAT_HELPURL}',\n  },\n  // Block for 'do while/until' loop.\n  {\n    'type': 'controls_whileUntil',\n    'message0': '%1 %2',\n    'args0': [\n      {\n        'type': 'field_dropdown',\n        'name': 'MODE',\n        'options': [\n          ['%{BKY_CONTROLS_WHILEUNTIL_OPERATOR_WHILE}', 'WHILE'],\n          ['%{BKY_CONTROLS_WHILEUNTIL_OPERATOR_UNTIL}', 'UNTIL'],\n        ],\n      },\n      {\n        'type': 'input_value',\n        'name': 'BOOL',\n        'check': 'Boolean',\n      },\n    ],\n    'message1': '%{BKY_CONTROLS_REPEAT_INPUT_DO} %1',\n    'args1': [\n      {\n        'type': 'input_statement',\n        'name': 'DO',\n      },\n    ],\n    'previousStatement': null,\n    'nextStatement': null,\n    'style': 'loop_blocks',\n    'helpUrl': '%{BKY_CONTROLS_WHILEUNTIL_HELPURL}',\n    'extensions': ['controls_whileUntil_tooltip'],\n  },\n  // Block for 'for' loop.\n  {\n    'type': 'controls_for',\n    'message0': '%{BKY_CONTROLS_FOR_TITLE}',\n    'args0': [\n      {\n        'type': 'field_variable',\n        'name': 'VAR',\n        'variable': null,\n      },\n      {\n        'type': 'input_value',\n        'name': 'FROM',\n        'check': 'Number',\n        'align': 'RIGHT',\n      },\n      {\n        'type': 'input_value',\n        'name': 'TO',\n        'check': 'Number',\n        'align': 'RIGHT',\n      },\n      {\n        'type': 'input_value',\n        'name': 'BY',\n        'check': 'Number',\n        'align': 'RIGHT',\n      },\n    ],\n    'message1': '%{BKY_CONTROLS_REPEAT_INPUT_DO} %1',\n    'args1': [\n      {\n        'type': 'input_statement',\n        'name': 'DO',\n      },\n    ],\n    'inputsInline': true,\n    'previousStatement': null,\n    'nextStatement': null,\n    'style': 'loop_blocks',\n    'helpUrl': '%{BKY_CONTROLS_FOR_HELPURL}',\n    'extensions': ['contextMenu_newGetVariableBlock', 'controls_for_tooltip'],\n  },\n  // Block for 'for each' loop.\n  {\n    'type': 'controls_forEach',\n    'message0': '%{BKY_CONTROLS_FOREACH_TITLE}',\n    'args0': [\n      {\n        'type': 'field_variable',\n        'name': 'VAR',\n        'variable': null,\n      },\n      {\n        'type': 'input_value',\n        'name': 'LIST',\n        'check': 'Array',\n      },\n    ],\n    'message1': '%{BKY_CONTROLS_REPEAT_INPUT_DO} %1',\n    'args1': [\n      {\n        'type': 'input_statement',\n        'name': 'DO',\n      },\n    ],\n    'previousStatement': null,\n    'nextStatement': null,\n    'style': 'loop_blocks',\n    'helpUrl': '%{BKY_CONTROLS_FOREACH_HELPURL}',\n    'extensions': [\n      'contextMenu_newGetVariableBlock',\n      'controls_forEach_tooltip',\n    ],\n  },\n  // Block for flow statements: continue, break.\n  {\n    'type': 'controls_flow_statements',\n    'message0': '%1',\n    'args0': [\n      {\n        'type': 'field_dropdown',\n        'name': 'FLOW',\n        'options': [\n          ['%{BKY_CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK}', 'BREAK'],\n          ['%{BKY_CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE}', 'CONTINUE'],\n        ],\n      },\n    ],\n    'previousStatement': null,\n    'style': 'loop_blocks',\n    'helpUrl': '%{BKY_CONTROLS_FLOW_STATEMENTS_HELPURL}',\n    'suppressPrefixSuffix': true,\n    'extensions': ['controls_flow_tooltip', 'controls_flow_in_loop_check'],\n  },\n]);\n\n/**\n * Tooltips for the 'controls_whileUntil' block, keyed by MODE value.\n *\n * @see {Extensions#buildTooltipForDropdown}\n */\nconst WHILE_UNTIL_TOOLTIPS = {\n  'WHILE': '%{BKY_CONTROLS_WHILEUNTIL_TOOLTIP_WHILE}',\n  'UNTIL': '%{BKY_CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL}',\n};\n\nExtensions.register(\n  'controls_whileUntil_tooltip',\n  Extensions.buildTooltipForDropdown('MODE', WHILE_UNTIL_TOOLTIPS),\n);\n\n/**\n * Tooltips for the 'controls_flow_statements' block, keyed by FLOW value.\n *\n * @see {Extensions#buildTooltipForDropdown}\n */\nconst BREAK_CONTINUE_TOOLTIPS = {\n  'BREAK': '%{BKY_CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK}',\n  'CONTINUE': '%{BKY_CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE}',\n};\n\nExtensions.register(\n  'controls_flow_tooltip',\n  Extensions.buildTooltipForDropdown('FLOW', BREAK_CONTINUE_TOOLTIPS),\n);\n\n/** Type of a block that has CUSTOM_CONTEXT_MENU_CREATE_VARIABLES_GET_MIXIN */\ntype CustomContextMenuBlock = Block & CustomContextMenuMixin;\ninterface CustomContextMenuMixin extends CustomContextMenuMixinType {}\ntype CustomContextMenuMixinType =\n  typeof CUSTOM_CONTEXT_MENU_CREATE_VARIABLES_GET_MIXIN;\n\n/**\n * Mixin to add a context menu item to create a 'variables_get' block.\n * Used by blocks 'controls_for' and 'controls_forEach'.\n */\nconst CUSTOM_CONTEXT_MENU_CREATE_VARIABLES_GET_MIXIN = {\n  /**\n   * Add context menu option to create getter block for the loop's variable.\n   * (customContextMenu support limited to web BlockSvg.)\n   *\n   * @param options List of menu options to add to.\n   */\n  customContextMenu: function (\n    this: CustomContextMenuBlock,\n    options: Array<ContextMenuOption | LegacyContextMenuOption>,\n  ) {\n    if (this.isInFlyout) {\n      return;\n    }\n    const varField = this.getField('VAR') as FieldVariable;\n    const variable = varField.getVariable()!;\n    const varName = variable.getName();\n    if (!this.isCollapsed() && varName !== null) {\n      const getVarBlockState = {\n        type: 'variables_get',\n        fields: {VAR: varField.saveState(true)},\n      };\n\n      options.push({\n        enabled: true,\n        text: Msg['VARIABLES_SET_CREATE_GET'].replace('%1', varName),\n        callback: ContextMenu.callbackFactory(this, getVarBlockState),\n      });\n    }\n  },\n};\n\nExtensions.registerMixin(\n  'contextMenu_newGetVariableBlock',\n  CUSTOM_CONTEXT_MENU_CREATE_VARIABLES_GET_MIXIN,\n);\n\nExtensions.register(\n  'controls_for_tooltip',\n  Extensions.buildTooltipWithFieldText('%{BKY_CONTROLS_FOR_TOOLTIP}', 'VAR'),\n);\n\nExtensions.register(\n  'controls_forEach_tooltip',\n  Extensions.buildTooltipWithFieldText(\n    '%{BKY_CONTROLS_FOREACH_TOOLTIP}',\n    'VAR',\n  ),\n);\n\n/**\n * List of block types that are loops and thus do not need warnings.\n * To add a new loop type add this to your code:\n *\n * // If using the Blockly npm package and es6 import syntax:\n * import {loops} from 'blockly/blocks';\n * loops.loopTypes.add('custom_loop');\n *\n * // Else if using Closure Compiler and goog.modules:\n * const {loopTypes} = goog.require('Blockly.libraryBlocks.loops');\n * loopTypes.add('custom_loop');\n *\n * // Else if using blockly_compressed + blockss_compressed.js in browser:\n * Blockly.libraryBlocks.loopTypes.add('custom_loop');\n */\nexport const loopTypes: Set<string> = new Set([\n  'controls_repeat',\n  'controls_repeat_ext',\n  'controls_forEach',\n  'controls_for',\n  'controls_whileUntil',\n]);\n\n/**\n * Type of a block that has CONTROL_FLOW_IN_LOOP_CHECK_MIXIN\n *\n * @internal\n */\nexport type ControlFlowInLoopBlock = Block & ControlFlowInLoopMixin;\ninterface ControlFlowInLoopMixin extends ControlFlowInLoopMixinType {}\ntype ControlFlowInLoopMixinType = typeof CONTROL_FLOW_IN_LOOP_CHECK_MIXIN;\n\n/**\n * The language-neutral ID for when the reason why a block is disabled is\n * because the block is only valid inside of a loop.\n */\nconst CONTROL_FLOW_NOT_IN_LOOP_DISABLED_REASON = 'CONTROL_FLOW_NOT_IN_LOOP';\n/**\n * This mixin adds a check to make sure the 'controls_flow_statements' block\n * is contained in a loop. Otherwise a warning is added to the block.\n */\nconst CONTROL_FLOW_IN_LOOP_CHECK_MIXIN = {\n  /**\n   * Is this block enclosed (at any level) by a loop?\n   *\n   * @returns The nearest surrounding loop, or null if none.\n   */\n  getSurroundLoop: function (this: ControlFlowInLoopBlock): Block | null {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    let block: Block | null = this;\n    do {\n      if (loopTypes.has(block.type)) {\n        return block;\n      }\n      block = block.getSurroundParent();\n    } while (block);\n    return null;\n  },\n\n  /**\n   * Called whenever anything on the workspace changes.\n   * Add warning if this flow block is not nested inside a loop.\n   */\n  onchange: function (this: ControlFlowInLoopBlock, e: AbstractEvent) {\n    const ws = this.workspace as WorkspaceSvg;\n    // Don't change state if:\n    //   * It's at the start of a drag.\n    //   * It's not a move event.\n    if (\n      !ws.isDragging ||\n      ws.isDragging() ||\n      (e.type !== Events.BLOCK_MOVE && e.type !== Events.BLOCK_CREATE)\n    ) {\n      return;\n    }\n    const enabled = !!this.getSurroundLoop();\n    this.setWarningText(\n      enabled ? null : Msg['CONTROLS_FLOW_STATEMENTS_WARNING'],\n    );\n\n    if (!this.isInFlyout) {\n      try {\n        // There is no need to record the enable/disable change on the undo/redo\n        // list since the change will be automatically recreated when replayed.\n        eventUtils.setRecordUndo(false);\n        this.setDisabledReason(\n          !enabled,\n          CONTROL_FLOW_NOT_IN_LOOP_DISABLED_REASON,\n        );\n      } finally {\n        eventUtils.setRecordUndo(true);\n      }\n    }\n  },\n};\n\nExtensions.registerMixin(\n  'controls_flow_in_loop_check',\n  CONTROL_FLOW_IN_LOOP_CHECK_MIXIN,\n);\n\n// Register provided blocks.\ndefineBlocks(blocks);\n", "/**\n * @license\n * Copyright 2012 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n// Former goog.module ID: Blockly.libraryBlocks.logic\n\nimport type {Block} from '../core/block.js';\nimport type {BlockSvg} from '../core/block_svg.js';\nimport {\n  createBlockDefinitionsFromJsonArray,\n  defineBlocks,\n} from '../core/common.js';\nimport type {Connection} from '../core/connection.js';\nimport * as Events from '../core/events/events.js';\nimport type {Abstract as AbstractEvent} from '../core/events/events_abstract.js';\nimport * as Extensions from '../core/extensions.js';\nimport '../core/field_dropdown.js';\nimport '../core/field_label.js';\nimport '../core/icons/mutator_icon.js';\nimport {Msg} from '../core/msg.js';\nimport * as xmlUtils from '../core/utils/xml.js';\nimport type {Workspace} from '../core/workspace.js';\n\n/**\n * A dictionary of the block definitions provided by this module.\n */\nexport const blocks = createBlockDefinitionsFromJsonArray([\n  // Block for boolean data type: true and false.\n  {\n    'type': 'logic_boolean',\n    'message0': '%1',\n    'args0': [\n      {\n        'type': 'field_dropdown',\n        'name': 'BOOL',\n        'options': [\n          ['%{BKY_LOGIC_BOOLEAN_TRUE}', 'TRUE'],\n          ['%{BKY_LOGIC_BOOLEAN_FALSE}', 'FALSE'],\n        ],\n      },\n    ],\n    'output': 'Boolean',\n    'style': 'logic_blocks',\n    'tooltip': '%{BKY_LOGIC_BOOLEAN_TOOLTIP}',\n    'helpUrl': '%{BKY_LOGIC_BOOLEAN_HELPURL}',\n  },\n  // Block for if/elseif/else condition.\n  {\n    'type': 'controls_if',\n    'message0': '%{BKY_CONTROLS_IF_MSG_IF} %1',\n    'args0': [\n      {\n        'type': 'input_value',\n        'name': 'IF0',\n        'check': 'Boolean',\n      },\n    ],\n    'message1': '%{BKY_CONTROLS_IF_MSG_THEN} %1',\n    'args1': [\n      {\n        'type': 'input_statement',\n        'name': 'DO0',\n      },\n    ],\n    'previousStatement': null,\n    'nextStatement': null,\n    'style': 'logic_blocks',\n    'helpUrl': '%{BKY_CONTROLS_IF_HELPURL}',\n    'suppressPrefixSuffix': true,\n    'mutator': 'controls_if_mutator',\n    'extensions': ['controls_if_tooltip'],\n  },\n  // If/else block that does not use a mutator.\n  {\n    'type': 'controls_ifelse',\n    'message0': '%{BKY_CONTROLS_IF_MSG_IF} %1',\n    'args0': [\n      {\n        'type': 'input_value',\n        'name': 'IF0',\n        'check': 'Boolean',\n      },\n    ],\n    'message1': '%{BKY_CONTROLS_IF_MSG_THEN} %1',\n    'args1': [\n      {\n        'type': 'input_statement',\n        'name': 'DO0',\n      },\n    ],\n    'message2': '%{BKY_CONTROLS_IF_MSG_ELSE} %1',\n    'args2': [\n      {\n        'type': 'input_statement',\n        'name': 'ELSE',\n      },\n    ],\n    'previousStatement': null,\n    'nextStatement': null,\n    'style': 'logic_blocks',\n    'tooltip': '%{BKYCONTROLS_IF_TOOLTIP_2}',\n    'helpUrl': '%{BKY_CONTROLS_IF_HELPURL}',\n    'suppressPrefixSuffix': true,\n    'extensions': ['controls_if_tooltip'],\n  },\n  // Block for comparison operator.\n  {\n    'type': 'logic_compare',\n    'message0': '%1 %2 %3',\n    'args0': [\n      {\n        'type': 'input_value',\n        'name': 'A',\n      },\n      {\n        'type': 'field_dropdown',\n        'name': 'OP',\n        'options': [\n          ['=', 'EQ'],\n          ['\\u2260', 'NEQ'],\n          ['\\u200F<', 'LT'],\n          ['\\u200F\\u2264', 'LTE'],\n          ['\\u200F>', 'GT'],\n          ['\\u200F\\u2265', 'GTE'],\n        ],\n      },\n      {\n        'type': 'input_value',\n        'name': 'B',\n      },\n    ],\n    'inputsInline': true,\n    'output': 'Boolean',\n    'style': 'logic_blocks',\n    'helpUrl': '%{BKY_LOGIC_COMPARE_HELPURL}',\n    'extensions': ['logic_compare', 'logic_op_tooltip'],\n  },\n  // Block for logical operations: 'and', 'or'.\n  {\n    'type': 'logic_operation',\n    'message0': '%1 %2 %3',\n    'args0': [\n      {\n        'type': 'input_value',\n        'name': 'A',\n        'check': 'Boolean',\n      },\n      {\n        'type': 'field_dropdown',\n        'name': 'OP',\n        'options': [\n          ['%{BKY_LOGIC_OPERATION_AND}', 'AND'],\n          ['%{BKY_LOGIC_OPERATION_OR}', 'OR'],\n        ],\n      },\n      {\n        'type': 'input_value',\n        'name': 'B',\n        'check': 'Boolean',\n      },\n    ],\n    'inputsInline': true,\n    'output': 'Boolean',\n    'style': 'logic_blocks',\n    'helpUrl': '%{BKY_LOGIC_OPERATION_HELPURL}',\n    'extensions': ['logic_op_tooltip'],\n  },\n  // Block for negation.\n  {\n    'type': 'logic_negate',\n    'message0': '%{BKY_LOGIC_NEGATE_TITLE}',\n    'args0': [\n      {\n        'type': 'input_value',\n        'name': 'BOOL',\n        'check': 'Boolean',\n      },\n    ],\n    'output': 'Boolean',\n    'style': 'logic_blocks',\n    'tooltip': '%{BKY_LOGIC_NEGATE_TOOLTIP}',\n    'helpUrl': '%{BKY_LOGIC_NEGATE_HELPURL}',\n  },\n  // Block for null data type.\n  {\n    'type': 'logic_null',\n    'message0': '%{BKY_LOGIC_NULL}',\n    'output': null,\n    'style': 'logic_blocks',\n    'tooltip': '%{BKY_LOGIC_NULL_TOOLTIP}',\n    'helpUrl': '%{BKY_LOGIC_NULL_HELPURL}',\n  },\n  // Block for ternary operator.\n  {\n    'type': 'logic_ternary',\n    'message0': '%{BKY_LOGIC_TERNARY_CONDITION} %1',\n    'args0': [\n      {\n        'type': 'input_value',\n        'name': 'IF',\n        'check': 'Boolean',\n      },\n    ],\n    'message1': '%{BKY_LOGIC_TERNARY_IF_TRUE} %1',\n    'args1': [\n      {\n        'type': 'input_value',\n        'name': 'THEN',\n      },\n    ],\n    'message2': '%{BKY_LOGIC_TERNARY_IF_FALSE} %1',\n    'args2': [\n      {\n        'type': 'input_value',\n        'name': 'ELSE',\n      },\n    ],\n    'output': null,\n    'style': 'logic_blocks',\n    'tooltip': '%{BKY_LOGIC_TERNARY_TOOLTIP}',\n    'helpUrl': '%{BKY_LOGIC_TERNARY_HELPURL}',\n    'extensions': ['logic_ternary'],\n  },\n  // Block representing the if statement in the controls_if mutator.\n  {\n    'type': 'controls_if_if',\n    'message0': '%{BKY_CONTROLS_IF_IF_TITLE_IF}',\n    'nextStatement': null,\n    'enableContextMenu': false,\n    'style': 'logic_blocks',\n    'tooltip': '%{BKY_CONTROLS_IF_IF_TOOLTIP}',\n  },\n  // Block representing the else-if statement in the controls_if mutator.\n  {\n    'type': 'controls_if_elseif',\n    'message0': '%{BKY_CONTROLS_IF_ELSEIF_TITLE_ELSEIF}',\n    'previousStatement': null,\n    'nextStatement': null,\n    'enableContextMenu': false,\n    'style': 'logic_blocks',\n    'tooltip': '%{BKY_CONTROLS_IF_ELSEIF_TOOLTIP}',\n  },\n  // Block representing the else statement in the controls_if mutator.\n  {\n    'type': 'controls_if_else',\n    'message0': '%{BKY_CONTROLS_IF_ELSE_TITLE_ELSE}',\n    'previousStatement': null,\n    'enableContextMenu': false,\n    'style': 'logic_blocks',\n    'tooltip': '%{BKY_CONTROLS_IF_ELSE_TOOLTIP}',\n  },\n]);\n\n/**\n * Tooltip text, keyed by block OP value. Used by logic_compare and\n * logic_operation blocks.\n *\n * @see {Extensions#buildTooltipForDropdown}\n */\nconst TOOLTIPS_BY_OP = {\n  // logic_compare\n  'EQ': '%{BKY_LOGIC_COMPARE_TOOLTIP_EQ}',\n  'NEQ': '%{BKY_LOGIC_COMPARE_TOOLTIP_NEQ}',\n  'LT': '%{BKY_LOGIC_COMPARE_TOOLTIP_LT}',\n  'LTE': '%{BKY_LOGIC_COMPARE_TOOLTIP_LTE}',\n  'GT': '%{BKY_LOGIC_COMPARE_TOOLTIP_GT}',\n  'GTE': '%{BKY_LOGIC_COMPARE_TOOLTIP_GTE}',\n\n  // logic_operation\n  'AND': '%{BKY_LOGIC_OPERATION_TOOLTIP_AND}',\n  'OR': '%{BKY_LOGIC_OPERATION_TOOLTIP_OR}',\n};\n\nExtensions.register(\n  'logic_op_tooltip',\n  Extensions.buildTooltipForDropdown('OP', TOOLTIPS_BY_OP),\n);\n\n/** Type of a block that has CONTROLS_IF_MUTATOR_MIXIN */\ntype IfBlock = Block & IfMixin;\ninterface IfMixin extends IfMixinType {}\ntype IfMixinType = typeof CONTROLS_IF_MUTATOR_MIXIN;\n\n// Types for quarks defined in JSON.\n/** Type of a controls_if_if (if mutator container) block. */\ninterface ContainerBlock extends Block {}\n\n/** Type of a controls_if_elseif or controls_if_else block. */\ninterface ClauseBlock extends Block {\n  valueConnection_?: Connection | null;\n  statementConnection_?: Connection | null;\n}\n\n/** Extra state for serialising controls_if blocks. */\ntype IfExtraState = {\n  elseIfCount?: number;\n  hasElse?: boolean;\n};\n\n/**\n * Mutator methods added to controls_if blocks.\n */\nconst CONTROLS_IF_MUTATOR_MIXIN = {\n  elseifCount_: 0,\n  elseCount_: 0,\n\n  /**\n   * Create XML to represent the number of else-if and else inputs.\n   * Backwards compatible serialization implementation.\n   *\n   * @returns XML storage element.\n   */\n  mutationToDom: function (this: IfBlock): Element | null {\n    if (!this.elseifCount_ && !this.elseCount_) {\n      return null;\n    }\n    const container = xmlUtils.createElement('mutation');\n    if (this.elseifCount_) {\n      container.setAttribute('elseif', String(this.elseifCount_));\n    }\n    if (this.elseCount_) {\n      container.setAttribute('else', '1');\n    }\n    return container;\n  },\n  /**\n   * Parse XML to restore the else-if and else inputs.\n   * Backwards compatible serialization implementation.\n   *\n   * @param xmlElement XML storage element.\n   */\n  domToMutation: function (this: IfBlock, xmlElement: Element) {\n    this.elseifCount_ = parseInt(xmlElement.getAttribute('elseif')!, 10) || 0;\n    this.elseCount_ = parseInt(xmlElement.getAttribute('else')!, 10) || 0;\n    this.rebuildShape_();\n  },\n  /**\n   * Returns the state of this block as a JSON serializable object.\n   *\n   * @returns The state of this block, ie the else if count and else state.\n   */\n  saveExtraState: function (this: IfBlock): IfExtraState | null {\n    if (!this.elseifCount_ && !this.elseCount_) {\n      return null;\n    }\n    const state = Object.create(null);\n    if (this.elseifCount_) {\n      state['elseIfCount'] = this.elseifCount_;\n    }\n    if (this.elseCount_) {\n      state['hasElse'] = true;\n    }\n    return state;\n  },\n  /**\n   * Applies the given state to this block.\n   *\n   * @param state The state to apply to this block, ie the else if count\n   and\n   *     else state.\n   */\n  loadExtraState: function (this: IfBlock, state: IfExtraState) {\n    this.elseifCount_ = state['elseIfCount'] || 0;\n    this.elseCount_ = state['hasElse'] ? 1 : 0;\n    this.updateShape_();\n  },\n  /**\n   * Populate the mutator's dialog with this block's components.\n   *\n   * @param workspace MutatorIcon's workspace.\n   * @returns Root block in mutator.\n   */\n  decompose: function (this: IfBlock, workspace: Workspace): ContainerBlock {\n    const containerBlock = workspace.newBlock('controls_if_if');\n    (containerBlock as BlockSvg).initSvg();\n    let connection = containerBlock.nextConnection!;\n    for (let i = 1; i <= this.elseifCount_; i++) {\n      const elseifBlock = workspace.newBlock('controls_if_elseif');\n      (elseifBlock as BlockSvg).initSvg();\n      connection.connect(elseifBlock.previousConnection!);\n      connection = elseifBlock.nextConnection!;\n    }\n    if (this.elseCount_) {\n      const elseBlock = workspace.newBlock('controls_if_else');\n      (elseBlock as BlockSvg).initSvg();\n      connection.connect(elseBlock.previousConnection!);\n    }\n    return containerBlock;\n  },\n  /**\n   * Reconfigure this block based on the mutator dialog's components.\n   *\n   * @param containerBlock Root block in mutator.\n   */\n  compose: function (this: IfBlock, containerBlock: ContainerBlock) {\n    let clauseBlock =\n      containerBlock.nextConnection!.targetBlock() as ClauseBlock | null;\n    // Count number of inputs.\n    this.elseifCount_ = 0;\n    this.elseCount_ = 0;\n    // Connections arrays are passed to .reconnectChildBlocks_() which\n    // takes 1-based arrays, so are initialised with a dummy value at\n    // index 0 for convenience.\n    const valueConnections: Array<Connection | null> = [null];\n    const statementConnections: Array<Connection | null> = [null];\n    let elseStatementConnection: Connection | null = null;\n    while (clauseBlock) {\n      if (clauseBlock.isInsertionMarker()) {\n        clauseBlock = clauseBlock.getNextBlock() as ClauseBlock | null;\n        continue;\n      }\n      switch (clauseBlock.type) {\n        case 'controls_if_elseif':\n          this.elseifCount_++;\n          // TODO(#6920): null valid, undefined not.\n          valueConnections.push(\n            clauseBlock.valueConnection_ as Connection | null,\n          );\n          statementConnections.push(\n            clauseBlock.statementConnection_ as Connection | null,\n          );\n          break;\n        case 'controls_if_else':\n          this.elseCount_++;\n          elseStatementConnection =\n            clauseBlock.statementConnection_ as Connection | null;\n          break;\n        default:\n          throw TypeError('Unknown block type: ' + clauseBlock.type);\n      }\n      clauseBlock = clauseBlock.getNextBlock() as ClauseBlock | null;\n    }\n    this.updateShape_();\n    // Reconnect any child blocks.\n    this.reconnectChildBlocks_(\n      valueConnections,\n      statementConnections,\n      elseStatementConnection,\n    );\n  },\n  /**\n   * Store pointers to any connected child blocks.\n   *\n   * @param containerBlock Root block in mutator.\n   */\n  saveConnections: function (this: IfBlock, containerBlock: ContainerBlock) {\n    let clauseBlock =\n      containerBlock!.nextConnection!.targetBlock() as ClauseBlock | null;\n    let i = 1;\n    while (clauseBlock) {\n      if (clauseBlock.isInsertionMarker()) {\n        clauseBlock = clauseBlock.getNextBlock() as ClauseBlock | null;\n        continue;\n      }\n      switch (clauseBlock.type) {\n        case 'controls_if_elseif': {\n          const inputIf = this.getInput('IF' + i);\n          const inputDo = this.getInput('DO' + i);\n          clauseBlock.valueConnection_ =\n            inputIf && inputIf.connection!.targetConnection;\n          clauseBlock.statementConnection_ =\n            inputDo && inputDo.connection!.targetConnection;\n          i++;\n          break;\n        }\n        case 'controls_if_else': {\n          const inputDo = this.getInput('ELSE');\n          clauseBlock.statementConnection_ =\n            inputDo && inputDo.connection!.targetConnection;\n          break;\n        }\n        default:\n          throw TypeError('Unknown block type: ' + clauseBlock.type);\n      }\n      clauseBlock = clauseBlock.getNextBlock() as ClauseBlock | null;\n    }\n  },\n  /**\n   * Reconstructs the block with all child blocks attached.\n   */\n  rebuildShape_: function (this: IfBlock) {\n    const valueConnections: Array<Connection | null> = [null];\n    const statementConnections: Array<Connection | null> = [null];\n    let elseStatementConnection: Connection | null = null;\n\n    if (this.getInput('ELSE')) {\n      elseStatementConnection =\n        this.getInput('ELSE')!.connection!.targetConnection;\n    }\n    for (let i = 1; this.getInput('IF' + i); i++) {\n      const inputIf = this.getInput('IF' + i);\n      const inputDo = this.getInput('DO' + i);\n      valueConnections.push(inputIf!.connection!.targetConnection);\n      statementConnections.push(inputDo!.connection!.targetConnection);\n    }\n    this.updateShape_();\n    this.reconnectChildBlocks_(\n      valueConnections,\n      statementConnections,\n      elseStatementConnection,\n    );\n  },\n  /**\n   * Modify this block to have the correct number of inputs.\n   *\n   * @internal\n   */\n  updateShape_: function (this: IfBlock) {\n    // Delete everything.\n    if (this.getInput('ELSE')) {\n      this.removeInput('ELSE');\n    }\n    for (let i = 1; this.getInput('IF' + i); i++) {\n      this.removeInput('IF' + i);\n      this.removeInput('DO' + i);\n    }\n    // Rebuild block.\n    for (let i = 1; i <= this.elseifCount_; i++) {\n      this.appendValueInput('IF' + i)\n        .setCheck('Boolean')\n        .appendField(Msg['CONTROLS_IF_MSG_ELSEIF']);\n      this.appendStatementInput('DO' + i).appendField(\n        Msg['CONTROLS_IF_MSG_THEN'],\n      );\n    }\n    if (this.elseCount_) {\n      this.appendStatementInput('ELSE').appendField(\n        Msg['CONTROLS_IF_MSG_ELSE'],\n      );\n    }\n  },\n  /**\n   * Reconnects child blocks.\n   *\n   * @param valueConnections 1-based array of value connections for\n   *     'if' input.  Value at index [0] ignored.\n   * @param statementConnections 1-based array of statement\n   *     connections for 'do' input.  Value at index [0] ignored.\n   * @param elseStatementConnection Statement connection for else input.\n   */\n  reconnectChildBlocks_: function (\n    this: IfBlock,\n    valueConnections: Array<Connection | null>,\n    statementConnections: Array<Connection | null>,\n    elseStatementConnection: Connection | null,\n  ) {\n    for (let i = 1; i <= this.elseifCount_; i++) {\n      valueConnections[i]?.reconnect(this, 'IF' + i);\n      statementConnections[i]?.reconnect(this, 'DO' + i);\n    }\n    elseStatementConnection?.reconnect(this, 'ELSE');\n  },\n};\n\nExtensions.registerMutator(\n  'controls_if_mutator',\n  CONTROLS_IF_MUTATOR_MIXIN,\n  null as unknown as undefined, // TODO(#6920)\n  ['controls_if_elseif', 'controls_if_else'],\n);\n\n/**\n * \"controls_if\" extension function. Adds mutator, shape updating methods,\n * and dynamic tooltip to \"controls_if\" blocks.\n */\nconst CONTROLS_IF_TOOLTIP_EXTENSION = function (this: IfBlock) {\n  this.setTooltip(\n    function (this: IfBlock) {\n      if (!this.elseifCount_ && !this.elseCount_) {\n        return Msg['CONTROLS_IF_TOOLTIP_1'];\n      } else if (!this.elseifCount_ && this.elseCount_) {\n        return Msg['CONTROLS_IF_TOOLTIP_2'];\n      } else if (this.elseifCount_ && !this.elseCount_) {\n        return Msg['CONTROLS_IF_TOOLTIP_3'];\n      } else if (this.elseifCount_ && this.elseCount_) {\n        return Msg['CONTROLS_IF_TOOLTIP_4'];\n      }\n      return '';\n    }.bind(this),\n  );\n};\n\nExtensions.register('controls_if_tooltip', CONTROLS_IF_TOOLTIP_EXTENSION);\n\n/** Type of a block that has LOGIC_COMPARE_ONCHANGE_MIXIN */\ntype CompareBlock = Block & CompareMixin;\ninterface CompareMixin extends CompareMixinType {\n  prevBlocks_?: Array<Block | null>;\n}\ntype CompareMixinType = typeof LOGIC_COMPARE_ONCHANGE_MIXIN;\n\n/**\n * Adds dynamic type validation for the left and right sides of a\n * logic_compare block.\n */\nconst LOGIC_COMPARE_ONCHANGE_MIXIN = {\n  /**\n   * Called whenever anything on the workspace changes.\n   * Prevent mismatched types from being compared.\n   *\n   * @param e Change event.\n   */\n  onchange: function (this: CompareBlock, e: AbstractEvent) {\n    if (!this.prevBlocks_) {\n      this.prevBlocks_ = [null, null];\n    }\n\n    const blockA = this.getInputTargetBlock('A');\n    const blockB = this.getInputTargetBlock('B');\n    // Disconnect blocks that existed prior to this change if they don't\n    // match.\n    if (\n      blockA &&\n      blockB &&\n      !this.workspace.connectionChecker.doTypeChecks(\n        blockA.outputConnection!,\n        blockB.outputConnection!,\n      )\n    ) {\n      // Mismatch between two inputs.  Revert the block connections,\n      // bumping away the newly connected block(s).\n      Events.setGroup(e.group);\n      const prevA = this.prevBlocks_[0];\n      if (prevA !== blockA) {\n        blockA.unplug();\n        if (prevA && !prevA.isDisposed() && !prevA.isShadow()) {\n          // The shadow block is automatically replaced during unplug().\n          this.getInput('A')!.connection!.connect(prevA.outputConnection!);\n        }\n      }\n      const prevB = this.prevBlocks_[1];\n      if (prevB !== blockB) {\n        blockB.unplug();\n        if (prevB && !prevB.isDisposed() && !prevB.isShadow()) {\n          // The shadow block is automatically replaced during unplug().\n          this.getInput('B')!.connection!.connect(prevB.outputConnection!);\n        }\n      }\n      this.bumpNeighbours();\n      Events.setGroup(false);\n    }\n    this.prevBlocks_[0] = this.getInputTargetBlock('A');\n    this.prevBlocks_[1] = this.getInputTargetBlock('B');\n  },\n};\n\n/**\n * \"logic_compare\" extension function. Adds type left and right side type\n * checking to \"logic_compare\" blocks.\n */\nconst LOGIC_COMPARE_EXTENSION = function (this: CompareBlock) {\n  // Add onchange handler to ensure types are compatible.\n  this.mixin(LOGIC_COMPARE_ONCHANGE_MIXIN);\n};\n\nExtensions.register('logic_compare', LOGIC_COMPARE_EXTENSION);\n\n/** Type of a block that has LOGIC_TERNARY_ONCHANGE_MIXIN */\ntype TernaryBlock = Block & TernaryMixin;\ninterface TernaryMixin extends TernaryMixinType {}\ntype TernaryMixinType = typeof LOGIC_TERNARY_ONCHANGE_MIXIN;\n\n/**\n * Adds type coordination between inputs and output.\n */\nconst LOGIC_TERNARY_ONCHANGE_MIXIN = {\n  prevParentConnection_: null as Connection | null,\n\n  /**\n   * Called whenever anything on the workspace changes.\n   * Prevent mismatched types.\n   */\n  onchange: function (this: TernaryBlock, e: AbstractEvent) {\n    const blockA = this.getInputTargetBlock('THEN');\n    const blockB = this.getInputTargetBlock('ELSE');\n    const parentConnection = this.outputConnection!.targetConnection;\n    // Disconnect blocks that existed prior to this change if they don't\n    // match.\n    if ((blockA || blockB) && parentConnection) {\n      for (let i = 0; i < 2; i++) {\n        const block = i === 1 ? blockA : blockB;\n        if (\n          block &&\n          !block.workspace.connectionChecker.doTypeChecks(\n            block.outputConnection!,\n            parentConnection,\n          )\n        ) {\n          // Ensure that any disconnections are grouped with the causing\n          // event.\n          Events.setGroup(e.group);\n          if (parentConnection === this.prevParentConnection_) {\n            this.unplug();\n            parentConnection.getSourceBlock().bumpNeighbours();\n          } else {\n            block.unplug();\n            block.bumpNeighbours();\n          }\n          Events.setGroup(false);\n        }\n      }\n    }\n    this.prevParentConnection_ = parentConnection;\n  },\n};\n\nExtensions.registerMixin('logic_ternary', LOGIC_TERNARY_ONCHANGE_MIXIN);\n\n// Register provided blocks.\ndefineBlocks(blocks);\n", "/**\n * @license\n * Copyright 2012 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n// Former goog.module ID: Blockly.libraryBlocks.lists\n\nimport type {Block} from '../core/block.js';\nimport type {BlockSvg} from '../core/block_svg.js';\nimport {\n  createBlockDefinitionsFromJsonArray,\n  defineBlocks,\n} from '../core/common.js';\nimport type {Connection} from '../core/connection.js';\nimport '../core/field_dropdown.js';\nimport type {FieldDropdown} from '../core/field_dropdown.js';\nimport * as fieldRegistry from '../core/field_registry.js';\nimport {MutatorIcon} from '../core/icons/mutator_icon.js';\nimport {Align} from '../core/inputs/align.js';\nimport {ValueInput} from '../core/inputs/value_input.js';\nimport {Msg} from '../core/msg.js';\nimport * as xmlUtils from '../core/utils/xml.js';\nimport type {Workspace} from '../core/workspace.js';\n\n/**\n * A dictionary of the block definitions provided by this module.\n */\nexport const blocks = createBlockDefinitionsFromJsonArray([\n  // Block for creating an empty list\n  // The 'list_create_with' block is preferred as it is more flexible.\n  // <block type=\"lists_create_with\">\n  //   <mutation items=\"0\"></mutation>\n  // </block>\n  {\n    'type': 'lists_create_empty',\n    'message0': '%{BKY_LISTS_CREATE_EMPTY_TITLE}',\n    'output': 'Array',\n    'style': 'list_blocks',\n    'tooltip': '%{BKY_LISTS_CREATE_EMPTY_TOOLTIP}',\n    'helpUrl': '%{BKY_LISTS_CREATE_EMPTY_HELPURL}',\n  },\n  // Block for creating a list with one element repeated.\n  {\n    'type': 'lists_repeat',\n    'message0': '%{BKY_LISTS_REPEAT_TITLE}',\n    'args0': [\n      {\n        'type': 'input_value',\n        'name': 'ITEM',\n      },\n      {\n        'type': 'input_value',\n        'name': 'NUM',\n        'check': 'Number',\n      },\n    ],\n    'output': 'Array',\n    'style': 'list_blocks',\n    'tooltip': '%{BKY_LISTS_REPEAT_TOOLTIP}',\n    'helpUrl': '%{BKY_LISTS_REPEAT_HELPURL}',\n  },\n  // Block for reversing a list.\n  {\n    'type': 'lists_reverse',\n    'message0': '%{BKY_LISTS_REVERSE_MESSAGE0}',\n    'args0': [\n      {\n        'type': 'input_value',\n        'name': 'LIST',\n        'check': 'Array',\n      },\n    ],\n    'output': 'Array',\n    'inputsInline': true,\n    'style': 'list_blocks',\n    'tooltip': '%{BKY_LISTS_REVERSE_TOOLTIP}',\n    'helpUrl': '%{BKY_LISTS_REVERSE_HELPURL}',\n  },\n  // Block for checking if a list is empty\n  {\n    'type': 'lists_isEmpty',\n    'message0': '%{BKY_LISTS_ISEMPTY_TITLE}',\n    'args0': [\n      {\n        'type': 'input_value',\n        'name': 'VALUE',\n        'check': ['String', 'Array'],\n      },\n    ],\n    'output': 'Boolean',\n    'style': 'list_blocks',\n    'tooltip': '%{BKY_LISTS_ISEMPTY_TOOLTIP}',\n    'helpUrl': '%{BKY_LISTS_ISEMPTY_HELPURL}',\n  },\n  // Block for getting the list length\n  {\n    'type': 'lists_length',\n    'message0': '%{BKY_LISTS_LENGTH_TITLE}',\n    'args0': [\n      {\n        'type': 'input_value',\n        'name': 'VALUE',\n        'check': ['String', 'Array'],\n      },\n    ],\n    'output': 'Number',\n    'style': 'list_blocks',\n    'tooltip': '%{BKY_LISTS_LENGTH_TOOLTIP}',\n    'helpUrl': '%{BKY_LISTS_LENGTH_HELPURL}',\n  },\n]);\n\n/**\n * Type of a 'lists_create_with' block.\n *\n * @internal\n */\nexport type CreateWithBlock = Block & ListCreateWithMixin;\ninterface ListCreateWithMixin extends ListCreateWithMixinType {\n  itemCount_: number;\n}\ntype ListCreateWithMixinType = typeof LISTS_CREATE_WITH;\n\nconst LISTS_CREATE_WITH = {\n  /**\n   * Block for creating a list with any number of elements of any type.\n   */\n  init: function (this: CreateWithBlock) {\n    this.setHelpUrl(Msg['LISTS_CREATE_WITH_HELPURL']);\n    this.setStyle('list_blocks');\n    this.itemCount_ = 3;\n    this.updateShape_();\n    this.setOutput(true, 'Array');\n    this.setMutator(\n      new MutatorIcon(['lists_create_with_item'], this as unknown as BlockSvg),\n    ); // BUG(#6905)\n    this.setTooltip(Msg['LISTS_CREATE_WITH_TOOLTIP']);\n  },\n  /**\n   * Create XML to represent list inputs.\n   * Backwards compatible serialization implementation.\n   */\n  mutationToDom: function (this: CreateWithBlock): Element {\n    const container = xmlUtils.createElement('mutation');\n    container.setAttribute('items', String(this.itemCount_));\n    return container;\n  },\n  /**\n   * Parse XML to restore the list inputs.\n   * Backwards compatible serialization implementation.\n   *\n   * @param xmlElement XML storage element.\n   */\n  domToMutation: function (this: CreateWithBlock, xmlElement: Element) {\n    const items = xmlElement.getAttribute('items');\n    if (!items) throw new TypeError('element did not have items');\n    this.itemCount_ = parseInt(items, 10);\n    this.updateShape_();\n  },\n  /**\n   * Returns the state of this block as a JSON serializable object.\n   *\n   * @returns The state of this block, ie the item count.\n   */\n  saveExtraState: function (this: CreateWithBlock): {itemCount: number} {\n    return {\n      'itemCount': this.itemCount_,\n    };\n  },\n  /**\n   * Applies the given state to this block.\n   *\n   * @param state The state to apply to this block, ie the item count.\n   */\n  loadExtraState: function (this: CreateWithBlock, state: AnyDuringMigration) {\n    this.itemCount_ = state['itemCount'];\n    this.updateShape_();\n  },\n  /**\n   * Populate the mutator's dialog with this block's components.\n   *\n   * @param workspace Mutator's workspace.\n   * @returns Root block in mutator.\n   */\n  decompose: function (\n    this: CreateWithBlock,\n    workspace: Workspace,\n  ): ContainerBlock {\n    const containerBlock = workspace.newBlock(\n      'lists_create_with_container',\n    ) as ContainerBlock;\n    (containerBlock as BlockSvg).initSvg();\n    let connection = containerBlock.getInput('STACK')!.connection;\n    for (let i = 0; i < this.itemCount_; i++) {\n      const itemBlock = workspace.newBlock(\n        'lists_create_with_item',\n      ) as ItemBlock;\n      (itemBlock as BlockSvg).initSvg();\n      if (!itemBlock.previousConnection) {\n        throw new Error('itemBlock has no previousConnection');\n      }\n      connection!.connect(itemBlock.previousConnection);\n      connection = itemBlock.nextConnection;\n    }\n    return containerBlock;\n  },\n  /**\n   * Reconfigure this block based on the mutator dialog's components.\n   *\n   * @param containerBlock Root block in mutator.\n   */\n  compose: function (this: CreateWithBlock, containerBlock: Block) {\n    let itemBlock: ItemBlock | null = containerBlock.getInputTargetBlock(\n      'STACK',\n    ) as ItemBlock;\n    // Count number of inputs.\n    const connections: Connection[] = [];\n    while (itemBlock) {\n      if (itemBlock.isInsertionMarker()) {\n        itemBlock = itemBlock.getNextBlock() as ItemBlock | null;\n        continue;\n      }\n      connections.push(itemBlock.valueConnection_ as Connection);\n      itemBlock = itemBlock.getNextBlock() as ItemBlock | null;\n    }\n    // Disconnect any children that don't belong.\n    for (let i = 0; i < this.itemCount_; i++) {\n      const connection = this.getInput('ADD' + i)!.connection!.targetConnection;\n      if (connection && !connections.includes(connection)) {\n        connection.disconnect();\n      }\n    }\n    this.itemCount_ = connections.length;\n    this.updateShape_();\n    // Reconnect any child blocks.\n    for (let i = 0; i < this.itemCount_; i++) {\n      connections[i]?.reconnect(this, 'ADD' + i);\n    }\n  },\n  /**\n   * Store pointers to any connected child blocks.\n   *\n   * @param containerBlock Root block in mutator.\n   */\n  saveConnections: function (this: CreateWithBlock, containerBlock: Block) {\n    let itemBlock: ItemBlock | null = containerBlock.getInputTargetBlock(\n      'STACK',\n    ) as ItemBlock;\n    let i = 0;\n    while (itemBlock) {\n      if (itemBlock.isInsertionMarker()) {\n        itemBlock = itemBlock.getNextBlock() as ItemBlock | null;\n        continue;\n      }\n      const input = this.getInput('ADD' + i);\n      itemBlock.valueConnection_ = input?.connection!\n        .targetConnection as Connection;\n      itemBlock = itemBlock.getNextBlock() as ItemBlock | null;\n      i++;\n    }\n  },\n  /**\n   * Modify this block to have the correct number of inputs.\n   */\n  updateShape_: function (this: CreateWithBlock) {\n    if (this.itemCount_ && this.getInput('EMPTY')) {\n      this.removeInput('EMPTY');\n    } else if (!this.itemCount_ && !this.getInput('EMPTY')) {\n      this.appendDummyInput('EMPTY').appendField(\n        Msg['LISTS_CREATE_EMPTY_TITLE'],\n      );\n    }\n    // Add new inputs.\n    for (let i = 0; i < this.itemCount_; i++) {\n      if (!this.getInput('ADD' + i)) {\n        const input = this.appendValueInput('ADD' + i).setAlign(Align.RIGHT);\n        if (i === 0) {\n          input.appendField(Msg['LISTS_CREATE_WITH_INPUT_WITH']);\n        }\n      }\n    }\n    // Remove deleted inputs.\n    for (let i = this.itemCount_; this.getInput('ADD' + i); i++) {\n      this.removeInput('ADD' + i);\n    }\n  },\n};\nblocks['lists_create_with'] = LISTS_CREATE_WITH;\n\n/** Type for a 'lists_create_with_container' block. */\ntype ContainerBlock = Block & ContainerMutator;\ninterface ContainerMutator extends ContainerMutatorType {}\ntype ContainerMutatorType = typeof LISTS_CREATE_WITH_CONTAINER;\n\nconst LISTS_CREATE_WITH_CONTAINER = {\n  /**\n   * Mutator block for list container.\n   */\n  init: function (this: ContainerBlock) {\n    this.setStyle('list_blocks');\n    this.appendDummyInput().appendField(\n      Msg['LISTS_CREATE_WITH_CONTAINER_TITLE_ADD'],\n    );\n    this.appendStatementInput('STACK');\n    this.setTooltip(Msg['LISTS_CREATE_WITH_CONTAINER_TOOLTIP']);\n    this.contextMenu = false;\n  },\n};\nblocks['lists_create_with_container'] = LISTS_CREATE_WITH_CONTAINER;\n\n/** Type for a 'lists_create_with_item' block. */\ntype ItemBlock = Block & ItemMutator;\ninterface ItemMutator extends ItemMutatorType {\n  valueConnection_?: Connection;\n}\ntype ItemMutatorType = typeof LISTS_CREATE_WITH_ITEM;\n\nconst LISTS_CREATE_WITH_ITEM = {\n  /**\n   * Mutator block for adding items.\n   */\n  init: function (this: ItemBlock) {\n    this.setStyle('list_blocks');\n    this.appendDummyInput().appendField(Msg['LISTS_CREATE_WITH_ITEM_TITLE']);\n    this.setPreviousStatement(true);\n    this.setNextStatement(true);\n    this.setTooltip(Msg['LISTS_CREATE_WITH_ITEM_TOOLTIP']);\n    this.contextMenu = false;\n  },\n};\nblocks['lists_create_with_item'] = LISTS_CREATE_WITH_ITEM;\n\n/** Type for a 'lists_indexOf' block. */\ntype IndexOfBlock = Block & IndexOfMutator;\ninterface IndexOfMutator extends IndexOfMutatorType {}\ntype IndexOfMutatorType = typeof LISTS_INDEXOF;\n\nconst LISTS_INDEXOF = {\n  /**\n   * Block for finding an item in the list.\n   */\n  init: function (this: IndexOfBlock) {\n    const OPERATORS = [\n      [Msg['LISTS_INDEX_OF_FIRST'], 'FIRST'],\n      [Msg['LISTS_INDEX_OF_LAST'], 'LAST'],\n    ];\n    this.setHelpUrl(Msg['LISTS_INDEX_OF_HELPURL']);\n    this.setStyle('list_blocks');\n    this.setOutput(true, 'Number');\n    this.appendValueInput('VALUE')\n      .setCheck('Array')\n      .appendField(Msg['LISTS_INDEX_OF_INPUT_IN_LIST']);\n    const operatorsDropdown = fieldRegistry.fromJson({\n      type: 'field_dropdown',\n      options: OPERATORS,\n    });\n    if (!operatorsDropdown) throw new Error('field_dropdown not found');\n    this.appendValueInput('FIND').appendField(operatorsDropdown, 'END');\n    this.setInputsInline(true);\n    this.setTooltip(() => {\n      return Msg['LISTS_INDEX_OF_TOOLTIP'].replace(\n        '%1',\n        this.workspace.options.oneBasedIndex ? '0' : '-1',\n      );\n    });\n  },\n};\nblocks['lists_indexOf'] = LISTS_INDEXOF;\n\n/** Type for a 'lists_getIndex' block. */\ntype GetIndexBlock = Block & GetIndexMutator;\ninterface GetIndexMutator extends GetIndexMutatorType {\n  WHERE_OPTIONS: Array<[string, string]>;\n}\ntype GetIndexMutatorType = typeof LISTS_GETINDEX;\n\nconst LISTS_GETINDEX = {\n  /**\n   * Block for getting element at index.\n   */\n  init: function (this: GetIndexBlock) {\n    const MODE = [\n      [Msg['LISTS_GET_INDEX_GET'], 'GET'],\n      [Msg['LISTS_GET_INDEX_GET_REMOVE'], 'GET_REMOVE'],\n      [Msg['LISTS_GET_INDEX_REMOVE'], 'REMOVE'],\n    ];\n    this.WHERE_OPTIONS = [\n      [Msg['LISTS_GET_INDEX_FROM_START'], 'FROM_START'],\n      [Msg['LISTS_GET_INDEX_FROM_END'], 'FROM_END'],\n      [Msg['LISTS_GET_INDEX_FIRST'], 'FIRST'],\n      [Msg['LISTS_GET_INDEX_LAST'], 'LAST'],\n      [Msg['LISTS_GET_INDEX_RANDOM'], 'RANDOM'],\n    ];\n    this.setHelpUrl(Msg['LISTS_GET_INDEX_HELPURL']);\n    this.setStyle('list_blocks');\n    const modeMenu = fieldRegistry.fromJson({\n      type: 'field_dropdown',\n      options: MODE,\n    }) as FieldDropdown;\n    modeMenu.setValidator(\n      /** @param value The input value. */\n      function (this: FieldDropdown, value: string) {\n        const isStatement = value === 'REMOVE';\n        (this.getSourceBlock() as GetIndexBlock).updateStatement_(isStatement);\n        return undefined;\n      },\n    );\n    this.appendValueInput('VALUE')\n      .setCheck('Array')\n      .appendField(Msg['LISTS_GET_INDEX_INPUT_IN_LIST']);\n    this.appendDummyInput()\n      .appendField(modeMenu, 'MODE')\n      .appendField('', 'SPACE');\n    const menu = fieldRegistry.fromJson({\n      type: 'field_dropdown',\n      options: this.WHERE_OPTIONS,\n    }) as FieldDropdown;\n    menu.setValidator(\n      /** @param value The input value. */\n      function (this: FieldDropdown, value: string) {\n        const oldValue: string | null = this.getValue();\n        const oldAt = oldValue === 'FROM_START' || oldValue === 'FROM_END';\n        const newAt = value === 'FROM_START' || value === 'FROM_END';\n        if (newAt !== oldAt) {\n          const block = this.getSourceBlock() as GetIndexBlock;\n          block.updateAt_(newAt);\n        }\n        return undefined;\n      },\n    );\n    this.appendDummyInput().appendField(menu, 'WHERE');\n    this.appendDummyInput('AT');\n    if (Msg['LISTS_GET_INDEX_TAIL']) {\n      this.appendDummyInput('TAIL').appendField(Msg['LISTS_GET_INDEX_TAIL']);\n    }\n    this.setInputsInline(true);\n    this.setOutput(true);\n    this.updateAt_(true);\n    this.setTooltip(() => {\n      const mode = this.getFieldValue('MODE');\n      const where = this.getFieldValue('WHERE');\n      let tooltip = '';\n      switch (mode + ' ' + where) {\n        case 'GET FROM_START':\n        case 'GET FROM_END':\n          tooltip = Msg['LISTS_GET_INDEX_TOOLTIP_GET_FROM'];\n          break;\n        case 'GET FIRST':\n          tooltip = Msg['LISTS_GET_INDEX_TOOLTIP_GET_FIRST'];\n          break;\n        case 'GET LAST':\n          tooltip = Msg['LISTS_GET_INDEX_TOOLTIP_GET_LAST'];\n          break;\n        case 'GET RANDOM':\n          tooltip = Msg['LISTS_GET_INDEX_TOOLTIP_GET_RANDOM'];\n          break;\n        case 'GET_REMOVE FROM_START':\n        case 'GET_REMOVE FROM_END':\n          tooltip = Msg['LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM'];\n          break;\n        case 'GET_REMOVE FIRST':\n          tooltip = Msg['LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST'];\n          break;\n        case 'GET_REMOVE LAST':\n          tooltip = Msg['LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST'];\n          break;\n        case 'GET_REMOVE RANDOM':\n          tooltip = Msg['LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM'];\n          break;\n        case 'REMOVE FROM_START':\n        case 'REMOVE FROM_END':\n          tooltip = Msg['LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM'];\n          break;\n        case 'REMOVE FIRST':\n          tooltip = Msg['LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST'];\n          break;\n        case 'REMOVE LAST':\n          tooltip = Msg['LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST'];\n          break;\n        case 'REMOVE RANDOM':\n          tooltip = Msg['LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM'];\n          break;\n      }\n      if (where === 'FROM_START' || where === 'FROM_END') {\n        const msg =\n          where === 'FROM_START'\n            ? Msg['LISTS_INDEX_FROM_START_TOOLTIP']\n            : Msg['LISTS_INDEX_FROM_END_TOOLTIP'];\n        tooltip +=\n          '  ' +\n          msg.replace('%1', this.workspace.options.oneBasedIndex ? '#1' : '#0');\n      }\n      return tooltip;\n    });\n  },\n  /**\n   * Create XML to represent whether the block is a statement or a value.\n   * Also represent whether there is an 'AT' input.\n   *\n   * @returns XML storage element.\n   */\n  mutationToDom: function (this: GetIndexBlock): Element {\n    const container = xmlUtils.createElement('mutation');\n    const isStatement = !this.outputConnection;\n    container.setAttribute('statement', String(isStatement));\n    const isAt = this.getInput('AT') instanceof ValueInput;\n    container.setAttribute('at', String(isAt));\n    return container;\n  },\n  /**\n   * Parse XML to restore the 'AT' input.\n   *\n   * @param xmlElement XML storage element.\n   */\n  domToMutation: function (this: GetIndexBlock, xmlElement: Element) {\n    // Note: Until January 2013 this block did not have mutations,\n    // so 'statement' defaults to false and 'at' defaults to true.\n    const isStatement = xmlElement.getAttribute('statement') === 'true';\n    this.updateStatement_(isStatement);\n    const isAt = xmlElement.getAttribute('at') !== 'false';\n    this.updateAt_(isAt);\n  },\n  /**\n   * Returns the state of this block as a JSON serializable object.\n   * Returns null for efficiency if no state is needed (not a statement)\n   *\n   * @returns The state of this block, ie whether it's a statement.\n   */\n  saveExtraState: function (this: GetIndexBlock): {\n    isStatement: boolean;\n  } | null {\n    if (!this.outputConnection) {\n      return {\n        isStatement: true,\n      };\n    }\n    return null;\n  },\n\n  /**\n   * Applies the given state to this block.\n   *\n   * @param state The state to apply to this block, ie whether it's a\n   *     statement.\n   */\n  loadExtraState: function (this: GetIndexBlock, state: AnyDuringMigration) {\n    if (state['isStatement']) {\n      this.updateStatement_(true);\n    } else if (typeof state === 'string') {\n      // backward compatible for json serialised mutations\n      this.domToMutation(xmlUtils.textToDom(state));\n    }\n  },\n\n  /**\n   * Switch between a value block and a statement block.\n   *\n   * @param newStatement True if the block should be a statement.\n   *     False if the block should be a value.\n   */\n  updateStatement_: function (this: GetIndexBlock, newStatement: boolean) {\n    const oldStatement = !this.outputConnection;\n    if (newStatement !== oldStatement) {\n      // TODO(#6920): The .unplug only has one parameter.\n      (this.unplug as (arg0?: boolean, arg1?: boolean) => void)(true, true);\n      if (newStatement) {\n        this.setOutput(false);\n        this.setPreviousStatement(true);\n        this.setNextStatement(true);\n      } else {\n        this.setPreviousStatement(false);\n        this.setNextStatement(false);\n        this.setOutput(true);\n      }\n    }\n  },\n  /**\n   * Create or delete an input for the numeric index.\n   *\n   * @param isAt True if the input should exist.\n   */\n  updateAt_: function (this: GetIndexBlock, isAt: boolean) {\n    // Destroy old 'AT' and 'ORDINAL' inputs.\n    this.removeInput('AT');\n    this.removeInput('ORDINAL', true);\n    // Create either a value 'AT' input or a dummy input.\n    if (isAt) {\n      this.appendValueInput('AT').setCheck('Number');\n      if (Msg['ORDINAL_NUMBER_SUFFIX']) {\n        this.appendDummyInput('ORDINAL').appendField(\n          Msg['ORDINAL_NUMBER_SUFFIX'],\n        );\n      }\n    } else {\n      this.appendDummyInput('AT');\n    }\n    if (Msg['LISTS_GET_INDEX_TAIL']) {\n      this.moveInputBefore('TAIL', null);\n    }\n  },\n};\nblocks['lists_getIndex'] = LISTS_GETINDEX;\n\n/** Type for a 'lists_setIndex' block. */\ntype SetIndexBlock = Block & SetIndexMutator;\ninterface SetIndexMutator extends SetIndexMutatorType {\n  WHERE_OPTIONS: Array<[string, string]>;\n}\ntype SetIndexMutatorType = typeof LISTS_SETINDEX;\n\nconst LISTS_SETINDEX = {\n  /**\n   * Block for setting the element at index.\n   */\n  init: function (this: SetIndexBlock) {\n    const MODE = [\n      [Msg['LISTS_SET_INDEX_SET'], 'SET'],\n      [Msg['LISTS_SET_INDEX_INSERT'], 'INSERT'],\n    ];\n    this.WHERE_OPTIONS = [\n      [Msg['LISTS_GET_INDEX_FROM_START'], 'FROM_START'],\n      [Msg['LISTS_GET_INDEX_FROM_END'], 'FROM_END'],\n      [Msg['LISTS_GET_INDEX_FIRST'], 'FIRST'],\n      [Msg['LISTS_GET_INDEX_LAST'], 'LAST'],\n      [Msg['LISTS_GET_INDEX_RANDOM'], 'RANDOM'],\n    ];\n    this.setHelpUrl(Msg['LISTS_SET_INDEX_HELPURL']);\n    this.setStyle('list_blocks');\n    this.appendValueInput('LIST')\n      .setCheck('Array')\n      .appendField(Msg['LISTS_SET_INDEX_INPUT_IN_LIST']);\n    const operationDropdown = fieldRegistry.fromJson({\n      type: 'field_dropdown',\n      options: MODE,\n    }) as FieldDropdown;\n    this.appendDummyInput()\n      .appendField(operationDropdown, 'MODE')\n      .appendField('', 'SPACE');\n    const menu = fieldRegistry.fromJson({\n      type: 'field_dropdown',\n      options: this.WHERE_OPTIONS,\n    }) as FieldDropdown;\n    menu.setValidator(\n      /** @param value The input value. */\n      function (this: FieldDropdown, value: string) {\n        const oldValue: string | null = this.getValue();\n        const oldAt = oldValue === 'FROM_START' || oldValue === 'FROM_END';\n        const newAt = value === 'FROM_START' || value === 'FROM_END';\n        if (newAt !== oldAt) {\n          const block = this.getSourceBlock() as SetIndexBlock;\n          block.updateAt_(newAt);\n        }\n        return undefined;\n      },\n    );\n    this.appendDummyInput().appendField(menu, 'WHERE');\n    this.appendDummyInput('AT');\n    this.appendValueInput('TO').appendField(Msg['LISTS_SET_INDEX_INPUT_TO']);\n    this.setInputsInline(true);\n    this.setPreviousStatement(true);\n    this.setNextStatement(true);\n    this.setTooltip(Msg['LISTS_SET_INDEX_TOOLTIP']);\n    this.updateAt_(true);\n    this.setTooltip(() => {\n      const mode = this.getFieldValue('MODE');\n      const where = this.getFieldValue('WHERE');\n      let tooltip = '';\n      switch (mode + ' ' + where) {\n        case 'SET FROM_START':\n        case 'SET FROM_END':\n          tooltip = Msg['LISTS_SET_INDEX_TOOLTIP_SET_FROM'];\n          break;\n        case 'SET FIRST':\n          tooltip = Msg['LISTS_SET_INDEX_TOOLTIP_SET_FIRST'];\n          break;\n        case 'SET LAST':\n          tooltip = Msg['LISTS_SET_INDEX_TOOLTIP_SET_LAST'];\n          break;\n        case 'SET RANDOM':\n          tooltip = Msg['LISTS_SET_INDEX_TOOLTIP_SET_RANDOM'];\n          break;\n        case 'INSERT FROM_START':\n        case 'INSERT FROM_END':\n          tooltip = Msg['LISTS_SET_INDEX_TOOLTIP_INSERT_FROM'];\n          break;\n        case 'INSERT FIRST':\n          tooltip = Msg['LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST'];\n          break;\n        case 'INSERT LAST':\n          tooltip = Msg['LISTS_SET_INDEX_TOOLTIP_INSERT_LAST'];\n          break;\n        case 'INSERT RANDOM':\n          tooltip = Msg['LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM'];\n          break;\n      }\n      if (where === 'FROM_START' || where === 'FROM_END') {\n        tooltip +=\n          '  ' +\n          Msg['LISTS_INDEX_FROM_START_TOOLTIP'].replace(\n            '%1',\n            this.workspace.options.oneBasedIndex ? '#1' : '#0',\n          );\n      }\n      return tooltip;\n    });\n  },\n  /**\n   * Create XML to represent whether there is an 'AT' input.\n   *\n   * @returns XML storage element.\n   */\n  mutationToDom: function (this: SetIndexBlock): Element {\n    const container = xmlUtils.createElement('mutation');\n    const isAt = this.getInput('AT') instanceof ValueInput;\n    container.setAttribute('at', String(isAt));\n    return container;\n  },\n  /**\n   * Parse XML to restore the 'AT' input.\n   *\n   * @param xmlElement XML storage element.\n   */\n  domToMutation: function (this: SetIndexBlock, xmlElement: Element) {\n    // Note: Until January 2013 this block did not have mutations,\n    // so 'at' defaults to true.\n    const isAt = xmlElement.getAttribute('at') !== 'false';\n    this.updateAt_(isAt);\n  },\n\n  /**\n   * Returns the state of this block as a JSON serializable object.\n   * This block does not need to serialize any specific state as it is already\n   * encoded in the dropdown values, but must have an implementation to avoid\n   * the backward compatible XML mutations being serialized.\n   *\n   * @returns The state of this block.\n   */\n  saveExtraState: function (this: SetIndexBlock): null {\n    return null;\n  },\n\n  /**\n   * Applies the given state to this block.\n   * No extra state is needed or expected as it is already encoded in the\n   * dropdown values.\n   */\n  loadExtraState: function (this: SetIndexBlock) {},\n\n  /**\n   * Create or delete an input for the numeric index.\n   *\n   * @param isAt True if the input should exist.\n   */\n  updateAt_: function (this: SetIndexBlock, isAt: boolean) {\n    // Destroy old 'AT' and 'ORDINAL' input.\n    this.removeInput('AT');\n    this.removeInput('ORDINAL', true);\n    // Create either a value 'AT' input or a dummy input.\n    if (isAt) {\n      this.appendValueInput('AT').setCheck('Number');\n      if (Msg['ORDINAL_NUMBER_SUFFIX']) {\n        this.appendDummyInput('ORDINAL').appendField(\n          Msg['ORDINAL_NUMBER_SUFFIX'],\n        );\n      }\n    } else {\n      this.appendDummyInput('AT');\n    }\n    this.moveInputBefore('AT', 'TO');\n    if (this.getInput('ORDINAL')) {\n      this.moveInputBefore('ORDINAL', 'TO');\n    }\n  },\n};\nblocks['lists_setIndex'] = LISTS_SETINDEX;\n\n/** Type for a 'lists_getSublist' block. */\ntype GetSublistBlock = Block & GetSublistMutator;\ninterface GetSublistMutator extends GetSublistMutatorType {\n  WHERE_OPTIONS_1: Array<[string, string]>;\n  WHERE_OPTIONS_2: Array<[string, string]>;\n}\ntype GetSublistMutatorType = typeof LISTS_GETSUBLIST;\n\nconst LISTS_GETSUBLIST = {\n  /**\n   * Block for getting sublist.\n   */\n  init: function (this: GetSublistBlock) {\n    this['WHERE_OPTIONS_1'] = [\n      [Msg['LISTS_GET_SUBLIST_START_FROM_START'], 'FROM_START'],\n      [Msg['LISTS_GET_SUBLIST_START_FROM_END'], 'FROM_END'],\n      [Msg['LISTS_GET_SUBLIST_START_FIRST'], 'FIRST'],\n    ];\n    this['WHERE_OPTIONS_2'] = [\n      [Msg['LISTS_GET_SUBLIST_END_FROM_START'], 'FROM_START'],\n      [Msg['LISTS_GET_SUBLIST_END_FROM_END'], 'FROM_END'],\n      [Msg['LISTS_GET_SUBLIST_END_LAST'], 'LAST'],\n    ];\n    this.setHelpUrl(Msg['LISTS_GET_SUBLIST_HELPURL']);\n    this.setStyle('list_blocks');\n    this.appendValueInput('LIST')\n      .setCheck('Array')\n      .appendField(Msg['LISTS_GET_SUBLIST_INPUT_IN_LIST']);\n    const createMenu = (n: 1 | 2): FieldDropdown => {\n      const menu = fieldRegistry.fromJson({\n        type: 'field_dropdown',\n        options:\n          this[('WHERE_OPTIONS_' + n) as 'WHERE_OPTIONS_1' | 'WHERE_OPTIONS_2'],\n      }) as FieldDropdown;\n      menu.setValidator(\n        /** @param value The input value. */\n        function (this: FieldDropdown, value: string) {\n          const oldValue: string | null = this.getValue();\n          const oldAt = oldValue === 'FROM_START' || oldValue === 'FROM_END';\n          const newAt = value === 'FROM_START' || value === 'FROM_END';\n          if (newAt !== oldAt) {\n            const block = this.getSourceBlock() as GetSublistBlock;\n            block.updateAt_(n, newAt);\n          }\n          return undefined;\n        },\n      );\n      return menu;\n    };\n    this.appendDummyInput('WHERE1_INPUT').appendField(createMenu(1), 'WHERE1');\n    this.appendDummyInput('AT1');\n    this.appendDummyInput('WHERE2_INPUT').appendField(createMenu(2), 'WHERE2');\n    this.appendDummyInput('AT2');\n    if (Msg['LISTS_GET_SUBLIST_TAIL']) {\n      this.appendDummyInput('TAIL').appendField(Msg['LISTS_GET_SUBLIST_TAIL']);\n    }\n    this.setInputsInline(true);\n    this.setOutput(true, 'Array');\n    this.updateAt_(1, true);\n    this.updateAt_(2, true);\n    this.setTooltip(Msg['LISTS_GET_SUBLIST_TOOLTIP']);\n  },\n  /**\n   * Create XML to represent whether there are 'AT' inputs.\n   *\n   * @returns XML storage element.\n   */\n  mutationToDom: function (this: GetSublistBlock): Element {\n    const container = xmlUtils.createElement('mutation');\n    const isAt1 = this.getInput('AT1') instanceof ValueInput;\n    container.setAttribute('at1', String(isAt1));\n    const isAt2 = this.getInput('AT2') instanceof ValueInput;\n    container.setAttribute('at2', String(isAt2));\n    return container;\n  },\n  /**\n   * Parse XML to restore the 'AT' inputs.\n   *\n   * @param xmlElement XML storage element.\n   */\n  domToMutation: function (this: GetSublistBlock, xmlElement: Element) {\n    const isAt1 = xmlElement.getAttribute('at1') === 'true';\n    const isAt2 = xmlElement.getAttribute('at2') === 'true';\n    this.updateAt_(1, isAt1);\n    this.updateAt_(2, isAt2);\n  },\n\n  /**\n   * Returns the state of this block as a JSON serializable object.\n   * This block does not need to serialize any specific state as it is already\n   * encoded in the dropdown values, but must have an implementation to avoid\n   * the backward compatible XML mutations being serialized.\n   *\n   * @returns The state of this block.\n   */\n  saveExtraState: function (this: GetSublistBlock): null {\n    return null;\n  },\n\n  /**\n   * Applies the given state to this block.\n   * No extra state is needed or expected as it is already encoded in the\n   * dropdown values.\n   */\n  loadExtraState: function (this: GetSublistBlock) {},\n\n  /**\n   * Create or delete an input for a numeric index.\n   * This block has two such inputs, independent of each other.\n   *\n   * @param n Specify first or second input (1 or 2).\n   * @param isAt True if the input should exist.\n   */\n  updateAt_: function (this: GetSublistBlock, n: 1 | 2, isAt: boolean) {\n    // Create or delete an input for the numeric index.\n    // Destroy old 'AT' and 'ORDINAL' inputs.\n    this.removeInput('AT' + n);\n    this.removeInput('ORDINAL' + n, true);\n    // Create either a value 'AT' input or a dummy input.\n    if (isAt) {\n      this.appendValueInput('AT' + n).setCheck('Number');\n      if (Msg['ORDINAL_NUMBER_SUFFIX']) {\n        this.appendDummyInput('ORDINAL' + n).appendField(\n          Msg['ORDINAL_NUMBER_SUFFIX'],\n        );\n      }\n    } else {\n      this.appendDummyInput('AT' + n);\n    }\n    if (n === 1) {\n      this.moveInputBefore('AT1', 'WHERE2_INPUT');\n      if (this.getInput('ORDINAL1')) {\n        this.moveInputBefore('ORDINAL1', 'WHERE2_INPUT');\n      }\n    }\n    if (Msg['LISTS_GET_SUBLIST_TAIL']) {\n      this.moveInputBefore('TAIL', null);\n    }\n  },\n};\nblocks['lists_getSublist'] = LISTS_GETSUBLIST;\n\ntype SortBlock = Block | (typeof blocks)['lists_sort'];\n\nblocks['lists_sort'] = {\n  /**\n   * Block for sorting a list.\n   */\n  init: function (this: SortBlock) {\n    this.jsonInit({\n      'message0': '%{BKY_LISTS_SORT_TITLE}',\n      'args0': [\n        {\n          'type': 'field_dropdown',\n          'name': 'TYPE',\n          'options': [\n            ['%{BKY_LISTS_SORT_TYPE_NUMERIC}', 'NUMERIC'],\n            ['%{BKY_LISTS_SORT_TYPE_TEXT}', 'TEXT'],\n            ['%{BKY_LISTS_SORT_TYPE_IGNORECASE}', 'IGNORE_CASE'],\n          ],\n        },\n        {\n          'type': 'field_dropdown',\n          'name': 'DIRECTION',\n          'options': [\n            ['%{BKY_LISTS_SORT_ORDER_ASCENDING}', '1'],\n            ['%{BKY_LISTS_SORT_ORDER_DESCENDING}', '-1'],\n          ],\n        },\n        {\n          'type': 'input_value',\n          'name': 'LIST',\n          'check': 'Array',\n        },\n      ],\n      'output': 'Array',\n      'style': 'list_blocks',\n      'tooltip': '%{BKY_LISTS_SORT_TOOLTIP}',\n      'helpUrl': '%{BKY_LISTS_SORT_HELPURL}',\n    });\n  },\n};\n\ntype SplitBlock = Block | (typeof blocks)['lists_split'];\n\nblocks['lists_split'] = {\n  /**\n   * Block for splitting text into a list, or joining a list into text.\n   */\n  init: function (this: SplitBlock) {\n    const dropdown = fieldRegistry.fromJson({\n      type: 'field_dropdown',\n      options: [\n        [Msg['LISTS_SPLIT_LIST_FROM_TEXT'], 'SPLIT'],\n        [Msg['LISTS_SPLIT_TEXT_FROM_LIST'], 'JOIN'],\n      ],\n    });\n    if (!dropdown) throw new Error('field_dropdown not found');\n    dropdown.setValidator((newMode) => {\n      this.updateType_(newMode);\n    });\n    this.setHelpUrl(Msg['LISTS_SPLIT_HELPURL']);\n    this.setStyle('list_blocks');\n    this.appendValueInput('INPUT')\n      .setCheck('String')\n      .appendField(dropdown, 'MODE');\n    this.appendValueInput('DELIM')\n      .setCheck('String')\n      .appendField(Msg['LISTS_SPLIT_WITH_DELIMITER']);\n    this.setInputsInline(true);\n    this.setOutput(true, 'Array');\n    this.setTooltip(() => {\n      const mode = this.getFieldValue('MODE');\n      if (mode === 'SPLIT') {\n        return Msg['LISTS_SPLIT_TOOLTIP_SPLIT'];\n      } else if (mode === 'JOIN') {\n        return Msg['LISTS_SPLIT_TOOLTIP_JOIN'];\n      }\n      throw Error('Unknown mode: ' + mode);\n    });\n  },\n  /**\n   * Modify this block to have the correct input and output types.\n   *\n   * @param newMode Either 'SPLIT' or 'JOIN'.\n   */\n  updateType_: function (this: SplitBlock, newMode: string) {\n    const mode = this.getFieldValue('MODE');\n    if (mode !== newMode) {\n      const inputConnection = this.getInput('INPUT')!.connection;\n      inputConnection!.setShadowDom(null);\n      const inputBlock = inputConnection!.targetBlock();\n      // TODO(#6920): This is probably not needed; see details in bug.\n      if (inputBlock) {\n        inputConnection!.disconnect();\n        if (inputBlock.isShadow()) {\n          inputBlock.dispose(false);\n        } else {\n          this.bumpNeighbours();\n        }\n      }\n    }\n    if (newMode === 'SPLIT') {\n      this.outputConnection!.setCheck('Array');\n      this.getInput('INPUT')!.setCheck('String');\n    } else {\n      this.outputConnection!.setCheck('String');\n      this.getInput('INPUT')!.setCheck('Array');\n    }\n  },\n  /**\n   * Create XML to represent the input and output types.\n   *\n   * @returns XML storage element.\n   */\n  mutationToDom: function (this: SplitBlock): Element {\n    const container = xmlUtils.createElement('mutation');\n    container.setAttribute('mode', this.getFieldValue('MODE'));\n    return container;\n  },\n  /**\n   * Parse XML to restore the input and output types.\n   *\n   * @param xmlElement XML storage element.\n   */\n  domToMutation: function (this: SplitBlock, xmlElement: Element) {\n    this.updateType_(xmlElement.getAttribute('mode'));\n  },\n\n  /**\n   * Returns the state of this block as a JSON serializable object.\n   *\n   * @returns The state of this block.\n   */\n  saveExtraState: function (this: SplitBlock): {mode: string} {\n    return {'mode': this.getFieldValue('MODE')};\n  },\n\n  /**\n   * Applies the given state to this block.\n   */\n  loadExtraState: function (this: SplitBlock, state: {mode: string}) {\n    this.updateType_(state['mode']);\n  },\n};\n\n// Register provided blocks.\ndefineBlocks(blocks);\n", "/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n// Former goog.module ID: Blockly.libraryBlocks\n\nimport type {BlockDefinition} from '../core/blocks.js';\nimport * as lists from './lists.js';\nimport * as logic from './logic.js';\nimport * as loops from './loops.js';\nimport * as math from './math.js';\nimport * as procedures from './procedures.js';\nimport * as texts from './text.js';\nimport * as variables from './variables.js';\nimport * as variablesDynamic from './variables_dynamic.js';\n\nexport {\n  lists,\n  logic,\n  loops,\n  math,\n  procedures,\n  texts,\n  variables,\n  variablesDynamic,\n};\n\n/**\n * A dictionary of the block definitions provided by all the\n * Blockly.libraryBlocks.* modules.\n */\nexport const blocks: {[key: string]: BlockDefinition} = Object.assign(\n  {},\n  lists.blocks,\n  logic.blocks,\n  loops.blocks,\n  math.blocks,\n  procedures.blocks,\n  texts.blocks,\n  variables.blocks,\n  variablesDynamic.blocks,\n);\n", "/* eslint-disable */\n;(function(root, factory) {\n  if (typeof define === 'function' && define.amd) { // AMD\n    define(['blockly/core', 'blockly/msg/en', 'blockly/blocks'], factory);\n  } else if (typeof exports === 'object') { // Node.js\n    module.exports = factory(require('blockly/core'), require('blockly/msg/en'), require('blockly/blocks'));\n  } else { // Browser\n    root.Blockly = factory(root.Blockly, root.Blockly.Msg, root.Blockly.Blocks);\n  }\n}(this, function(Blockly, en, blocks) {\n/**\n * @license\n * Copyright 2019 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n/**\n * @file Main entrypoint for blockly package.  Via its UMD wrapper,\n * this module loads blockly/core, blockly/blocks and blockly/msg/en\n * and then calls setLocale(en).\n *\n * This entrypoint previously also loaded one or more generators\n * (JavaScript in browser, all five in node.js environments) but it no\n * longer makes sense to do so because of changes to generators\n * exports (they no longer have the side effect of defining\n * Blockly.JavaScript, etc., when loaded as modules).\n */\n\n/* eslint-disable */\n'use strict';\n\n// Include the EN Locale by default.\nBlockly.setLocale(en);\n\nreturn Blockly;\n}));\n", "import Blockly from './index.js';\nexport const {\n  Block,\n  BlockFlyoutInflater,\n  BlockNavigationPolicy,\n  BlockSvg,\n  Blocks,\n  ButtonFlyoutInflater,\n  COLLAPSED_FIELD_NAME,\n  COLLAPSED_INPUT_NAME,\n  COLLAPSE_CHARS,\n  CodeGenerator,\n  CollapsibleToolboxCategory,\n  ComponentManager,\n  Connection,\n  ConnectionChecker,\n  ConnectionDB,\n  ConnectionNavigationPolicy,\n  ConnectionType,\n  ContextMenu,\n  ContextMenuItems,\n  ContextMenuRegistry,\n  Css,\n  DELETE_VARIABLE_ID,\n  DeleteArea,\n  DragTarget,\n  DropDownDiv,\n  Events,\n  Extensions,\n  Field,\n  FieldCheckbox,\n  FieldDropdown,\n  FieldImage,\n  FieldLabel,\n  FieldLabelSerializable,\n  FieldNavigationPolicy,\n  FieldNumber,\n  FieldTextInput,\n  FieldVariable,\n  Flyout,\n  FlyoutButton,\n  FlyoutButtonNavigationPolicy,\n  FlyoutItem,\n  FlyoutMetricsManager,\n  FlyoutNavigationPolicy,\n  FlyoutNavigator,\n  FlyoutSeparator,\n  FlyoutSeparatorNavigationPolicy,\n  FocusManager,\n  FocusableTreeTraverser,\n  Generator,\n  Gesture,\n  Grid,\n  HorizontalFlyout,\n  INPUT_VALUE,\n  Input,\n  InsertionMarkerPreviewer,\n  KeyboardNavigationController,\n  LabelFlyoutInflater,\n  LineCursor,\n  Marker,\n  MarkerManager,\n  Menu,\n  MenuItem,\n  MetricsManager,\n  Msg,\n  NEXT_STATEMENT,\n  Names,\n  Navigator,\n  OPPOSITE_TYPE,\n  OUTPUT_VALUE,\n  Options,\n  PREVIOUS_STATEMENT,\n  PROCEDURE_CATEGORY_NAME,\n  Procedures,\n  RENAME_VARIABLE_ID,\n  RenderedConnection,\n  Scrollbar,\n  ScrollbarPair,\n  SeparatorFlyoutInflater,\n  ShortcutItems,\n  ShortcutRegistry,\n  TOOLBOX_AT_BOTTOM,\n  TOOLBOX_AT_LEFT,\n  TOOLBOX_AT_RIGHT,\n  TOOLBOX_AT_TOP,\n  Theme,\n  ThemeManager,\n  Themes,\n  Toast,\n  Toolbox,\n  ToolboxCategory,\n  ToolboxItem,\n  ToolboxSeparator,\n  Tooltip,\n  Touch,\n  Trashcan,\n  UnattachedFieldError,\n  VARIABLE_CATEGORY_NAME,\n  VARIABLE_DYNAMIC_CATEGORY_NAME,\n  VERSION,\n  VariableMap,\n  VariableModel,\n  Variables,\n  VariablesDynamic,\n  VerticalFlyout,\n  WidgetDiv,\n  Workspace,\n  WorkspaceAudio,\n  WorkspaceDragger,\n  WorkspaceNavigationPolicy,\n  WorkspaceSvg,\n  Xml,\n  ZoomControls,\n  blockAnimations,\n  blockRendering,\n  browserEvents,\n  bubbles,\n  bumpObjects,\n  clipboard,\n  comments,\n  common,\n  config,\n  constants,\n  defineBlocksWithJsonArray,\n  dialog,\n  dragging,\n  fieldRegistry,\n  geras,\n  getFocusManager,\n  getMainWorkspace,\n  getSelected,\n  hasBubble,\n  hideChaff,\n  icons,\n  inject,\n  inputs,\n  isCopyable,\n  isDeletable,\n  isDraggable,\n  isIcon,\n  isObservable,\n  isPaster,\n  isRenderedElement,\n  isSelectable,\n  isSerializable,\n  isVariableBackedParameterModel,\n  keyboardNavigationController,\n  layers,\n  navigateBlock,\n  navigateStacks,\n  procedures,\n  registry,\n  renderManagement,\n  serialization,\n  setLocale,\n  setParentContainer,\n  svgResize,\n  thrasos,\n  uiPosition,\n  utils,\n  zelos,\n} = Blockly;\n"], "mappings": ";;;;;;;;;AAAA;AAAA;AACC,KAAC,SAAS,MAAM,SAAS;AACxB,UAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAC9C,eAAO,CAAC,GAAG,OAAO;AAAA,MACpB,WAAW,OAAO,YAAY,UAAU;AACtC,eAAO,UAAU,QAAQ;AAAA,MAC3B,OAAO;AACL,YAAI,WAAW,QAAQ;AACvB,iBAAS,OAAO,UAAU;AACxB,eAAK,QAAQ,IAAI,GAAG,IAAI,SAAS,GAAG;AAAA,QACtC;AAAA,MACF;AAAA,IACF,GAAE,SAAM,WAAW;AAGnB;AAEA,UAAIA,WAAUA,YAAW,EAAE,KAAK,uBAAO,OAAO,IAAI,EAAE;AAEpD,MAAAA,SAAQ,IAAI,YAAY,IAAI;AAC5B,MAAAA,SAAQ,IAAI,aAAa,IAAI;AAC7B,MAAAA,SAAQ,IAAI,SAAS,IAAI;AACzB,MAAAA,SAAQ,IAAI,kCAAkC,IAAI;AAClD,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,WAAW,IAAI;AAC3B,MAAAA,SAAQ,IAAI,UAAU,IAAI;AAC1B,MAAAA,SAAQ,IAAI,OAAO,IAAI;AACvB,MAAAA,SAAQ,IAAI,4BAA4B,IAAI;AAC5C,MAAAA,SAAQ,IAAI,cAAc,IAAI;AAC9B,MAAAA,SAAQ,IAAI,gBAAgB,IAAI;AAChC,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,iBAAiB,IAAI;AACjC,MAAAA,SAAQ,IAAI,kBAAkB,IAAI;AAClC,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,gBAAgB,IAAI;AAChC,MAAAA,SAAQ,IAAI,kBAAkB,IAAI;AAClC,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,aAAa,IAAI;AAC7B,MAAAA,SAAQ,IAAI,kCAAkC,IAAI;AAClD,MAAAA,SAAQ,IAAI,yCAAyC,IAAI;AACzD,MAAAA,SAAQ,IAAI,4CAA4C,IAAI;AAC5D,MAAAA,SAAQ,IAAI,wCAAwC,IAAI;AACxD,MAAAA,SAAQ,IAAI,2CAA2C,IAAI;AAC3D,MAAAA,SAAQ,IAAI,kCAAkC,IAAI;AAClD,MAAAA,SAAQ,IAAI,0BAA0B,IAAI;AAC1C,MAAAA,SAAQ,IAAI,wBAAwB,IAAI;AACxC,MAAAA,SAAQ,IAAI,0BAA0B,IAAI;AAC1C,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,4BAA4B,IAAI;AAC5C,MAAAA,SAAQ,IAAI,0BAA0B,IAAI;AAC1C,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,wBAAwB,IAAI;AACxC,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,wBAAwB,IAAI;AACxC,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,0BAA0B,IAAI;AAC1C,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,6BAA6B,IAAI;AAC7C,MAAAA,SAAQ,IAAI,oCAAoC,IAAI;AACpD,MAAAA,SAAQ,IAAI,oCAAoC,IAAI;AACpD,MAAAA,SAAQ,IAAI,mCAAmC,IAAI;AACnD,MAAAA,SAAQ,IAAI,mCAAmC,IAAI;AACnD,MAAAA,SAAQ,IAAI,aAAa,IAAI;AAC7B,MAAAA,SAAQ,IAAI,eAAe,IAAI;AAC/B,MAAAA,SAAQ,IAAI,cAAc,IAAI;AAC9B,MAAAA,SAAQ,IAAI,mBAAmB,IAAI;AACnC,MAAAA,SAAQ,IAAI,cAAc,IAAI;AAC9B,MAAAA,SAAQ,IAAI,YAAY,IAAI;AAC5B,MAAAA,SAAQ,IAAI,iBAAiB,IAAI;AACjC,MAAAA,SAAQ,IAAI,iBAAiB,IAAI;AACjC,MAAAA,SAAQ,IAAI,8BAA8B,IAAI;AAC9C,MAAAA,SAAQ,IAAI,iBAAiB,IAAI;AACjC,MAAAA,SAAQ,IAAI,eAAe,IAAI;AAC/B,MAAAA,SAAQ,IAAI,WAAW,IAAI;AAC3B,MAAAA,SAAQ,IAAI,eAAe,IAAI;AAC/B,MAAAA,SAAQ,IAAI,iBAAiB,IAAI;AACjC,MAAAA,SAAQ,IAAI,mBAAmB,IAAI;AACnC,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,cAAc,IAAI;AAC9B,MAAAA,SAAQ,IAAI,YAAY,IAAI;AAC5B,MAAAA,SAAQ,IAAI,cAAc,IAAI;AAC9B,MAAAA,SAAQ,IAAI,iBAAiB,IAAI;AACjC,MAAAA,SAAQ,IAAI,aAAa,IAAI;AAC7B,MAAAA,SAAQ,IAAI,MAAM,IAAI;AACtB,MAAAA,SAAQ,IAAI,aAAa,IAAI;AAC7B,MAAAA,SAAQ,IAAI,eAAe,IAAI;AAC/B,MAAAA,SAAQ,IAAI,cAAc,IAAI;AAC9B,MAAAA,SAAQ,IAAI,OAAO,IAAI;AACvB,MAAAA,SAAQ,IAAI,4BAA4B,IAAI;AAC5C,MAAAA,SAAQ,IAAI,0BAA0B,IAAI;AAC1C,MAAAA,SAAQ,IAAI,4BAA4B,IAAI;AAC5C,MAAAA,SAAQ,IAAI,uCAAuC,IAAI;AACvD,MAAAA,SAAQ,IAAI,qCAAqC,IAAI;AACrD,MAAAA,SAAQ,IAAI,2BAA2B,IAAI;AAC3C,MAAAA,SAAQ,IAAI,8BAA8B,IAAI;AAC9C,MAAAA,SAAQ,IAAI,gCAAgC,IAAI;AAChD,MAAAA,SAAQ,IAAI,2BAA2B,IAAI;AAC3C,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,0BAA0B,IAAI;AAC1C,MAAAA,SAAQ,IAAI,4BAA4B,IAAI;AAC5C,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,4BAA4B,IAAI;AAC5C,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,wBAAwB,IAAI;AACxC,MAAAA,SAAQ,IAAI,wBAAwB,IAAI;AACxC,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,mCAAmC,IAAI;AACnD,MAAAA,SAAQ,IAAI,kCAAkC,IAAI;AAClD,MAAAA,SAAQ,IAAI,kCAAkC,IAAI;AAClD,MAAAA,SAAQ,IAAI,oCAAoC,IAAI;AACpD,MAAAA,SAAQ,IAAI,0CAA0C,IAAI;AAC1D,MAAAA,SAAQ,IAAI,yCAAyC,IAAI;AACzD,MAAAA,SAAQ,IAAI,yCAAyC,IAAI;AACzD,MAAAA,SAAQ,IAAI,2CAA2C,IAAI;AAC3D,MAAAA,SAAQ,IAAI,sCAAsC,IAAI;AACtD,MAAAA,SAAQ,IAAI,qCAAqC,IAAI;AACrD,MAAAA,SAAQ,IAAI,qCAAqC,IAAI;AACrD,MAAAA,SAAQ,IAAI,uCAAuC,IAAI;AACvD,MAAAA,SAAQ,IAAI,gCAAgC,IAAI;AAChD,MAAAA,SAAQ,IAAI,kCAAkC,IAAI;AAClD,MAAAA,SAAQ,IAAI,4BAA4B,IAAI;AAC5C,MAAAA,SAAQ,IAAI,2BAA2B,IAAI;AAC3C,MAAAA,SAAQ,IAAI,+BAA+B,IAAI;AAC/C,MAAAA,SAAQ,IAAI,kCAAkC,IAAI;AAClD,MAAAA,SAAQ,IAAI,oCAAoC,IAAI;AACpD,MAAAA,SAAQ,IAAI,wBAAwB,IAAI;AACxC,MAAAA,SAAQ,IAAI,2BAA2B,IAAI;AAC3C,MAAAA,SAAQ,IAAI,8BAA8B,IAAI;AAC9C,MAAAA,SAAQ,IAAI,gCAAgC,IAAI;AAChD,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,wBAAwB,IAAI;AACxC,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,wBAAwB,IAAI;AACxC,MAAAA,SAAQ,IAAI,cAAc,IAAI;AAC9B,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,wBAAwB,IAAI;AACxC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,0BAA0B,IAAI;AAC1C,MAAAA,SAAQ,IAAI,wBAAwB,IAAI;AACxC,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,sCAAsC,IAAI;AACtD,MAAAA,SAAQ,IAAI,qCAAqC,IAAI;AACrD,MAAAA,SAAQ,IAAI,qCAAqC,IAAI;AACrD,MAAAA,SAAQ,IAAI,uCAAuC,IAAI;AACvD,MAAAA,SAAQ,IAAI,mCAAmC,IAAI;AACnD,MAAAA,SAAQ,IAAI,kCAAkC,IAAI;AAClD,MAAAA,SAAQ,IAAI,kCAAkC,IAAI;AAClD,MAAAA,SAAQ,IAAI,oCAAoC,IAAI;AACpD,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,4BAA4B,IAAI;AAC5C,MAAAA,SAAQ,IAAI,6BAA6B,IAAI;AAC7C,MAAAA,SAAQ,IAAI,kBAAkB,IAAI;AAClC,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,4BAA4B,IAAI;AAC5C,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,4BAA4B,IAAI;AAC5C,MAAAA,SAAQ,IAAI,4BAA4B,IAAI;AAC5C,MAAAA,SAAQ,IAAI,0BAA0B,IAAI;AAC1C,MAAAA,SAAQ,IAAI,2BAA2B,IAAI;AAC3C,MAAAA,SAAQ,IAAI,4BAA4B,IAAI;AAC5C,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,0BAA0B,IAAI;AAC1C,MAAAA,SAAQ,IAAI,0BAA0B,IAAI;AAC1C,MAAAA,SAAQ,IAAI,2BAA2B,IAAI;AAC3C,MAAAA,SAAQ,IAAI,0BAA0B,IAAI;AAC1C,MAAAA,SAAQ,IAAI,2BAA2B,IAAI;AAC3C,MAAAA,SAAQ,IAAI,2BAA2B,IAAI;AAC3C,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,YAAY,IAAI;AAC5B,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,6BAA6B,IAAI;AAC7C,MAAAA,SAAQ,IAAI,4BAA4B,IAAI;AAC5C,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,wBAAwB,IAAI;AACxC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,QAAQ,IAAI;AACxB,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,6BAA6B,IAAI;AAC7C,MAAAA,SAAQ,IAAI,gCAAgC,IAAI;AAChD,MAAAA,SAAQ,IAAI,+BAA+B,IAAI;AAC/C,MAAAA,SAAQ,IAAI,kCAAkC,IAAI;AAClD,MAAAA,SAAQ,IAAI,+BAA+B,IAAI;AAC/C,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,kBAAkB,IAAI;AAClC,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,mBAAmB,IAAI;AACnC,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,wBAAwB,IAAI;AACxC,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,wBAAwB,IAAI;AACxC,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,cAAc,IAAI;AAC9B,MAAAA,SAAQ,IAAI,kBAAkB,IAAI;AAClC,MAAAA,SAAQ,IAAI,aAAa,IAAI;AAC7B,MAAAA,SAAQ,IAAI,kBAAkB,IAAI;AAClC,MAAAA,SAAQ,IAAI,eAAe,IAAI;AAC/B,MAAAA,SAAQ,IAAI,iBAAiB,IAAI;AACjC,MAAAA,SAAQ,IAAI,eAAe,IAAI;AAC/B,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,mBAAmB,IAAI;AACnC,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,4BAA4B,IAAI;AAC5C,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,8BAA8B,IAAI;AAC9C,MAAAA,SAAQ,IAAI,0BAA0B,IAAI;AAC1C,MAAAA,SAAQ,IAAI,6BAA6B,IAAI;AAC7C,MAAAA,SAAQ,IAAI,0BAA0B,IAAI;AAC1C,MAAAA,SAAQ,IAAI,2BAA2B,IAAI;AAC3C,MAAAA,SAAQ,IAAI,6BAA6B,IAAI;AAC7C,MAAAA,SAAQ,IAAI,8BAA8B,IAAI;AAC9C,MAAAA,SAAQ,IAAI,0BAA0B,IAAI;AAC1C,MAAAA,SAAQ,IAAI,6BAA6B,IAAI;AAC7C,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,4BAA4B,IAAI;AAC5C,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,0BAA0B,IAAI;AAC1C,MAAAA,SAAQ,IAAI,4BAA4B,IAAI;AAC5C,MAAAA,SAAQ,IAAI,6BAA6B,IAAI;AAC7C,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,mBAAmB,IAAI;AACnC,MAAAA,SAAQ,IAAI,2BAA2B,IAAI;AAC3C,MAAAA,SAAQ,IAAI,gCAAgC,IAAI;AAChD,MAAAA,SAAQ,IAAI,2BAA2B,IAAI;AAC3C,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,2BAA2B,IAAI;AAC3C,MAAAA,SAAQ,IAAI,+BAA+B,IAAI;AAC/C,MAAAA,SAAQ,IAAI,6BAA6B,IAAI;AAC7C,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,wBAAwB,IAAI;AACxC,MAAAA,SAAQ,IAAI,2BAA2B,IAAI;AAC3C,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,2BAA2B,IAAI;AAC3C,MAAAA,SAAQ,IAAI,0BAA0B,IAAI;AAC1C,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,gBAAgB,IAAI;AAChC,MAAAA,SAAQ,IAAI,gBAAgB,IAAI;AAChC,MAAAA,SAAQ,IAAI,gBAAgB,IAAI;AAChC,MAAAA,SAAQ,IAAI,eAAe,IAAI;AAC/B,MAAAA,SAAQ,IAAI,mBAAmB,IAAI;AACnC,MAAAA,SAAQ,IAAI,eAAe,IAAI;AAC/B,MAAAA,SAAQ,IAAI,eAAe,IAAI;AAC/B,MAAAA,SAAQ,IAAI,wBAAwB,IAAI;AACxC,MAAAA,SAAQ,IAAI,wBAAwB,IAAI;AACxC,MAAAA,SAAQ,IAAI,wBAAwB,IAAI;AACxC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,YAAY,IAAI;AAC5B,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,wBAAwB,IAAI;AACxC,MAAAA,SAAQ,IAAI,0BAA0B,IAAI;AAC1C,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,cAAc,IAAI;AAC9B,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,YAAY,IAAI;AAC5B,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,gBAAgB,IAAI;AAChC,MAAAA,SAAQ,IAAI,6BAA6B,IAAI;AAC7C,MAAAA,SAAQ,IAAI,0BAA0B,IAAI;AAC1C,MAAAA,SAAQ,IAAI,iCAAiC,IAAI;AACjD,MAAAA,SAAQ,IAAI,iCAAiC,IAAI;AACjD,MAAAA,SAAQ,IAAI,+BAA+B,IAAI;AAC/C,MAAAA,SAAQ,IAAI,+BAA+B,IAAI;AAC/C,MAAAA,SAAQ,IAAI,+BAA+B,IAAI;AAC/C,MAAAA,SAAQ,IAAI,sCAAsC,IAAI;AACtD,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,gCAAgC,IAAI;AAChD,MAAAA,SAAQ,IAAI,2BAA2B,IAAI;AAC3C,MAAAA,SAAQ,IAAI,gCAAgC,IAAI;AAChD,MAAAA,SAAQ,IAAI,kCAAkC,IAAI;AAClD,MAAAA,SAAQ,IAAI,8BAA8B,IAAI;AAC9C,MAAAA,SAAQ,IAAI,gCAAgC,IAAI;AAChD,MAAAA,SAAQ,IAAI,8BAA8B,IAAI;AAC9C,MAAAA,SAAQ,IAAI,6BAA6B,IAAI;AAC7C,MAAAA,SAAQ,IAAI,8BAA8B,IAAI;AAC9C,MAAAA,SAAQ,IAAI,kCAAkC,IAAI;AAClD,MAAAA,SAAQ,IAAI,0BAA0B,IAAI;AAC1C,MAAAA,SAAQ,IAAI,6BAA6B,IAAI;AAC7C,MAAAA,SAAQ,IAAI,6BAA6B,IAAI;AAC7C,MAAAA,SAAQ,IAAI,6BAA6B,IAAI;AAC7C,MAAAA,SAAQ,IAAI,6BAA6B,IAAI;AAC7C,MAAAA,SAAQ,IAAI,+BAA+B,IAAI;AAC/C,MAAAA,SAAQ,IAAI,mCAAmC,IAAI;AACnD,MAAAA,SAAQ,IAAI,qCAAqC,IAAI;AACrD,MAAAA,SAAQ,IAAI,MAAM,IAAI;AACtB,MAAAA,SAAQ,IAAI,gBAAgB,IAAI;AAChC,MAAAA,SAAQ,IAAI,iBAAiB,IAAI;AACjC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,2BAA2B,IAAI;AAC3C,MAAAA,SAAQ,IAAI,mBAAmB,IAAI;AACnC,MAAAA,SAAQ,IAAI,mBAAmB,IAAI;AACnC,MAAAA,SAAQ,IAAI,YAAY,IAAI;AAC5B,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,mBAAmB,IAAI;AACnC,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,oCAAoC,IAAI;AACpD,MAAAA,SAAQ,IAAI,oCAAoC,IAAI;AACpD,MAAAA,SAAQ,IAAI,oCAAoC,IAAI;AACpD,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,mBAAmB,IAAI;AACnC,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,wBAAwB,IAAI;AACxC,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,kBAAkB,IAAI;AAClC,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,kBAAkB,IAAI;AAClC,MAAAA,SAAQ,IAAI,mBAAmB,IAAI;AACnC,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,+BAA+B,IAAI;AAC/C,MAAAA,SAAQ,IAAI,6BAA6B,IAAI;AAC7C,MAAAA,SAAQ,IAAI,0BAA0B,IAAI;AAC1C,MAAAA,SAAQ,IAAI,iCAAiC,IAAI;AACjD,MAAAA,SAAQ,IAAI,mCAAmC,IAAI;AACnD,MAAAA,SAAQ,IAAI,6BAA6B,IAAI;AAC7C,MAAAA,SAAQ,IAAI,4BAA4B,IAAI;AAC5C,MAAAA,SAAQ,IAAI,kCAAkC,IAAI;AAClD,MAAAA,SAAQ,IAAI,gCAAgC,IAAI;AAChD,MAAAA,SAAQ,IAAI,mCAAmC,IAAI;AACnD,MAAAA,SAAQ,IAAI,qCAAqC,IAAI;AACrD,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,4BAA4B,IAAI;AAC5C,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,6BAA6B,IAAI;AAC7C,MAAAA,SAAQ,IAAI,4BAA4B,IAAI;AAC5C,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,mBAAmB,IAAI;AACnC,MAAAA,SAAQ,IAAI,4BAA4B,IAAI;AAC5C,MAAAA,SAAQ,IAAI,mBAAmB,IAAI;AACnC,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,mBAAmB,IAAI;AACnC,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,kBAAkB,IAAI;AAClC,MAAAA,SAAQ,IAAI,oBAAoB,IAAI;AACpC,MAAAA,SAAQ,IAAI,qBAAqB,IAAI;AACrC,MAAAA,SAAQ,IAAI,4BAA4B,IAAI;AAC5C,MAAAA,SAAQ,IAAI,0BAA0B,IAAI;AAC1C,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,mBAAmB,IAAI;AACnC,MAAAA,SAAQ,IAAI,mBAAmB,IAAI;AACnC,MAAAA,SAAQ,IAAI,mBAAmB,IAAI;AACnC,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,0BAA0B,IAAI;AAC1C,MAAAA,SAAQ,IAAI,mBAAmB,IAAI;AACnC,MAAAA,SAAQ,IAAI,OAAO,IAAI;AACvB,MAAAA,SAAQ,IAAI,MAAM,IAAI;AACtB,MAAAA,SAAQ,IAAI,SAAS,IAAI;AACzB,MAAAA,SAAQ,IAAI,aAAa,IAAI;AAC7B,MAAAA,SAAQ,IAAI,wBAAwB,IAAI;AACxC,MAAAA,SAAQ,IAAI,0BAA0B,IAAI;AAC1C,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,eAAe,IAAI;AAC/B,MAAAA,SAAQ,IAAI,0BAA0B,IAAI;AAC1C,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,MAAAA,SAAQ,IAAI,yBAAyB,IAAI;AACzC,MAAAA,SAAQ,IAAI,0CAA0C,IAAI;AAC1D,MAAAA,SAAQ,IAAI,yCAAyC,IAAI;AACzD,MAAAA,SAAQ,IAAI,SAAS,IAAI;AACzB,MAAAA,SAAQ,IAAI,sBAAsB,IAAI;AACtC,MAAAA,SAAQ,IAAI,gCAAgC,IAAI;AAChD,MAAAA,SAAQ,IAAI,2BAA2B,IAAIA,SAAQ,IAAI,0BAA0B;AACjF,MAAAA,SAAQ,IAAI,uBAAuB,IAAIA,SAAQ,IAAI,0BAA0B;AAC7E,MAAAA,SAAQ,IAAI,iCAAiC,IAAIA,SAAQ,IAAI,wBAAwB;AACrF,MAAAA,SAAQ,IAAI,6BAA6B,IAAIA,SAAQ,IAAI,sBAAsB;AAC/E,MAAAA,SAAQ,IAAI,yBAAyB,IAAIA,SAAQ,IAAI,oBAAoB;AACzE,MAAAA,SAAQ,IAAI,sBAAsB,IAAIA,SAAQ,IAAI,0BAA0B;AAC5E,MAAAA,SAAQ,IAAI,8BAA8B,IAAIA,SAAQ,IAAI,0BAA0B;AACpF,MAAAA,SAAQ,IAAI,8BAA8B,IAAIA,SAAQ,IAAI,wBAAwB;AAClF,MAAAA,SAAQ,IAAI,yBAAyB,IAAIA,SAAQ,IAAI,wBAAwB;AAC7E,MAAAA,SAAQ,IAAI,+BAA+B,IAAIA,SAAQ,IAAI,cAAc;AACzE,MAAAA,SAAQ,IAAI,iCAAiC,IAAIA,SAAQ,IAAI,cAAc;AAC3E,MAAAA,SAAQ,IAAI,8BAA8B,IAAIA,SAAQ,IAAI,cAAc;AACxE,MAAAA,SAAQ,IAAI,+BAA+B,IAAIA,SAAQ,IAAI,cAAc;AACzE,MAAAA,SAAQ,IAAI,wBAAwB,IAAIA,SAAQ,IAAI,wBAAwB;AAC5E,MAAAA,SAAQ,IAAI,8BAA8B,IAAIA,SAAQ,IAAI,gCAAgC;AAC1F,MAAAA,SAAQ,IAAI,yBAAyB,IAAIA,SAAQ,IAAI,2BAA2B;AAChF,MAAAA,SAAQ,IAAI,gCAAgC,IAAIA,SAAQ,IAAI,kCAAkC;AAC9F,MAAAA,SAAQ,IAAI,4BAA4B,IAAIA,SAAQ,IAAI,8BAA8B;AACtF,MAAAA,SAAQ,IAAI,sBAAsB,IAAIA,SAAQ,IAAI,wBAAwB;AAC1E,MAAAA,SAAQ,IAAI,kCAAkC,IAAIA,SAAQ,IAAI,wBAAwB;AAEtF,MAAAA,SAAQ,IAAI,UAAU,IAAI;AAC1B,MAAAA,SAAQ,IAAI,WAAW,IAAI;AAC3B,MAAAA,SAAQ,IAAI,WAAW,IAAI;AAC3B,MAAAA,SAAQ,IAAI,WAAW,IAAI;AAC3B,MAAAA,SAAQ,IAAI,eAAe,IAAI;AAC/B,MAAAA,SAAQ,IAAI,WAAW,IAAI;AAC3B,MAAAA,SAAQ,IAAI,gBAAgB,IAAI;AAChC,MAAAA,SAAQ,IAAI,YAAY,IAAI;AAC5B,MAAAA,SAAQ,IAAI,uBAAuB,IAAI;AACvC,aAAOA,SAAQ;AAAA,IACf,CAAC;AAAA;AAAA;;;;;;;;;;;;;;;AC/bM,UAAMC,oDAASC,EAAAA,kEAAoC,CAExD,EACE,MAAQ,yBACR,UAAY,MACZ,OAAS,CACP,EACE,MAAQ,kBACR,MAAQ,OACR,UAAY,gCAHd,CADO,GAOT,QAAU,MACV,OAAS,2BACT,SAAW,gCACX,SAAW,gCACX,YAAc,CAAC,yCAAD,EAdhB,GAiBA,EACE,MAAQ,yBACR,UAAY,wBACZ,OAAS,CACP;QACE,MAAQ;QACR,MAAQ;QACR,UAAY;MAHd,GAKA,EACE,MAAQ,eACR,MAAQ,QAFV,CANO,GAWT,mBAAqB,MACrB,eAAiB,MACjB,OAAS,2BACT,SAAW,gCACX,SAAW,gCACX,YAAc,CAAC,yCAAD,EAnBhB,CAnBwD,CAApC,GAqDhBC,8FAAmD,EAMvDC,mBAAmBA,SAEjBC,GAA2D;AAG3D,YAAI,CAAC,KAAKC,YAAY;AAGpB,cAAkB,4BAAd,KAAKC,MAAkC;AACzC,gBAAAC,IAAe;gBACfC,IAAiBC,EAAAA,+BAAA;UAFwB,MAIzCF,KAAe,yBACfC,IAAiBC,EAAAA,+BAAA;AAGnB,cAAMC,IAAW,KAAKC,SAAS,KAAd;AACXC,cAAmB,EACvBN,MAAMC,GACNM,QAAQ,EAACC,KAAKJ,EAASK,UAAU,IAAnB,EAAN,EAFe;AAKzBX,YAAQY,KAAK,EACXC,SAA8C,IAArC,KAAKC,UAAUC,kBAAf,GACTC,MAAMZ,EAAea,QAAQ,MAAMX,EAASY,QAAT,CAA7B,GACNC,UAAsBC,EAAAA,mDAAgB,MAAMZ,CAAlC,EAHC,CAAb;QAjBoB,WAwBJ,4BAAd,KAAKN,QACS,qCAAd,KAAKA,KAECmB,KAAe;UACnBL,MAAMX,EAAAA,+BAAA;UACNQ,SAAS;UACTM,UAAUG,uEAA4B,IAA5B;QAHS,GAKfC,IAAO,KAAKhB,SAAS,KAAd,EAAsBW,QAAtB,GACPM,IAAe,EACnBR,MAAMX,EAAAA,+BAAA,gBAAuBY,QAAQ,MAAMM,CAArC,GACNV,SAAS,MACTM,UAAUM,uEAA4B,IAA5B,EAHS,GAKrBzB,EAAQ0B,QAAQL,CAAhB,GACArB,EAAQ0B,QAAQF,CAAhB;MA1CuD,GAoD7DG,UAAUA,SAA+BC,GAAiB;AAClDC,YAAK,KAAKC,cAAc,KAAnB;AACLC,YAA0BC,EAAAA,6CAAY,KAAKlB,WAAWe,CAAtC;AACJ,oCAAd,KAAK3B,OACP,KAAK+B,iBAAkBC,SAASH,EAAcI,QAAd,CAAhC,IAEA,KAAKC,SAAS,OAAd,EAAwBC,WAAYH,SAASH,EAAcI,QAAd,CAA7C;MANsD,EA5DH,GA8EnDb,yEAA8BA,SAAUgB,GAAoB;AAChE,eAAO,WAAA;AACL,gBAAMxB,IAAYwB,EAAMxB,WAElByB,IADgBD,EAAM/B,SAAS,KAAfiC,EACSR,YAAd;AACPS,YAAAA,gDAAe3B,GAAWyB,CAApC;QAJK;MADyD,GAgB5Dd,yEAA8BA,SAAUa,GAAoB;AAChE,eAAO,WAAA;AAEL,gBAAMC,IADgBD,EAAM/B,SAAS,KAAfiC,EACSR,YAAd;AACbO,eACQG,EAAAA,gDAAeH,EAASI,aAAT,GAAyBJ,GAAUD,CAA5D;QAJG;MADyD;QAUvDM,gDACT,2CACA9C,2FAFF;AAMA+C,QAAAA,2CAAajD,iDAAb;AA/LA,UAAAkD,4CAAA,EA4BalD,QAAAA,kDA5Bb;AC2BO,UAAMA,4CAASC,EAAAA,kEAAoC,CAExD,EACE,MAAQ,iBACR,UAAY,MACZ,OAAS,CACP,EACE,MAAQ,kBACR,MAAQ,OACR,UAAY,gCAHd,CADO,GAOT,QAAU,MACV,OAAS,mBACT,SAAW,gCACX,SAAW,gCACX,YAAc,CAAC,kCAAD,EAdhB,GAiBA,EACE,MAAQ,iBACR,UAAY,wBACZ,OAAS;QACP,EACE,MAAQ,kBACR,MAAQ,OACR,UAAY,gCAHd;QAKA,EACE,MAAQ,eACR,MAAQ,QAFV;MANO,GAWT,mBAAqB,MACrB,eAAiB,MACjB,OAAS,mBACT,SAAW,gCACX,SAAW,gCACX,YAAc,CAAC,kCAAD,EAnBhB,CAnBwD,CAApC,GAqDhBC,sFAAmD,EAMvDC,mBAAmBA,SAEjBC,GAA2D;AAE3D,YAAI,CAAC,KAAKC,YAAY;AAIpB,cAAkB,oBAAd,KAAKC,MAA0B;AACjC,gBAAAC,IAAe;AACf,gBAAAC,IAAiBC,EAAAA,+BAAA;UAFgB,MAIjCF,KAAe,iBACfC,IAAiBC,EAAAA,+BAAA;AAGnB,cAAMC,IAAW,KAAKC,SAAS,KAAd;AACXC,cAAmB,EACvBN,MAAMC,GACNM,QAAQ,EAACC,KAAKJ,EAASK,UAAU,IAAnB,EAAN,EAFe;AAKzBX,YAAQY,KAAK,EACXC,SAA8C,IAArC,KAAKC,UAAUC,kBAAf,GACTC,MAAMZ,EAAea,QAAQ,MAAMX,EAASY,QAAT,CAA7B,GACNC,UAAsBC,EAAAA,mDAAgB,MAAMZ,CAAlC,EAHC,CAAb;QAlBoB,WA0BJ,oBAAd,KAAKN,QACS,6BAAd,KAAKA,KAECmB,KAAe,EACnBL,MAAMX,EAAAA,+BAAA,iBACNQ,SAAS,MACTM,UAAUG,+DAA4B,IAA5B,EAHS,GAKfC,IAAO,KAAKhB,SAAS,KAAd,EAAsBW,QAAtB,GACPM,IAAe,EACnBR,MAAMX,EAAAA,+BAAA,gBAAuBY,QAAQ,MAAMM,CAArC,GACNV,SAAS,MACTM,UAAUM,+DAA4B,IAA5B,EAHS,GAKrBzB,EAAQ0B,QAAQL,CAAhB,GACArB,EAAQ0B,QAAQF,CAAhB;MA3CuD,EARN,GAgEnDF,iEAA8BA,SAClCgB,GAAoB;AAEpB,eAAO,WAAA;AACL,gBAAMxB,IAAYwB,EAAMxB,WAElByB,IADgBD,EAAM/B,SAAS,KAAfiC,EACSR,YAAd;AACPS,YAAAA,gDAAe3B,GAAWyB,CAApC;QAJK;MAFa,GAiBhBd,iEAA8BA,SAClCa,GAAoB;AAEpB,eAAO,WAAA;AAEL,gBAAMC,IADgBD,EAAM/B,SAAS,KAAfiC,EACSR,YAAd;AACbO,eACQG,EAAAA,gDAAeH,EAASI,aAAT,GAAyBJ,GAAUD,CAA5D;QAJG;MAFa;AAWXM,QAAAA,gDACT,oCACA9C,mFAFF;AAMA+C,QAAAA,2CAAajD,yCAAb;AApLA,UAAAkD,oCAAA,EA2BalD,QAAAA,0CA3Bb;AC+BO,UAAMA,uCAASC,EAAAA,kEAAoC;QAExD,EACE,MAAQ,QACR,UAAY,MACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,QACR,MAAQ,GAHV,CADO,GAOT,QAAU,UACV,OAAS,eACT,SAAW,4BACX,SAAW,4BACX,YAAc,CAAC,eAAe,4BAAhB,EAdhB;QAgBA,EACE,MAAQ,aACR,UAAY,IACZ,QAAU,UACV,OAAS,eACT,SAAW,4BACX,SAAW,4BACX,SAAW,oBAPb;QASA,EACE,MAAQ,8BACR,UAAY,4CACZ,OAAS,CACP,EACE,MAAQ,cADV,GAGA,EACE,MAAQ,mBACR,MAAQ,QAFV,CAJO,GAST,OAAS,eACT,SAAW,mCACX,mBAAqB,MAdvB;QAgBA,EACE,MAAQ,yBACR,UAAY,2CACZ,mBAAqB,MACrB,eAAiB,MACjB,OAAS,eACT,SAAW,wCACX,mBAAqB,MAPvB;QASA;UACE,MAAQ;UACR,UAAY;UACZ,OAAS,CACP,EACE,MAAQ,kBACR,MAAQ,OACR,UAAY,8BAHd,GAKA,EACE,MAAQ,eACR,MAAQ,OAFV,CANO;UAWT,mBAAqB;UACrB,eAAiB;UACjB,OAAS;UACT,YAAc,CAAC,qBAAD;QAjBhB;QAmBA,EACE,MAAQ,eACR,UAAY,4BACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,SACR,OAAS,CAAC,UAAU,OAAX,EAHX,CADO,GAOT,QAAU,UACV,OAAS,eACT,SAAW,8BACX,SAAW,6BAbb;QAeA;UACE,MAAQ;UACR,UAAY;UACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,SACR,OAAS,CAAC,UAAU,OAAX,EAHX,CADO;UAOT,QAAU;UACV,OAAS;UACT,SAAW;UACX,SAAW;QAbb;QAeA;UACE,MAAQ;UACR,UAAY;UACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,SACR,OAAS,SAHX,GAKA,EACE,MAAQ,kBACR,MAAQ,OACR,SAAW,CACT,CAAC,sCAAsC,OAAvC,GACA,CAAC,qCAAqC,MAAtC,CAFS,EAHb,GAQA,EACE,MAAQ,eACR,MAAQ,QACR,OAAS,SAHX,CAdO;UAoBT,QAAU;UACV,OAAS;UACT,SAAW;UACX,cAAgB;UAChB,YAAc,CAAC,sBAAD;QA3BhB;QA6BA;UACE,MAAQ;UACR,UAAY;UACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,SACR,OAAS,SAHX,GAKA,EACE,MAAQ,kBACR,MAAQ,SACR,SAAW,CACT,CAAC,iCAAiC,YAAlC,GACA,CAAC,+BAA+B,UAAhC,GACA,CAAC,4BAA4B,OAA7B,GACA,CAAC,2BAA2B,MAA5B,GACA,CAAC,6BAA6B,QAA9B,CALS,EAHb,CANO;UAkBT,QAAU;UACV,OAAS;UACT,SAAW;UACX,cAAgB;UAChB,SAAW;QAzBb;MAlIwD,CAApC,GAuKhBkD,oDAAsB,EAI1BC,MAAMA,WAAA;AACJ,aAAA,kBAA0B,CACxB,CAAC3C,EAAAA,+BAAA,qCAA4C,YAA7C,GACA,CAACA,EAAAA,+BAAA,mCAA0C,UAA3C,GACA,CAACA,EAAAA,+BAAA,gCAAuC,OAAxC,CAHwB;AAK1B,aAAA,kBAA0B,CACxB,CAACA,EAAAA,+BAAA,mCAA0C,YAA3C,GACA;UAACA,EAAAA,+BAAA;UAAwC;QAAzC,GACA,CAACA,EAAAA,+BAAA,6BAAoC,MAArC,CAHwB;AAK1B,aAAK4C,WAAW5C,EAAAA,+BAAA,0BAAhB;AACA,aAAK6C,SAAS,aAAd;AACA,aAAKC,iBAAiB,QAAtB,EACGjB,SAAS,QADZ,EAEGkB,YAAY/C,EAAAA,+BAAA,gCAFf;AAGA,cAAMgD,IAAcC,OAA2B;AAC7C,gBAAMC,IAAqBC,EAAAA,+CAAS,EAClCtD,MAAM,kBACNF,SACE,KAAM,mBAAmBsD,CAAzB,EAHgC,CAAvB;AAKbC,YAAKE,aAEH,SAA+BC,GAAU;AACvC,kBAAMC,IAA0B,KAAKC,SAAL;AAE1BC,gBAAkB,iBAAVH,KAAoC,eAAVA;AACpCG,mBAFuB,iBAAbF,KAA0C,eAAbA,MAG3B,KAAKG,eAALxB,EACRyB,UAAUT,GAAGO,CAAnB;UANqC,CAF3C;AAaA,iBAAON;QAnBsC;AAqB/C,aAAKS,iBAAiB,cAAtB,EAAsCZ,YAAYC,EAAW,CAAX,GAAe,QAAjE;AACA,aAAKW,iBAAiB,KAAtB;AACA,aAAKA,iBAAiB,cAAtB,EAAsCZ,YAAYC,EAAW,CAAX,GAAe,QAAjE;AACA,aAAKW,iBAAiB,KAAtB;AACI3D,UAAAA,+BAAA,2BACF,KAAK2D,iBAAiB,MAAtB,EAA8BZ,YAAY/C,EAAAA,+BAAA,uBAA1C;AAEF,aAAK4D,gBAAgB,IAArB;AACA,aAAKC,UAAU,MAAM,QAArB;aACKH,UAAU,GAAG,IAAlB;AACA,aAAKA,UAAU,GAAG,IAAlB;AACA,aAAKI,WAAW9D,EAAAA,+BAAA,0BAAhB;MAhDI,GAwDN+D,eAAeA,WAAA;AACb,cAAMC,IAAqBC,EAAAA,+CAAc,UAAvB;AAClB,YAAMC,IAAQ,KAAKnC,SAAS,KAAd,aAAgCoC,EAAAA;AAC9CH,UAAUI,aAAa,OAAO,GAAGF,CAAH,EAA9B;AACMG,YAAQ,KAAKtC,SAAS,KAAd,aAAgCoC,EAAAA;AAC9CH,UAAUI,aAAa,OAAO,GAAGC,CAAH,EAA9B;AACA,eAAOL;MANM,GAcfM,eAAeA,SAAmCC,GAAmB;AACnE,cAAML,IAA2C,WAAnCK,EAAWC,aAAa,KAAxB;AACRH,YAA2C,WAAnCE,EAAWC,aAAa,KAAxB;AACd,aAAKd,UAAU,GAAGQ,CAAlB;AACA,aAAKR,UAAU,GAAGW,CAAlB;MAJmE,GAoBrEX,WAAWA,SAAmCT,GAAUwB,GAAa;AAGnE,aAAKC,YAAY,OAAOzB,CAAxB;AACA,aAAKyB,YAAY,YAAYzB,GAAG,IAAhC;AAEIwB,aACF,KAAK3B,iBAAiB,OAAOG,CAA7B,EAAgCpB,SAAS,QAAzC,GACI7B,EAAAA,+BAAA,yBACF,KAAK2D,iBAAiB,YAAYV,CAAlC,EAAqCF,YACnC/C,EAAAA,+BAAA,qBADF,KAKF,KAAK2D,iBAAiB,OAAOV,CAA7B;AAGQ,cAANA,KAAWjD,EAAAA,+BAAA,4BACb,KAAK0E;UAAY;UAAQ;QAAzB,GACA,KAAKf,iBAAiB,MAAtB,EAA8BZ,YAAY/C,EAAAA,+BAAA,uBAA1C;AAEQ,cAANiD,MACF,KAAK0B,gBAAgB,OAAO,cAA5B,GACI,KAAK5C,SAAS,UAAd,KACF,KAAK4C,gBAAgB,YAAY,cAAjC;MAxB+D,EA9F3C;AA4H5BpF,2CAAA,oBAA8BmD;2CAE9B,kBAA4B,EAI1BC,MAAMA,WAAA;AACJ,cAAMiC,IAAY,CAChB,CAAC5E,EAAAA,+BAAA,oCAA2C,WAA5C,GACA,CAACA,EAAAA,+BAAA,oCAA2C,WAA5C,GACA,CAACA,EAAAA,+BAAA,oCAA2C,WAA5C,CAHgB;AAKlB,aAAK4C,WAAW5C,EAAAA,+BAAA,uBAAhB;AACA,aAAK6C,SAAS,aAAd;AACA,aAAKC,iBAAiB,MAAtB,EACGjB,SAAS,QADZ,EAEGkB,YACeI,EAAAA,+CAAS;UACrBtD,MAAM;UACNF,SAASiF;QAFY,CAAvB,GAIA,MAPJ;AASA,aAAKf,UAAU,MAAM,QAArB;AACA,aAAKC,WAAW9D,EAAAA,+BAAA,uBAAhB;MAlBI,EAJoB;2CA0B5B,YAAsB,EAIpB2C,MAAMA,WAAA;AACJ,cAAMiC,IAAY,CAChB,CAAC5E,EAAAA,+BAAA,yBAAgC,MAAjC,GACA,CAACA,EAAAA,+BAAA,yBAAgC,MAAjC,GACA,CAACA,EAAAA,+BAAA,0BAAiC,OAAlC,CAHgB;AAKlB,aAAK4C,WAAW5C,EAAAA,+BAAA,iBAAhB;AACA,aAAK6C,SAAS,aAAd;AACA,aAAKC,iBAAiB,MAAtB,EACGjB,SAAS,QADZ,EAEGkB;UACeI,EAAAA,+CAAS,EACrBtD,MAAM,kBACNF,SAASiF,EAFY,CAAvB;UAIA;QAPJ;AASA,aAAKf,UAAU,MAAM,QAArB;AACA,aAAKC,WAAW9D,EAAAA,+BAAA,iBAAhB;MAlBI,EAJc;AA0BtBT,2CAAA,aAAuB,EAIrBoD,MAAMA,WAAA;AACJ,aAAKkC,SAAS,EACZ,UAAY7E,EAAAA,+BAAA,kBACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,OAFV,CADO,GAMT,mBAAqB,MACrB,eAAiB,MACjB,OAAS,eACT,SAAWA,EAAAA,+BAAA,oBACX,SAAWA,EAAAA,+BAAA,mBAZC,CAAd;MADI,EAJe;UA8BjB8E,8CAAgB,EAOpBC,aAAaA,SAAmCC,GAAa;AAC3D,aAAKpD,iBAAkBC,SAAmB,aAAVmD,IAAqB,WAAW,QAAhE;MAD2D,GAS7DjB,eAAeA,WAAA;AACb,cAAMC,IAAqBC,EAAAA,+CAAc,UAAvB;AAClBD,UAAUI,aAAa,QAAQ,KAAK3C,cAAc,MAAnB,CAA/B;AACA,eAAOuC;MAHM,GAWfM,eAAeA,SAAmCC,GAAmB;AACnE,aAAKQ,YAAYR,EAAWC,aAAa,MAAxB,CAAjB;MADmE,EA3BjD;2CAqCtB,kBAA4B,OAAA,OAAA,CAAA,GACvBM,6CADuB,EAK1BnC,MAAMA,WAAA;AACJ,YAAMsC,IAAQ,CACZ,CAACjF,EAAAA,+BAAA,uBAA8B,MAA/B,GACA,CAACA,EAAAA,+BAAA,yBAAgC,QAAjC,CAFY;AAId,aAAK4C,WAAW5C,EAAAA,+BAAA,mBAAhB;AACA,aAAK6C,SAAS,aAAd;AACMqC,YAAyB/B,EAAAA,+CAAS,EACtCtD,MAAM,kBACNF,SAASsF,EAF6B,CAAvB;AAIjBC,UAAS9B,aAAc4B,OAAiB;AACtC,eAAKD,YAAYC,CAAjB;QADsC,CAAxC;AAIA,aAAKlC,iBAAiB,MAAtB,EAA8BC;UAAYmC;UAAU;QAApD;AACA,aAAKrB,UAAU,MAAM,QAArB;AACA,aAAKC,WAAW,MACwB,WAA/B,KAAKrC,cAAc,MAAnB,IACHzB,EAAAA,+BAAA,2BACAA,EAAAA,+BAAA,0BAHN;MAjBI,EALoB,CAAA;2CAgC5B,cAAwB,OAAA,OAAA,CAAA,GACnB8E,6CADmB,EAMtBnC,MAAMA,WAAA;AACJ,aAAKwC,MAAMC,+CAAX;AACA,YAAMH,IAAQ,CACZ,CAACjF,EAAAA,+BAAA,uBAA8B,MAA/B,GACA,CAACA,EAAAA,+BAAA,yBAAgC,QAAjC,CAFY;AAKd,aAAK4C,WAAW5C,EAAAA,+BAAA,mBAAhB;AACA,aAAK6C,SAAS,aAAd;AACMqC,YAAyB/B,EAAAA,+CAAS,EACtCtD,MAAM,kBACNF,SAASsF,EAF6B,CAAvB;UAIR7B,aAAc4B,OAAiB;AACtC,eAAKD,YAAYC,CAAjB;QADsC,CAAxC;AAIA,aAAKrB,iBAAL,EACGZ,YAAYmC,GAAU,MADzB,EAEGnC,YAAY,KAAKsC,UAAU,IAAf,CAFf,EAGGtC,YACeI,EAAAA,+CAAS,EACrBtD,MAAM,eACNc,MAAM,GAFe,CAAvB,GAIA,MARJ,EAUGoC,YAAY,KAAKsC,UAAU,KAAf,CAVf;AAWA,aAAKxB,UAAU,MAAM,QAArB;AACA,aAAKC,WAAW,MACwB,WAA/B,KAAKrC,cAAc,MAAnB,IACHzB,EAAAA,+BAAA,2BACAA,EAAAA,+BAAA,0BAHN;MA7BI,EANgB,CAAA;2CA2CxB,aAAuB,EAIrB2C,MAAMA,WAAA;AACJ,aAAKkC,SAAS,EACZ,UAAY7E,EAAAA,+BAAA,qBACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,OACR,OAAS,SAHX,GAKA,EACE,MAAQ,eACR,MAAQ,QACR,OAAS,SAHX,CANO,GAYT,QAAU,UACV,cAAgB,MAChB,OAAS,eACT,SAAWA,EAAAA,+BAAA,oBACX,SAAWA,EAAAA,+BAAA,mBAlBC,CAAd;MADI,EAJe;2CA4BvB,eAAyB,EAIvB2C,MAAMA,WAAA;AACJ,aAAKkC,SAAS,EACZ,UAAY7E,EAAAA,+BAAA,uBACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,QACR,OAAS,SAHX,GAKA,EACE,MAAQ,eACR,MAAQ,MACR,OAAS,SAHX,GAKA,EACE,MAAQ,eACR,MAAQ,QACR,OAAS,SAHX,CAXO,GAiBT,QAAU,UACV,cAAgB,MAChB,OAAS,eACT,SAAWA,EAAAA,+BAAA,sBACX,SAAWA,EAAAA,+BAAA,qBAvBC,CAAd;MADI,EAJiB;2CAiCzB,eAAyB,EAIvB2C,MAAMA,WAAA;AACJ,aAAKkC,SAAS,EACZ,UAAY7E,EAAAA,+BAAA,uBACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,QACR,OAAS,SAHX,CADO,GAOT,QAAU,UACV,cAAgB,MAChB,OAAS,eACT,SAAWA,EAAAA,+BAAA,sBACX,SAAWA,EAAAA,+BAAA,qBAbC,CAAd;MADI,EAJiB;UA4BnBoF,kDAAoB;QAKxBE,0BACE;QASFC,2BACE;QAQFC,mBAAmB;QAInBC,oBAAoB;QAOpBC,aAAaA,SAAiCC,GAAiB;AAC7D,mBAASC,IAAI,GAAGC,GAAQA,IAAQ,KAAKC,UAAUF,CAAf,GAAoBA,IAClD,UAASG,IAAI,GAAGC,GAAQA,IAAQH,EAAMI,SAASF,CAAf,GAAoBA,IAClD,KAAIJ,MAAcK,EAAM9E,MAAM;AAC5B2E,cAAMK,cAAcH,GAAG,KAAKV,UAAU,IAAf,CAAvB;AACAQ,cAAMK,cAAcH,IAAI,GAAG,KAAKV,UAAU,KAAf,CAA3B;AACA;UAH4B;AAOlCc,kBAAQC,KACN,kBAAkBT,IAAY,oBAAoB,KAAKU,YAAL,CADpD;QAV6D;QAuB/DhB,WAAWA,SAAiCiB,GAAa;AACjDC,cAAS,KAAKC,MAAM,CAACF,IAAOA;AAIlC,iBAAqBnD,EAAAA,+CAAS;YAC5BtD,MAAM;YACN4G,KALcF,IACZ,KAAKjB,2BACL,KAAKC;YAIPmB,OAAO,KAAKlB;YACZmB,QAAQ,KAAKlB;YACbmB,KAAKL,IAAS,MAAW;UALG,CAAvB;QALgD;MA1DjC,GA4EpBM,iDAAmBA,WAAA;AACvB,aAAK1B,MAAMC,+CAAX;AACA,aAAKM,YAAY,MAAjB;MAFuB,GAuBnBoB,mDAAqB;QACzBC,YAAY;QAOZhD,eAAeA,WAAA;AACb,gBAAMC,IAAqBC,EAAAA,+CAAc,UAAvB;AAClBD,YAAUI,aAAa,SAAS,GAAG,KAAK2C,UAAR,EAAhC;AACA,iBAAO/C;QAHM;QAWfM,eAAeA,SAAkCC,GAAmB;AAClE,eAAKwC,aAAaC,SAASzC,EAAWC,aAAa,OAAxB,GAAmC,EAA5C;AAClB,eAAKyC,aAAL;QAFkE;QASpEC,gBAAgBA,WAAA;AACd,iBAAO,EACL,WAAa,KAAKH,WADb;QADO;QAUhBI,gBAAgBA,SAAkCC,GAAyB;AACzE,eAAKL,aAAaK,EAAA;AAClB,eAAKH,aAAL;QAFyE;QAU3EI,WAAWA,SAAkC5G,GAAoB;AAC/D,gBAAM6G,IAAiB7G,EAAU8G,SAC/B,4BADqB;AAGvBD,YAAeE,QAAf;AACA,cAAIxF,IAAasF,EAAevF,SAAS,OAAxB,EAAkCC;AACnD,mBAAS4D,IAAI,GAAGA,IAAI,KAAKmB,YAAYnB,KAAK;AACxC,kBAAM6B,IAAYhH,EAAU8G,SAC1B,uBADgB;AAGlBE,cAAUD,QAAV;AACAxF,cAAW0F,QAAQD,EAAUE,kBAA7B;AACA3F,gBAAayF,EAAUG;UANiB;AAQ1C,iBAAON;QAdwD;QAqBjEO,SAASA,SAAkCP,GAAqB;AAC9D,cAAIG,IAAYH,EAAeQ,oBAC7B,OADc;AAKhB,eADMC,IAAc,CAAA,GACbN,IACDA,GAAUO,kBAAV,KAIJD,EAAYxH,KAAKkH,EAAUQ,gBAA3B,GAHER,IAAYA,EAAUS,aAAV;AAOhB,eAAStC,IAAI,GAAGA,IAAI,KAAKmB,YAAYnB,KAAK;AACxC,kBAAM5D,IAAa,KAAKD,SAAS,QAAQ6D,CAAtB,EAA0B5D,WAAYmG;AACrDnG,iBAAc,CAAC+F,EAAYK,SAASpG,CAArB,KACjBA,EAAWqG,WAAX;UAHsC;AAM1C,eAAKtB,aAAagB,EAAYO;AAC9B,eAAKrB,aAAL;AAEA,eAASrB,IAAI,GAAGA,IAAI,KAAKmB,YAAYnB,KAAK;AACxC,gBAAA;AAAA,qBAAA,IAAAmC,EAAYnC,CAAZ,MAAA,EAAgB2C,UAAU,MAAM,QAAQ3C,CAAxC;UADwC;QAxBoB;QAiChE4C,iBAAiBA,SAAkClB,GAAqB;AAClEG,cAAYH,EAAeQ,oBAAoB,OAAnC;AAChB,cAAIlC,IAAI;AACR,iBAAO6B,KAAW;AAChB,gBAAIA,EAAUO,kBAAV,GAA+B;AACjCP,kBAAYA,EAAUS,aAAV;AACZ;YAFiC;AAInC,kBAAMrC,IAAQ,KAAK9D,SAAS,QAAQ6D,CAAtB;AACb6B,cAA4BQ,mBAC3BpC,KAASA,EAAM7D,WAAYmG;gBACjBV,EAAUS,aAAV;AACZtC;UATgB;QAHoD;QAmBxEqB,cAAcA,WAAA;AACR,eAAKF,cAAc,KAAKhF,SAAS,OAAd,IACrB,KAAK2C,YAAY,OAAjB,IACU,KAAKqC,cAAe,KAAKhF,SAAS,OAAd,KAC9B,KAAK4B,iBAAiB,OAAtB,EACGZ,YAAY,KAAKsC,UAAU,IAAf,CADf,EAEGtC,YAAY,KAAKsC,UAAU,KAAf,CAFf;AAKF,mBAASO,IAAI,GAAGA,IAAI,KAAKmB,YAAYnB,IACnC,KAAI,CAAC,KAAK7D,SAAS,QAAQ6D,CAAtB,GAA0B;AAC7B,kBAAMC,IAAQ,KAAK/C,iBAAiB,QAAQ8C,CAA9B,EAAiC6C,SAASC,EAAAA,0CAAMC,KAAhD;AACJ,kBAAN/C,KACFC,EAAM9C,YAAY/C,EAAAA,+BAAA,0BAAlB;UAH2B;AAQjC,eAAS4F,IAAI,KAAKmB,YAAY,KAAKhF,SAAS,QAAQ6D,CAAtB,GAA0BA,IACtD,MAAKlB,YAAY,QAAQkB,CAAzB;QAnBU;MAzHW,GAoJrBgD,+CAAiBA,WAAA;AAErB,aAAKzD,MAAMC,+CAAX;AAEA,aAAK2B,aAAa;AAClB,aAAKE,aAAL;AAEA,aAAK4B,WAAW,IAAIC,EAAAA,sDAAY,CAAC,uBAAD,GAA2B,IAA3C,CAAhB;MAPqB;AAWZC,QAAAA,2CACT,uBACWC,EAAAA,4DAA0B,8BAA8B,KAAnE,CAFF;UAQMC,0DAA4BA,WAAA;AAChC,aAAKnF,WAAW,MACP9D,EAAAA,+BAAA,qBAA4BY,QACjC,MACA,KAAKH,UAAUd,QAAQuJ,gBAAgB,MAAM,IAFxC,CADT;MADgC,GAiB5BC,qDAAuB,EAC3BC,OAAO,OAOPrF,eAAeA,WAAA;AACb,cAAMC,IAAqBC,EAAAA,+CAAc,UAAvB;AAClBD,UAAUI,aAAa,MAAM,GAAG,KAAKgF,KAAR,EAA7B;AACA,eAAOpF;MAHM,GAWfM,eAAeA,SAA6BC,GAAmB;AAGvDE,YAAyC,YAAlCF,EAAWC,aAAa,IAAxB;AACb,aAAKd,UAAUe,CAAf;MAJ6D,GAkB/Df,WAAWA,SAA6Be,GAAa;AAEnD,aAAKC;UAAY;UAAM;QAAvB;AACA,aAAKA,YAAY,WAAW,IAA5B;AAEID,cACF,KAAK3B,iBAAiB,IAAtB,EAA4BjB,SAAS,QAArC,GACI7B,EAAAA,+BAAA,yBACF,KAAK2D,iBAAiB,SAAtB,EAAiCZ,YAC/B/C,EAAAA,+BAAA,qBADF;AAKAA,UAAAA,+BAAA,qBACF,KAAK0E,YAAY,QAAQ,IAAzB,GACA,KAAKf,iBAAiB,MAAtB,EAA8BZ,YAAY/C,EAAAA,+BAAA,gBAA1C;AAGF,aAAKoJ,QAAQ3E;MAlBsC,EArC1B,GA8DvB4E,iDAAmBA,WAAA;AACN,aAAKnJ,SAAS,OAAdgF,EACR9B,aAAa,SAA+BC,GAAU;AACvDG,cAAkB,iBAAVH,KAAoC,eAAVA;AACxC,gBAAMpB,IAAQ,KAAKwB,eAAL;AACVD,gBAAUvB,EAAMmH,SAClBnH,EAAMyB,UAAUF,CAAhB;QAJ2D,CAA/D;AAQA,aAAKE,UAAU,IAAf;AACA,aAAKI,WAAW,MAAK;AACnB,cAAMwF,IAAQ,KAAK7H,cAAc,OAAnB;AACd,cAAI8H,IAAUvJ,EAAAA,+BAAA;AACd,WAAc,iBAAVsJ,KAAoC,eAAVA,OACtBE,IACM,iBAAVF,IACItJ,EAAAA,+BAAA,iCACAA,EAAAA,+BAAA,kCAEJuJ,KACE,OACAC,EAAI5I,QAAQ,MAAM,KAAKH,UAAUd,QAAQuJ,gBAAgB,OAAO,IAAhE;AAGN,iBAAOK;QAdY,CAArB;MAXuB;QA6BdR,2CAAS,wBAAwBE,uDAA5C;AAEWF,QAAAA,2CAAS,eAAelC,8CAAnC;AAEWtE,QAAAA,gDAAc,qBAAqB6C,+CAA9C;AAEWqE,QAAAA,kDACT,qBACA3C,kDACA8B,4CAHF;QAMWa,kDACT,uBACAN,oDACAE,8CAHF;AAOA7G,QAAAA,2CAAajD,oCAAb;AAx+BA,UAAAkD,+BAAA,EA+BalD,QAAAA,qCA/Bb;AC+CO,UAAMA,6CAA2C,CAAA,GAwBlDmK,2DAAuB;QAM3BC,gBAAgBA,SAAgCC,GAAsB;AAChE,eAAKC,mBAAmBD,MAGxBA,KACF,KAAKE,qBAAqB,OAA1B,EAAmC/G,YACjC/C,EAAAA,+BAAA,yBADF,GAGI,KAAK+B,SAAS,QAAd,KACF,KAAK4C,gBAAgB,SAAS,QAA9B,KAGF,KAAKD,YAAY,SAAS,IAA1B,GAEF,KAAKmF,iBAAiBD;QAd8C;QAqBtEG,eAAeA,WAAA;AAEb,cAAIC,IAAc;AACd,eAAKC,WAAW3B,WAClB0B,IACEhK,EAAAA,+BAAA,2BAAkC,MAAM,KAAKiK,WAAWC,KAAK,IAArB;AAIrCC,YAAAA,4CAAP;AACA,cAAI;AACF,iBAAKC,cAAcJ,GAAa,QAAhC;UADE,UAAJ;AAGSK,cAAAA,2CAAP;UADQ;QAZG;QAwBftG,eAAeA,SAEbuG,GAAqB;AAErB,gBAAMtG,IAAqBC,EAAAA,+CAAc,UAAvB;AACdqG,eACFtG,EAAUI,aAAa,QAAQ,KAAK3C,cAAc,MAAnB,CAA/B;AAEF,mBAASmE,IAAI,GAAGA,IAAI,KAAK2E,mBAAmBjC,QAAQ1C,KAAK;AACvD,kBAAM4E,IAAqBvG,EAAAA,+CAAc,KAAvB,GACZwG,IAAW,KAAKF,mBAAmB3E,CAAxB;AACjB4E,cAAUpG,aAAa,QAAQqG,EAASC,QAAT,CAA/B;AACAF,cAAUpG;cAAa;cAASqG,EAASE,MAAT;YAAhC;AACIL,iBAAgB,KAAKM,aACvBJ,EAAUpG,aAAa,WAAW,KAAKwG,UAAUhF,CAAf,CAAlC;AAEF5B,cAAU6G,YAAYL,CAAtB;UARuD;AAYpD,eAAKX,kBACR7F,EAAUI,aAAa,cAAc,OAArC;AAEF,iBAAOJ;QArBc;QA6BvBM,eAAeA,SAAgCC,GAAmB;AAChE,eAAK0F,aAAa,CAAA;AAClB,eAAKM,qBAAqB,CAAA;AAC1B,mBAAS3E,IAAI,GAAGkF,GAAYA,IAAYvG,EAAWwG,WAAWnF,CAAtB,GAA2BA,IACjE,KAAyC,UAArCkF,EAAUE,SAASC,YAAnB,GAA4C;AAC9C,gBAAMC,IAAeJ;AACrB,kBAAMK,IAAUD,EAAa1G,aAAa,MAA1B;AACV4G,gBACJF,EAAa1G,aAAa,OAA1B,KACA0G,EAAa1G,aAAa,OAA1B;AACF,iBAAKyF,WAAW1J,KAAK4K,CAArB;AACMjJ,gBAAqBmJ,EAAAA;cACzB,KAAK5K;cACL2K;cACAD;cACA;YAJe;AAMA,qBAAbjJ,IACF,KAAKqI,mBAAmBhK,KAAK2B,CAA7B,IAEAiE,QAAQmF,IACN,sCAAsCH,CAAtC,cADF;UAhB4C;AAsBlD,eAAKpB,cAAL;AACWwB,YAAAA,gDAAc,IAAzB;AAGA,eAAK5B,eAAyD,YAA1CpF,EAAWC,aAAa,YAAxB,CAApB;QA9BgE;QAqClE0C,gBAAgBA,WAAA;AACd,cAAI,CAAC,KAAKqD,mBAAmBjC,UAAU,KAAKuB,eAC1C,QAAO;AAET,gBAAMzC,IAAQoE,uBAAOC,OAAO,IAAd;AACd,cAAI,KAAKlB,mBAAmBjC,QAAQ;AAClClB,cAAA,SAAkB,CAAA;AAClB,qBAASxB,IAAI,GAAGA,IAAI,KAAK2E,mBAAmBjC,QAAQ1C,IAClDwB,GAAA,OAAgB7G,KAAK;cAGnB,MAAQ,KAAKgK,mBAAmB3E,CAAxB,EAA2B8E,QAA3B;cACR,IAAM,KAAKH,mBAAmB3E,CAAxB,EAA2B+E,MAA3B;YAJa,CAArB;UAHgC;AAW/B,eAAKd,mBACRzC,EAAA,gBAAyB;AAE3B,iBAAOA;QAnBO;QA2BhBD,gBAAgBA,SAAgCC,GAA0B;AACxE,eAAK6C,aAAa,CAAA;AAClB,eAAKM,qBAAqB,CAAA;AAC1B,cAAInD,EAAA,OACF,UAASxB,IAAI,GAAGA,IAAIwB,EAAA,OAAgBkB,QAAQ1C,KAAK;AAC/C,gBAAM8F,IAAQtE,EAAA,OAAgBxB,CAAhB;AACR1D,gBAAqBmJ,EAAAA,4DACzB,KAAK5K,WACLiL,EAAA,IACAA,EAAA,MACA,EAJe;AAMjB,iBAAKzB,WAAW1J,KAAK2B,EAASwI,QAAT,CAArB;AACA,iBAAKH,mBAAmBhK,KAAK2B,CAA7B;UAT+C;AAYnD,eAAK6H,cAAL;AACWwB,YAAAA,gDAAc,IAAzB;AACA,eAAK5B,eAA0C,UAA3BvC,EAAA,gBAAmC,QAAQ,IAA/D;QAlBwE;QA0B1EC,WAAWA,SAET5G,GAAoB;AAcpB,gBAAMkL,IAA8B1H,EAAAA,+CAAc,OAAvB;AAC3B0H,YAAmBvH,aAAa,QAAQ,6BAAxC;AACA,cAAMwH,IAAyB3H,EAAAA,+CAAc,WAAvB;AACtB2H,YAAcxH,aAAa,QAAQ,OAAnC;AACAuH,YAAmBd,YAAYe,CAA/B;AAGA,mBAAShG,IAAI,GAAGA,IAAI,KAAKqE,WAAW3B,QAAQ1C,KAAK;AAC/C,kBAAMiG,IAAwB5H,EAAAA,+CAAc,OAAvB;AACrB4H,cAAazH,aAAa,QAAQ,uBAAlC;AACA,gBAAM0H,IAAqB7H,EAAAA,+CAAc,OAAvB;AAClB6H,cAAU1H;cAAa;cAAQ;YAA/B;AACA,kBAAM2H,IAAwBC,EAAAA,gDAAe,KAAK/B,WAAWrE,CAAhB,CAAxB;AACrBkG,cAAUjB,YAAYkB,CAAtB;AACAF,cAAahB,YAAYiB,CAAzB;AACMG,gBAAoBhI,EAAAA,+CAAc,MAAvB;AACjB4H,cAAahB,YAAYoB,CAAzB;AAEAC,cAAKrB,YAAYgB,CAAjB;AACAK,gBAAOD;UAZwC;AAe3C3E,cAAqB6E,EAAAA,sCACzBR,GACAlL,CAFqB;AAKL,qCAAd,KAAKZ,OACPyH,EAAe8C,cAAc,KAAKP,gBAAgB,YAAlD,IAEAvC,EAAe5C,YAAY,iBAA3B;AAIS6G,YAAAA,gDAAc,IAAzB;AACA,iBAAOjE;QAjDa;QAwDtBO,SAASA,SAAgCP,GAA8B;AAErE,eAAK2C,aAAa,CAAA;AAClB,eAAKW,YAAY,CAAA;AACjB,eAAKL,qBAAqB,CAAA;AAC1B,cAAI6B,IAAa9E,EAAeQ,oBAAoB,OAAnC;AACjB,iBAAOsE,KAAc,CAACA,EAAWpE,kBAAX,KAAgC;AACpD,gBAAMmD,IAAUiB,EAAW3K,cAAc,MAAzB;AAChB,iBAAKwI,WAAW1J,KAAK4K,CAArB;AACMjJ,gBAAW,KAAKzB,UAAUkB,YAAYwJ,GAAS,EAApC;AACjB,iBAAKZ,mBAAmBhK,KAAK2B,CAA7B;AAEA,iBAAK0I,UAAUrK,KAAK6L,EAAW5K,EAA/B;AACA4K,gBACEA,EAAWxE,kBAAkBwE,EAAWxE,eAAeyE,YAA1B;UARqB;AAUtD,eAAKtC,cAAL;AACWwB,YAAAA,gDAAc,IAAzB;AAGI3B,cAAgBtC,EAAe7F,cAAc,YAA7B;AACpB,cAAsB,SAAlBmI,MACFA,IAAkC,WAAlBA,GACZ,KAAKC,mBAAmBD,GAC1B,KAAIA,GAAe;AACjB,iBAAKD,eAAe,IAApB;AAEK2C,gBAAAA;AAAL,qBAAKA,IAALA,KAAKA,yBAAAA,EAAsB/D,UAAU,MAAM,OAA3C;AACA,iBAAK+D,uBAAuB;UAJX,OAKZ;AAECC,gBAAkB,KAAKxK,SAAS,OAAd,EAAwBC;AAEhD,gBADA,KAAKsK,uBAAuBC,EAAiBpE,iBAErCqE,KAAaD,EAAiBF,YAAjB,GACnBG,EAAWC,OAAX,GACAD,EAAWE,eAAX;AAEF,iBAAK/C,eAAe,KAApB;UATK;QA7B0D;QAgDvEgD,SAASA,WAAA;AACP,iBAAO,KAAK1C;QADL;QAQT2C,cAAcA,WAAA;AAGZ,iBAAO,KAAKrC;QAHA;QAcdsC,eAAeA,SAEbC,GACAC,GAAa;AAEb,cAAMC,IAAc,KAAKvM,UAAUwM,gBAAgBH,CAA/B;AACpB,cAA8B,OAA1BE,EAAYlL,QAAZ,GAAJ;AAIMoL,gBAAUF,EAAYtC,QAAZ;AACVyC,gBAAS,KAAK1M,UAAUwM,gBAAgBF,CAA/B;AAEf,gBAAIK,IAAS;AACb,qBAASxH,IAAI,GAAGA,IAAI,KAAK2E,mBAAmBjC,QAAQ1C,IAC9C,MAAK2E,mBAAmB3E,CAAxB,EAA2B+E,MAA3B,MAAuCmC,MACzC,KAAK7C,WAAWrE,CAAhB,IAAqBuH,EAAOzC,QAAP,GACrB,KAAKH,mBAAmB3E,CAAxB,IAA6BuH,GAC7BC,IAAS;AAGTA,kBACF,KAAKC,mBAAmBH,GAASC,EAAOzC,QAAP,CAAjC,GACWa,EAAAA,gDAAc,IAAzB;UAjBF;QAHa;QA6Bf+B,eAAeA,SAEbpL,GAAwC;AAExC,gBAAMqL,IAAUrL,EAASwI,QAAT;AAChB,cAAI0C,IAAS,OACTF;AACJ,mBAAStH,IAAI,GAAGA,IAAI,KAAK2E,mBAAmBjC,QAAQ1C,IAC9C,MAAK2E,mBAAmB3E,CAAxB,EAA2B+E,MAA3B,MAAuCzI,EAASyI,MAAT,MACzCuC,IAAU,KAAKjD,WAAWrE,CAAhB,GACV,KAAKqE,WAAWrE,CAAhB,IAAqB2H,GACrBH,IAAS;AAGTA,gBACF,KAAKC,mBAAmBH,GAAmBK,CAA3C,GACWhC,EAAAA,gDAAc,IAAzB;QAdsC;QAwB1C8B,oBAAoBA,SAElBH,GACAK,GAAe;AAEf,eAAKxD,cAAL;AAEA,cAAMyD,IAAU,KAAKC,QAAQC,EAAAA,sDAAQC,IAArB;AAChB,cAAIH,KAAWA,EAAQI,gBAAR,GAA2B;AAClCrO,gBAASiO,EAAQlL,aAAR,EAAwBuL,aAAa,KAArC;AACf,qBAASjI,IAAI,GAAG3D,GAAQA,IAAQ1C,EAAOqG,CAAP,GAAYA,IAEzB,6BAAf3D,EAAMpC,QACNiO,EAAAA,mCAAMC,OAAOb,GAASjL,EAAMR,cAAc,MAApB,CAAtB,KAEAQ,EAAMmI,cAAcmD,GAAS,MAA7B;UAPoC;QAL3B;QAsBjB7N,mBAAmBA,SAEjBC,GAA2D;AAE3D,cAASC,CAAL,KAAKA,YAAT;AAIA,gBAAMsB,IAAO,KAAKO,cAAc,MAAnB,GACPuM,IAA0B,EAC9BnO,MAAO,KAA4BoO,WACnCC,YAAY,EAAChN,MAAMA,GAAMiN,QAAQ,KAAKlE,WAA1B,EAFkB;cAIxB1J,KAAK,EACXC,SAAS,MACTG,MAAMX,EAAAA,+BAAA,qBAA4BY,QAAQ,MAAMM,CAA1C,GACNJ,UAAsBC,EAAAA,mDAAgB,MAAMiN,CAAlC,EAHC,CAAb;AAOA,gBAAI,CAAC,KAAKI,YAAL,EACH,MAASxI,IAAI,GAAGA,IAAI,KAAK2E,mBAAmBjC,QAAQ1C,KAAK;AACjDyI,kBAAS,KAAK9D,mBAAmB3E,CAAxB;AACf,oBAAM0I,IAAmB,EACvBzO,MAAM,iBACNO,QAAQ,EACNC,KAAK,EACHa,MAAMmN,EAAO3D,QAAP,GACNlJ,IAAI6M,EAAO1D,MAAP,GACJ9K,MAAMwO,EAAOvM,QAAP,EAHH,EADC,EAFe;AAUzBnC,gBAAQY,KAAK,EACXC,SAAS,MACTG,MAAMX,EAAAA,+BAAA,yBAAgCY,QAAQ,MAAMyN,EAAO3D,QAAP,CAA9C,GACN5J,UAAsBC,EAAAA;gBAAgB;gBAAMuN;cAAlC,EAHC,CAAb;YAZuD;UAjB3D;QAF2D;MAjYlC;iDA0a7B,yBAAmC,OAAA,OAAA,CAAA,GAC9B5E,0DAD8B,EAKjC/G,MAAMA,WAAA;AACJ,YAAM4L,IAAsBC,EAAAA,gDAAc,IAAI,IAA7B;AACXC,YAA0BtL,EAAAA,+CAAS,EACvCtD,MAAM,eACNc,MAAM4N,EAFiC,CAAvB;AAIlBE,UAAWrL,aAAwBsL,EAAAA,wCAAnC;AACAD,UAAUE,cAAc,KAAxB;AACA,aAAKhL,iBAAL,EACGZ,YAAY/C,EAAAA,+BAAA,4BADf,EAEG+C,YAAY0L,GAAW,MAF1B,EAGG1L;UAAY;UAAI;QAHnB;AAIA,aAAK8F,WAAW,IAAI6E,EAAAA,sDAAQ,CAAC,uBAAD,GAA2B,IAAvC,CAAhB;AACA,SACG,KAAKjN,UAAUd,QAAQiP,YACrB,KAAKnO,UAAUd,QAAQkP,mBACtB,KAAKpO,UAAUd,QAAQkP,gBAAgBlP,QAAQiP,aACnD5O,EAAAA,+BAAA,kCAEA,KAAK8O,eAAe9O,EAAAA,+BAAA,8BAApB;AAEF,aAAK6C,SAAS,kBAAd;AACA,aAAKiB,WAAW9D,EAAAA,+BAAA,8BAAhB;aACK4C,WAAW5C,EAAAA,+BAAA,8BAAhB;AACA,aAAKiK,aAAa,CAAA;AAClB,aAAKM,qBAAqB,CAAA;AAC1B,aAAKZ,eAAe,IAApB;AACA,aAAK2C,uBAAuB;MA3BxB,GAqCNyC,iBAAiBA,WAAA;AACf,eAAO,CAAC,KAAKtN,cAAc,MAAnB,GAA4B,KAAKwI,YAAY,KAA9C;MADQ,GAGjBgE,WAAW,0BA7CsB,CAAA;iDAgDnC,uBAAiC,OAAA,OAAA,CAAA,GAC5BvE,0DAD4B,EAK/B/G,MAAMA,WAAA;AACJ,YAAM4L,IAAsBC,EAAAA,gDAAc,IAAI,IAA7B;AACXC,YAA0BtL,EAAAA,+CAAS,EACvCtD,MAAM,eACNc,MAAM4N,EAFiC,CAAvB;AAIlBE,UAAUrL,aAAwBsL,EAAAA,wCAAlC;AACAD,UAAUE,cAAc,KAAxB;AACA,aAAKhL,iBAAL,EACGZ,YAAY/C,EAAAA,+BAAA,0BADf,EAEG+C,YAAY0L,GAAW,MAF1B,EAGG1L;UAAY;UAAI;QAHnB;AAIA,aAAKD,iBAAiB,QAAtB,EACG2F,SAASC,EAAAA,0CAAMC,KADlB,EAEG5F,YAAY/C,EAAAA,+BAAA,2BAFf;AAGA,aAAK6I,WAAW,IAAI6E,EAAAA,sDAAQ,CAAC,uBAAD,GAA2B,IAAvC,CAAhB;AACA,SACG,KAAKjN,UAAUd,QAAQiP,YACrB,KAAKnO,UAAUd,QAAQkP,mBACtB,KAAKpO,UAAUd,QAAQkP,gBAAgBlP,QAAQiP,aACnD5O,EAAAA,+BAAA,gCAEA,KAAK8O,eAAe9O,EAAAA,+BAAA,4BAApB;aAEG6C,SAAS,kBAAd;AACA,aAAKiB,WAAW9D,EAAAA,+BAAA,4BAAhB;AACA,aAAK4C,WAAW5C,EAAAA,+BAAA,4BAAhB;AACA,aAAKiK,aAAa,CAAA;AAClB,aAAKM,qBAAqB,CAAA;AAC1B,aAAKZ,eAAe,IAApB;AACA,aAAK2C,uBAAuB;MA9BxB,GAwCNyC,iBAAiBA,WAAA;AACf,eAAO,CAAC,KAAKtN,cAAc,MAAnB,GAA4B,KAAKwI,YAAY,IAA9C;MADQ,GAGjBgE,WAAW,wBAhDoB,CAAA;UAwD3Be,kEAA8B,EAIlCrM,MAAMA,WAAA;AACJ,aAAKgB,iBAAL,EAAwBZ,YACtB/C,EAAAA,+BAAA,iCADF;AAGA,aAAK8J,qBAAqB,OAA1B;AACA,aAAKnG,iBAAiB,iBAAtB,EACGZ,YAAY/C,EAAAA,+BAAA,2BADf,EAEG+C,YACeI,EAAAA,+CAAS,EACrBtD,MAAM,kBACNoP,SAAS,KAFY,CAAvB,GAIA,YAPJ;AASA,aAAKpM,SAAS,kBAAd;AACA,aAAKiB,WAAW9D,EAAAA,+BAAA,mCAAhB;aACKkP,cAAc;MAhBf,EAJ4B;AAuBpC3P,iDAAA,8BAAwCyP;UAUlCG,6DAAN,cAAqCC,EAAAA,sDAArC;QAAAC,cAAA;AAAA,gBAAA,GAAAC,SAAA;AAIE,eAAAC,uBAAuB;QAJzB;QAgBqBC,YAAYC,GAAS;AACtC,gBAAMD,YAAYC,CAAlB;AACA,eAAKF,uBAAuB;AAC5B,eAAKG,kBAAkBC;QAHe;QAS/BC,iBAAiBvM,GAAa;AACrC,gBAAMuM,iBAAiBvM,CAAvB;AACA,eAAKkM,uBAAuB;QAFS;MAzBzC,GA+BMM,iEAA6B,EAIjClN,MAAMA,WAAA;AACJ,cAAMqD,IAAQ,IAAImJ;UACLW,EAAAA;UACX,KAAKC;QAFO;AAKd,aAAKpM,iBAAL,EACGZ,YAAY/C,EAAAA,+BAAA,2BADf,EAEG+C,YAAYiD,GAAO,MAFtB;AAGA,aAAKgK,qBAAqB,IAA1B;AACA,aAAKC,iBAAiB,IAAtB;AACA,aAAKpN,SAAS,kBAAd;AACA,aAAKiB,WAAW9D,EAAAA,+BAAA,6BAAhB;AACA,aAAKkP,cAAc;MAbf,GA0BNa,YAAYA,SAEV5E,GAAe;AAEf,YAAM+E,IAAc,KAAKzM,eAAL;AACpB,cAAM0M,IAAUD,EAAYzP,UAAU2P,iBAAtB;AAChBjF,YAAUA,EAAQvK,QAAQ,cAAc,GAA9B,EAAmCA,QAAQ,UAAU,EAArD;AACV,YAAI,CAACuK,EACH,QAAO;AAOT,cAAM5L,KAFH2Q,EAAYzP,UAA2B4P,mBACxCH,EAAYzP,WACWoN,aAAa,KAAvB,GACTyC,IAAenF,EAAQF,YAAR;AACrB,iBAASrF,IAAI,GAAGA,IAAIrG,EAAO+I,QAAQ1C,KAAK;AACtC,cAAIrG,EAAOqG,CAAP,EAAUpE,OAAO,KAAKiC,eAAL,EAAuBjC,GAC1C;AAGF,gBAAM+O,IAAWhR,EAAOqG,CAAP,EAAUnE,cAAc,MAAxB;AACjB,cAAI8O,KAAYA,EAAStF,YAAT,MAA2BqF,EACzC,QAAO;QAP6B;AAaxC,YAAIJ,EAAYtQ,WACd,QAAOuL;AAIT,SADMqF,IAAQL,EAAQxO,YAAYwJ,GAAS,EAA7B,MACDqF,EAAM9F,QAAN,MAAoBS,KAE/BgF,EAAQM,mBAAmBD,EAAM7F,MAAN,GAAeQ,CAA1C;AAEGqF,cACC,KAAKjB,uBACF,KAAKG,kBAGRS,EAAQM,mBAAmB,KAAKf,gBAAgB/E,MAArB,GAA8BQ,CAAzD,IAFA,KAAKuE,kBAAkBS,EAAQO,eAAevF,GAAS,EAAhC,IAKzBgF,EAAQO,eAAevF,GAAS,EAAhC;AAGJ,eAAOA;MAhDQ,EAhCgB;iDAmFnC,wBAAkC0E;UAuB5Bc,oFACJ,iCAMIC,4DAAwB,EAM5BC,kBAAkBA,WAAA;AAEhB,eAAO,KAAKpP,cAAc,MAAnB;MAFS,GAWlBqP,iBAAiBA,SAEf5D,GACAK,GAAe;AAEXO,UAAAA,mCAAMC,OAAOb,GAAS,KAAK2D,iBAAL,CAAtB,MACF,KAAKzG,cAAcmD,GAAS,MAA5B,GAIA,KAAKzJ,YAHW,KAAKlC,mBACjB5B,EAAAA,+BAAA,gCACAA,EAAAA,+BAAA,iCACoBY;UAAQ;UAAM2M;QAAtB,CAAhB;MAPa,GAmBjBwD,yBAAyBA,SAEvBC,GACAC,GAAkB;AAWlB,YAAMC,IAAsBC,EAAAA,gDAC1B,KAAKN,iBAAL,GACA,KAAKpQ,SAFU;AAMjB,SADM2Q,KADAC,IAAcH,KAAYA,EAASzD,QAAQC,EAAAA,sDAAQC,IAAzB,MACG0D,EAAYzD,gBAAZ,KAOjC,KAAK0D,aAAa,KAAlB,KALA,KAAKC,oBAAoB,CAAA,GACzB,KAAKC,YAAY;AAQnB,YAAIR,EAAW9G,KAAK,IAAhB,MAA0B,KAAKD,WAAWC,KAAK,IAArB,EAE5B,MAAKsH,YAAYP;aAFnB;AAKA,cAAIA,EAAS3I,WAAW0I,EAAW1I,OACjC,OAAMmJ,WAAW,kDAAX;AAEH,eAAKD,cAER,KAAKD,oBAAoB,CAAA,GACzB,KAAKC,YAAY,CAAA;AAGnB,mBAAS5L,IAAI,GAAGA,IAAI,KAAKqE,WAAW3B,QAAQ1C,KAAK;AAC/C,gBAAMC,IAAQ,KAAK9D,SAAS,QAAQ6D,CAAtB;AACVC,kBACI7D,IAAa6D,EAAM7D,WAAYmG,kBACrC,KAAKoJ,kBAAkB,KAAKC,UAAU5L,CAAf,CAAvB,IAA4C5D,GAE1CoP,KACApP,KACA,CAACiP,EAAS7I,SAAS,KAAKoJ,UAAU5L,CAAf,CAAlB,MAGD5D,EAAWqG,WAAX,GACArG,EAAWyB,eAAX,EAA4BiJ,eAA5B;UAZ2C;AAiBjD,eAAKzC,aAAc,CAAA,EAAgByH,OAAOV,CAAvB;AAEnB,eAAKzG,qBAAqB,CAAA;AAC1B,eAAS3E,IAAI,GAAGA,IAAI,KAAKqE,WAAW3B,QAAQ1C,IACpC1D,KAAqBmJ,EAAAA,4DACzB,KAAK5K,WACL,MACA,KAAKwJ,WAAWrE,CAAhB,GACA,EAJe,GAMjB,KAAK2E,mBAAmBhK,KAAK2B,CAA7B;AAGF,eAAK+E,aAAL;cACA,KAAKuK,YAAYP;AAGf,iBAASrL,IAAI,GAAGA,IAAI,KAAKqE,WAAW3B,QAAQ1C,IAE1C,KADM+L,IAAkB,KAAKH,UAAU5L,CAAf,GACpB+L,KAAW,KAAKJ,mBAAmB;AAGhCvP,kBAAAA;AAAL,eAAK,SAAAA,IAD0B,KAAKuP,kBAAkBI,CAAvB3P,KAC1B,IAAAA,EAAYuG,UAAU,MAAM,QAAQ3C,CAApC,MAEH,OAAO,KAAK2L,kBAAkBI,CAAvB;YAL4B;;QAlD3C;MA3BkB,GA6FpB1K,cAAcA,WAAA;AACZ,iBAASrB,IAAI,GAAGA,IAAI,KAAKqE,WAAW3B,QAAQ1C,KAAK;AAC/C,cAAMgM,IAAW,KAAK1R,SAAS,YAAY0F,CAA1B;AACjB,cAAIgM,GAAU;AAILzH,cAAAA,4CAAP;AACA,gBAAI;AACFyH,gBAASC,SAAS,KAAK5H,WAAWrE,CAAhB,CAAlB;YADE,UAAJ;AAGSyE,gBAAAA,2CAAP;YADQ;UAPE,MAYNyH,KAAyB3O,EAAAA,+CAAS;YACtCtD,MAAM;YACNc,MAAM,KAAKsJ,WAAWrE,CAAhB;UAFgC,CAAvB,GAIjB,KAAK9C,iBAAiB,QAAQ8C,CAA9B,EACG6C,SAASC,EAAAA,0CAAMC,KADlB,EAEG5F,YAAY+O,GAAU,YAAYlM,CAFrC;QAlB6C;AAwBjD,aAASA,IAAI,KAAKqE,WAAW3B,QAAQ,KAAKvG,SAAS,QAAQ6D,CAAtB,GAA0BA,IAC7D,MAAKlB,YAAY,QAAQkB,CAAzB;AAIF,SADMmM,IAAS,KAAKhQ,SAAS,QAAd,OAET,KAAKkI,WAAW3B,SACb,KAAKpI,SAAS,MAAd,KACH6R,EAAOhP,YAAY/C,EAAAA,+BAAA,+BAAsC,MAAzD,IAGE,KAAKE,SAAS,MAAd,KACF6R,EAAOC,YAAY,MAAnB;MArCM,GAgDdjO,eAAeA,WAAA;AACb,cAAMC,IAAqBC,EAAAA,+CAAc,UAAvB;UACRG,aAAa,QAAQ,KAAKyM,iBAAL,CAA/B;AACA,iBAASjL,IAAI,GAAGA,IAAI,KAAKqE,WAAW3B,QAAQ1C,KAAK;AAC/C,gBAAM4E,IAAqBvG,EAAAA,+CAAc,KAAvB;AAClBuG,YAAUpG,aAAa,QAAQ,KAAK6F,WAAWrE,CAAhB,CAA/B;AACA5B,YAAU6G,YAAYL,CAAtB;QAH+C;AAKjD,eAAOxG;MARM,GAgBfM,eAAeA,SAA2BC,GAAmB;AAC3D,YAAMrD,IAAOqD,EAAWC,aAAa,MAAxB;AACb,aAAKsM,gBAAgB,KAAKD,iBAAL,GAAyB3P,CAA9C;AACM+Q,YAAiB,CAAA;AACvB,cAAMhB,IAAW,CAAA;AACjB,iBAASrL,IAAI,GAAGkF,GAAYA,IAAYvG,EAAWwG,WAAWnF,CAAtB,GAA2BA,IACxB,WAArCkF,EAAUE,SAASC,YAAnB,MACFgH,EAAK1R,KAAMuK,EAAsBtG,aAAa,MAAnC,CAAX,GACAyM,EAAS1Q,KAAMuK,EAAsBtG,aAAa,SAAnC,CAAf;AAGJ,aAAKuM;UAAwBkB;UAAMhB;QAAnC;MAX2D,GAkB7D/J,gBAAgBA,WAAA;AACd,cAAME,IAAQoE,uBAAOC,OAAO,IAAd;AACdrE,UAAA,OAAgB,KAAKyJ,iBAAL;AACZ,aAAK5G,WAAW3B,WAClBlB,EAAA,SAAkB,KAAK6C;AAEzB,eAAO7C;MANO,GAchBD,gBAAgBA,SAA2BC,GAAqB;AAC9D,aAAK0J,gBAAgB,KAAKD,iBAAL,GAAyBzJ,EAAA,IAA9C;AAEA,YADM+G,IAAS/G,EAAA,QACH;AACV,gBAAM8K,IAAgB,CAAA;AACtBA,YAAI5J,SAAS6F,EAAO7F;AACpB4J,YAAIC,KAAK,IAAT;AACA,eAAKpB,wBAAwB5C,GAAQ+D,CAArC;QAJU;MAHkD,GAehEvF,SAASA,WAAA;AACP,eAAO,KAAK1C;MADL,GAQT2C,cAAcA,WAAA;AACZ,eAAO,KAAKrC;MADA,GASdjJ,UAAUA,SAA2B8Q,GAAoB;AACvD,YAAK,KAAK3R,aAA4B4R,CAAf,KAAK5R,UAAU4R,YAIjCD,EAAME,WAIX,KACEF,EAAMvS,SAAgB0S,EAAAA,qDACrBH,EAAsBF,IAAK9J,SAAS,KAAK5G,EAAzC,GACD;AAIA,cAAMN,IAAO,KAAK2P,iBAAL,GACT2B,IAAiBrB,EAAAA,gDAAcjQ,GAAM,KAAKT,SAApC;AAER+R,WAAAA,KACCA,EAAI3S,SAAS,KAAK4S,YACjBC,KAAKC,UAAUH,EAAI7F,QAAJ,CAAf,MAAkC+F,KAAKC,UAAU,KAAK1I,UAApB,MAGpCuI,IAAM;AAER,cAAKA,EAmCOA,GAAII,UAAJ,MACV,KAAKC,kBACH,MACAlC,iFAFF,GAIA,KAAKmC,eACH9S,EAAAA,+BAAA,qCAA4CY;YAAQ;YAAMM;UAA1D,CADF;eAxCQ;AACD6R,cAAAA,6CAASX,EAAMY,KAAtB;AAYMC,gBAAehP,EAAAA,+CAAc,KAAvB;AACNhC,gBAAiBgC,EAAAA,+CAAc,OAAvB;AACdhC,cAAMmC,aAAa,QAAQ,KAAKqO,QAAhC;AACMS,gBAAK,KAAKC,uBAAL;AAEX,gBAAMC,IAAIF,EAAGE,IAAwB,IAApBC,EAAAA,qCAAOC;AACxBrR,cAAMmC,aAAa,KAAK,GAFd8O,EAAGK,IAAIF,EAAAA,qCAAOC,cAAc,KAAK9M,MAAM,KAAK,EAE9B,EAAxB;AACAvE,cAAMmC,aAAa,KAAK,GAAGgP,CAAH,EAAxB;AACMI,gBAAW,KAAKzP,cAAL;AACjB9B,cAAM4I,YAAY2I,CAAlB;AACMxN,gBAAiB/B,EAAAA,+CAAc,OAAvB;cACRG,aAAa,QAAQ,MAA3B;AACMqP,gBAAW,KAAK5C,iBAAL;AACjB,kBAAMtD,IAAqBiB,EAAAA,gDAAciF,GAAU,IAAnC;AACZA,kBAAalG,KACf,KAAKuD,gBAAgB2C,GAAUlG,CAA/B;AAEFvH,cAAM6E,YAAqBmB,EAAAA,gDAAeyH,CAAxB,CAAlB;AACAxR,cAAM4I,YAAY7E,CAAlB;AACAiN,cAAIpI,YAAY5I,CAAhB;AACIyR,cAAAA,0CAAeT,GAAK,KAAKxS,SAA7B;AACOsS,cAAAA,6CAAS,KAAhB;UAlCQ;QAdV,MA0DSX,GAAMvS,SAAgB8T,EAAAA,qDAIzBzS,IAAO,KAAK2P,iBAAL,GACUM,EAAAA;UAAcjQ;UAAM,KAAKT;QAApC+R,MAEHO,EAAAA,6CAASX,EAAMY,KAAtB,GACA,KAAKY,QAAQ,IAAb,GACOb,EAAAA,6CAAS,KAAhB,MAGFX,EAAMvS,SAAgBgU,EAAAA,qDACa,eAAlCzB,EAAsB0B,YAGjB5S,IAAO,KAAK2P,iBAAL,IACP2B,IAAiBrB,EAAAA,gDAAcjQ,GAAM,KAAKT,SAApC,MACD+R,EAAIhR,OAHU4Q,EAGc2B,aAE/BC,IAAkBC,EAAAA,6CAAP,MAMf9N,QAAQmF,IACN,+DADF,GAIKyH,EAAAA,6CAASX,EAAMY,KAAtB,GACMkB,IAAQ1B,EAAII,UAAJ,GACd,KAAKC,kBACH,CAACqB,GACDvD,iFAFF,GAIA,KAAKmC,eACHoB,IACI,OACAlU,EAAAA,+BAAA,qCAA4CY,QAAQ,MAAMM,CAA1D,CAHN,GAKO6R,EAAAA,6CAASiB,CAAhB;MA/GmD,GAwHzDtU,mBAAmBA,SAEjBC,GAA2D;AAE3D,YAAM,KAAKc,UAA2B0T,UAAhC,GAAN;AAMA,cAAMjT,IAAO,KAAK2P,iBAAL,GACPpQ,IAAY,KAAKA;AACvBd,YAAQY,KAAK,EACXC,SAAS,MACTG,MAAMX,EAAAA,+BAAA,0BACNc,UAAUA,WAAA;AACR,kBAAM0R,IAAiBrB,EAAAA,gDAAcjQ,GAAMT,CAA/B;AACR+R,kBACD/R,EAA2B2T,cAAc5B,EAAIhR,EAA7C,GACD6S,EAAAA,qDAAA,EAAkBC,UAAU9B,CAA5B;UAJM,EAHC,CAAb;QARA;MAF2D,EAjYjC;iDAyZ9B,0BAAoC,OAAA,OAAA,CAAA,GAC/B5B,2DAD+B,EAKlCjO,MAAMA,WAAA;AACJ,aAAKgB,iBAAiB,QAAtB,EAAgCZ,YAAY,IAAI,MAAhD;AACA,aAAKiN,qBAAqB,IAA1B;AACA,aAAKC,iBAAiB,IAAtB;AACA,aAAKpN,SAAS,kBAAd;AAEA,aAAKD,WAAW5C,EAAAA,+BAAA,+BAAhB;AACA,aAAKiK,aAAa,CAAA;AAClB,aAAKM,qBAAqB,CAAA;AAC1B,aAAKgH,oBAAoB,CAAA;AACzB,aAAKC,YAAY;MAVb,GAaNiB,UAAU,yBAlBwB,CAAA;iDAqBpC,wBAAkC,OAAA,OAAA,CAAA,GAC7B7B,2DAD6B,EAKhCjO,MAAMA,WAAA;AACJ,aAAKgB,iBAAiB,QAAtB,EAAgCZ,YAAY,IAAI,MAAhD;AACA,aAAKc,UAAU,IAAf;AACA,aAAKhB,SAAS,kBAAd;AAEA,aAAKD,WAAW5C,EAAAA,+BAAA,6BAAhB;AACA,aAAKiK,aAAa,CAAA;AAClB,aAAKM,qBAAqB,CAAA;AAC1B,aAAKgH,oBAAoB,CAAA;AACzB,aAAKC,YAAY;MATb,GAYNiB,UAAU,uBAjBsB,CAAA;UAmC5B8B,0EAAsC,uBAEtCC,0DAAsB;QAI1B7R,MAAMA,WAAA;AACJ,eAAKG,iBAAiB,WAAtB,EACGjB,SAAS,SADZ,EAEGkB,YAAY/C,EAAAA,+BAAA,kBAFf;AAGA,eAAK8C,iBAAiB,OAAtB,EAA+BC,YAC7B/C,EAAAA,+BAAA,2BADF;AAGA,eAAK4D,gBAAgB,IAArB;AACA,eAAKoM,qBAAqB,IAA1B;AACA,eAAKC,iBAAiB,IAAtB;AACA,eAAKpN,SAAS,kBAAd;eACKiB,WAAW9D,EAAAA,+BAAA,2BAAhB;AACA,eAAK4C,WAAW5C,EAAAA,+BAAA,2BAAhB;AACA,eAAKyU,kBAAkB;QAbnB;QAoBN1Q,eAAeA,WAAA;AACb,gBAAMC,IAAqBC,EAAAA,+CAAc,UAAvB;AAClBD,YAAUI,aAAa,SAASsQ,OAAOC,OAAO,KAAKF,eAAZ,CAAP,CAAhC;AACA,iBAAOzQ;QAHM;QAUfM,eAAeA,SAA+BC,GAAmB;AAE/D,eAAKkQ,kBAA4B,QADnBlQ,EAAWC,aAAa,OAAxBnB;AAET,eAAKoR,oBACR,KAAK/P,YAAY,OAAjB,GACA,KAAKf,iBAAiB,OAAtB,EAA+BZ,YAC7B/C,EAAAA,+BAAA,2BADF;QAL6D;QAsBjEsB,UAAUA,SAA+BmO,GAAgB;AACvD,cACE,EAAE,KAAKhP,UAA2BmU,cAC/B,KAAKnU,UAA2BmU,WAAhC,KACFnF,EAAE5P,SAAgBgV,EAAAA,mDAAcpF,EAAE5P,SAAgB0S,EAAAA,oDAHrD;AAOIuC,gBAAQ;AAEZ,gBAAI7S,IAAQ;AACZ,eAAG;AACD,kBAAI,KAAK8S,eAAe3M,SAASnG,EAAMpC,IAAnC,GAA0C;AAC5CiV,oBAAQ;AACR;cAF4C;AAI9C7S,kBAAQA,EAAM+S,kBAAN;YALP,SAMM/S;AACL6S,iBAEiB,6BAAf7S,EAAMpC,QAAqC,KAAK4U,mBAClD,KAAK/P,YAAY,OAAjB,GACA,KAAKf,iBAAiB,OAAtB,EAA+BZ,YAC7B/C,EAAAA,+BAAA,2BADF,GAGA,KAAKyU,kBAAkB,SAER,2BAAfxS,EAAMpC,QACL,KAAK4U,oBAEN,KAAK/P,YAAY,OAAjB,GACA,KAAK5B,iBAAiB,OAAtB,EAA+BC,YAC7B/C,EAAAA,+BAAA,2BADF,GAGA,KAAKyU,kBAAkB,OAEzB,KAAK3B,eAAe,IAApB,KAEA,KAAKA,eAAe9S,EAAAA,+BAAA,2BAApB;AAGF,gBAAI,CAAC,KAAKJ,WACR,KAAI;AAGSqV,gBAAAA,kDAAc,KAAzB,GACA,KAAKpC,kBAAkB,CAACiC,GAAOP,uEAA/B;YAJE,UAAJ;AAMaU,gBAAAA,kDAAc,IAAzB;YADQ;UA9CZ;QADuD;QAyDzDF,gBAAgB,CAAC,0BAA0B,sBAA3B;MAjHU;AAmH5BxV,iDAAA,sBAAgCiV;AAGhChS,QAAAA,2CAAajD,0CAAb;AAn1CA,UAAAkD,qCAAA,EA+CalD,QAAAA,2CA/Cb;ACwBO,UAAMA,uCAASC,EAAAA,kEAAoC;QAExD,EACE,MAAQ,eACR,UAAY,MACZ,OAAS,CACP,EACE,MAAQ,gBACR,MAAQ,OACR,OAAS,EAHX,CADO,GAOT,QAAU,UACV,SAAW,8BACX,OAAS,eACT,SAAW,8BACX,YAAc,CAAC,4BAAD,EAdhB;QAkBA,EACE,MAAQ,mBACR,UAAY,YACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,KACR,OAAS,SAHX,GAKA,EACE,MAAQ,kBACR,MAAQ,MACR,SAAW,CACT;UAAC;UAA+B;QAAhC,GACA,CAAC,kCAAkC,OAAnC,GACA,CAAC,qCAAqC,UAAtC,GACA,CAAC,+BAA+B,QAAhC,GACA,CAAC,4BAA4B,OAA7B,CALS,EAHb,GAWA,EACE,MAAQ,eACR,MAAQ,KACR,OAAS,SAHX,CAjBO,GAuBT,cAAgB,MAChB,QAAU,UACV,OAAS,eACT,SAAW,kCACX,YAAc,CAAC,iBAAD,EA9BhB;QAkCA,EACE,MAAQ,eACR,UAAY,SACZ,OAAS,CACP,EACE,MAAQ,kBACR,MAAQ,MACR,SAAW,CACT,CAAC,8BAA8B,MAA/B,GACA;UAAC;UAAkC;QAAnC,GACA,CAAC,KAAK,KAAN,GACA,CAAC,MAAM,IAAP,GACA,CAAC,SAAS,OAAV,GACA,CAAC,MAAM,KAAP,GACA,CAAC,OAAO,OAAR,CAPS,EAHb,GAaA,EACE,MAAQ,eACR,MAAQ,OACR,OAAS,SAHX,CAdO,GAoBT,QAAU,UACV,OAAS,eACT,SAAW,8BACX,YAAc,CAAC,iBAAD,EA1BhB;QA8BA,EACE,MAAQ,aACR,UAAY,SACZ,OAAS,CACP,EACE,MAAQ,kBACR,MAAQ,MACR,SAAW,CACT,CAAC,wBAAwB,KAAzB,GACA,CAAC,wBAAwB,KAAzB,GACA,CAAC,wBAAwB,KAAzB,GACA,CAAC,yBAAyB,MAA1B,GACA,CAAC,yBAAyB,MAA1B,GACA;UAAC;UAAyB;QAA1B,CANS,EAHb,GAYA,EACE,MAAQ,eACR,MAAQ,OACR,OAAS,SAHX,CAbO,GAmBT,QAAU,UACV,OAAS,eACT,SAAW,4BACX,YAAc,CAAC,iBAAD,EAzBhB;QA6BA,EACE,MAAQ,iBACR,UAAY,MACZ,OAAS,CACP,EACE,MAAQ,kBACR,MAAQ,YACR,SAAW,CACT,CAAC,KAAU,IAAX,GACA,CAAC,KAAK,GAAN,GACA,CAAC,KAAU,cAAX,GACA,CAAC,WAAW,OAAZ,GACA,CAAC,WAAgB,SAAjB,GACA,CAAC,KAAU,UAAX,CANS,EAHb,CADO,GAcT,QAAU,UACV,OAAS,eACT,SAAW,gCACX,SAAW,+BApBb;QAyBA;UACE,MAAQ;UACR,UAAY;UACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,mBACR,OAAS,SAHX,GAKA,EACE,MAAQ,kBACR,MAAQ,YACR,SAAW,CACT,CAAC,uBAAuB,MAAxB,GACA,CAAC,sBAAsB,KAAvB,GACA,CAAC,wBAAwB,OAAzB,GACA,CAAC,wBAAwB,OAAzB,GACA,CAAC,2BAA2B,UAA5B,GACA,CAAC,2BAA2B,UAA5B,GACA,CAAC,+BAA+B,cAAhC,CAPS,EAHb,CANO;UAoBT,cAAgB;UAChB,QAAU;UACV,OAAS;UACT,SAAW;UACX,SAAW;QA3Bb;QA+BA,EACE,MAAQ,eACR,UAAY,4BACZ,OAAS,CACP,EACE,MAAQ,kBACR,MAAQ,OACR,UAAY,gCAHd,GAKA,EACE,MAAQ,eACR,MAAQ,SACR,OAAS,SAHX,CANO,GAYT,mBAAqB,MACrB,eAAiB,MACjB,OAAS,mBACT,SAAW,8BACX,YAAc,CAAC,qBAAD,EAnBhB;QAuBA,EACE,MAAQ,cACR,UAAY,SACZ,OAAS,CACP,EACE,MAAQ,kBACR,MAAQ,MACR,SAAW;UACT,CAAC,oCAAoC,OAArC;UACA,CAAC,sCAAsC,SAAvC;UACA,CAAC,wCAAwC,WAAzC;QAHS,EAHb,GASA,EACE,MAAQ,eACR,MAAQ,OACR,OAAS,SAHX,CAVO,GAgBT,QAAU,UACV,OAAS,eACT,SAAW,6BACX,SAAW,4BAtBb;QA2BA,EACE,MAAQ,gBACR,UAAY,SACZ,OAAS,CACP,EACE,MAAQ,kBACR,MAAQ,MACR,SAAW,CACT,CAAC,mCAAmC,KAApC,GACA,CAAC,mCAAmC,KAApC,GACA,CAAC,mCAAmC,KAApC,GACA,CAAC,uCAAuC,SAAxC,GACA;UAAC;UAAsC;QAAvC,GACA,CAAC,oCAAoC,MAArC,GACA,CAAC,uCAAuC,SAAxC,GACA,CAAC,sCAAsC,QAAvC,CARS,EAHb,GAcA,EACE,MAAQ,eACR,MAAQ,QACR,OAAS,QAHX,CAfO,GAqBT,QAAU,UACV,OAAS,eACT,SAAW,8BACX,SAAW,8BACX,YAAc,CAAC,iBAAD,EA5BhB;QAgCA;UACE,MAAQ;UACR,UAAY;UACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,YACR,OAAS,SAHX,GAKA,EACE,MAAQ,eACR,MAAQ,WACR,OAAS,SAHX,CANO;UAYT,cAAgB;UAChB,QAAU;UACV,OAAS;UACT,SAAW;UACX,SAAW;QAnBb;QAuBA,EACE,MAAQ,kBACR,UAAY,+BACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,SACR,OAAS,SAHX,GAKA,EACE,MAAQ,eACR,MAAQ,OACR,OAAS,SAHX,GAKA,EACE,MAAQ,eACR,MAAQ,QACR,OAAS,SAHX,CAXO,GAiBT,cAAgB,MAChB,QAAU,UACV,OAAS,eACT,SAAW,iCACX,SAAW,gCAxBb;QA4BA;UACE,MAAQ;UACR,UAAY;UACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,QACR,OAAS,SAHX,GAKA,EACE,MAAQ,eACR,MAAQ,MACR,OAAS,SAHX,CANO;UAYT,cAAgB;UAChB,QAAU;UACV,OAAS;UACT,SAAW;UACX,SAAW;QAnBb;QAuBA,EACE,MAAQ,qBACR,UAAY,yCACZ,QAAU,UACV,OAAS,eACT,SAAW,oCACX,SAAW,mCANb;QAUA,EACE,MAAQ,cACR,UAAY,2BACZ,OAAS,CACP;UACE,MAAQ;UACR,MAAQ;UACR,OAAS;QAHX,GAKA,EACE,MAAQ,eACR,MAAQ,KACR,OAAS,SAHX,CANO,GAYT,cAAgB,MAChB,QAAU,UACV,OAAS,eACT,SAAW,6BACX,SAAW,4BAnBb;MA/UwD,CAApC,GA8WhB0V,+CAAiB;QAErB,KAAO;QACP,OAAS;QACT,UAAY;QACZ,QAAU;QACV,OAAS;QAGT,MAAQ;QACR,KAAO;QACP,KAAO;QACP,IAAM;QACN,OAAS;QACT,KAAO;QACP,OAAS;QAGT,KAAO;QACP,KAAO;QACP,KAAO;QACP,MAAQ;QACR,MAAQ;QACR,MAAQ;QAGR,KAAO;QACP,KAAO;QACP,KAAO;QACP,SAAW;QACX,QAAU;QACV,MAAQ;QACR,SAAW;QACX,QAAU;MAjCW;AAoCZnM,QAAAA,2CACT,mBACWoM,EAAAA,0DAAwB,MAAMD,4CAAzC,CAFF;UAkBME,6DAA+B,EAOnCrR,eAAeA,WAAA;AACb,cAAMC,IAAqBC,EAAAA,+CAAc,UAAvB,GACZoR,IAAkD,mBAAnC,KAAK5T,cAAc,UAAnB;AACrBuC,UAAUI,aAAa,iBAAiBsQ,OAAOW,CAAP,CAAxC;AACA,eAAOrR;MAJM,GAYfM,eAAeA,SAAkCC,GAAmB;AAC5D8Q,YAA4D,WAA7C9Q,EAAWC,aAAa,eAAxB;AACrB,aAAKyC,aAAaoO,CAAlB;MAFkE,GAepEpO,cAAcA,SAAkCoO,GAAqB;AAEnE,cAAMC,IAAc,KAAKvT,SAAS,SAAd;AAChBsT,YACGC,KACH,KAAKxS,iBAAiB,SAAtB,EAAiCjB,SAAS,QAA1C,IAEOyT,KACT,KAAK5Q,YAAY,SAAjB;MARiE,EAlClC,GAoD/B6Q,+DAAiCA,WAAA;AACrC,aAAKrV,SAAS,UAAd,EAA2BkD,aAEzB,SAA+BoS,GAAc;AACrCH,cAA0B,mBAAXG;AACpB,eAAK/R,eAAL,EAA2CwD,aAAaoO,CAAxD;QAF0C,CAF/C;MADqC;AAW5B5L,QAAAA,kDACT,+BACA2L,4DACAG,4DAHF;AAOWxM,QAAAA,2CACT,uBACWC,EAAAA,4DAA0B,8BAA8B,KAAnE,CAFF;UAcMyM,yDAA2B,EAM/B1Q,aAAaA,SAAgCC,GAAa;AAC1C,mBAAVA,IACF,KAAKpD,iBAAkBC,SAAS,OAAhC,IAEA,KAAKD,iBAAkBC,SAAS,QAAhC;MAJsD,GAa1DkC,eAAeA,WAAA;AACb,cAAMC,IAAqBC,EAAAA,+CAAc,UAAvB;AAClBD,UAAUI,aAAa,MAAM,KAAK3C,cAAc,IAAnB,CAA7B;AACA,eAAOuC;MAHM,GAWfM,eAAeA,SAAgCC,GAAmB;AAC1DmR,YAAKnR,EAAWC,aAAa,IAAxB;AACX,YAAW,SAAPkR,EAAa,OAAM,IAAIC,UAAU,gCAAd;AACvB,aAAK5Q,YAAY2Q,CAAjB;MAHgE,EA9BnC,GA8C3BE,6DAA+BA,WAAA;AACnC,aAAK1V,SAAS,IAAd,EAAqBkD,cACnB,SAAgC4B,GAAa;AAC3C,eAAKD,YAAYC,CAAjB;QAD2C,GAG3C6Q,KAAK,IAHP,CADF;MADmC;AAS1BpM,QAAAA,kDACT,8BACAgM,wDACAG,0DAHF;AAOApT,QAAAA,2CAAajD,oCAAb;AA9kBA,UAAAkD,+BAAA,EAwBalD,QAAAA,qCAxBb;ACkCO,UAAMA,wCAASC,EAAAA,kEAAoC,CAExD,EACE,MAAQ,uBACR,UAAY,gCACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,SACR,OAAS,SAHX,CADO,GAOT,UAAY,sCACZ,OAAS,CACP,EACE,MAAQ,mBACR,MAAQ,KAFV,CADO,GAMT,mBAAqB,MACrB,eAAiB,MACjB,OAAS,eACT,SAAW,kCACX,SAAW,iCArBb,GAyBA;QACE,MAAQ;QACR,UAAY;QACZ,OAAS,CACP,EACE,MAAQ,gBACR,MAAQ,SACR,OAAS,IACT,KAAO,GACP,WAAa,EALf,CADO;QAST,UAAY;QACZ,OAAS,CACP,EACE,MAAQ,mBACR,MAAQ,KAFV,CADO;QAMT,mBAAqB;QACrB,eAAiB;QACjB,OAAS;QACT,SAAW;QACX,SAAW;MAvBb,GA0BA,EACE,MAAQ,uBACR,UAAY,SACZ,OAAS;QACP,EACE,MAAQ,kBACR,MAAQ,QACR,SAAW,CACT,CAAC,6CAA6C,OAA9C,GACA,CAAC,6CAA6C,OAA9C,CAFS,EAHb;QAQA,EACE,MAAQ,eACR,MAAQ,QACR,OAAS,UAHX;MATO,GAeT,UAAY,sCACZ,OAAS,CACP,EACE,MAAQ,mBACR,MAAQ,KAFV,CADO,GAMT,mBAAqB,MACrB,eAAiB,MACjB,OAAS,eACT,SAAW,sCACX,YAAc,CAAC,6BAAD,EA7BhB,GAgCA,EACE,MAAQ,gBACR,UAAY,6BACZ,OAAS,CACP,EACE,MAAQ,kBACR,MAAQ,OACR,UAAY,KAHd,GAKA,EACE,MAAQ,eACR,MAAQ,QACR,OAAS,UACT,OAAS,QAJX,GAMA;QACE,MAAQ;QACR,MAAQ;QACR,OAAS;QACT,OAAS;MAJX,GAMA,EACE,MAAQ,eACR,MAAQ,MACR,OAAS,UACT,OAAS,QAJX,CAlBO,GAyBT,UAAY,sCACZ,OAAS,CACP,EACE,MAAQ,mBACR,MAAQ,KAFV,CADO,GAMT,cAAgB,MAChB,mBAAqB,MACrB,eAAiB,MACjB,OAAS,eACT,SAAW,+BACX,YAAc,CAAC,mCAAmC,sBAApC,EAxChB,GA2CA,EACE,MAAQ,oBACR,UAAY,iCACZ,OAAS,CACP,EACE,MAAQ,kBACR,MAAQ,OACR,UAAY,KAHd,GAKA;QACE,MAAQ;QACR,MAAQ;QACR,OAAS;MAHX,CANO,GAYT,UAAY,sCACZ,OAAS,CACP,EACE,MAAQ,mBACR,MAAQ,KAFV,CADO,GAMT,mBAAqB,MACrB,eAAiB,MACjB,OAAS,eACT,SAAW,mCACX,YAAc,CACZ,mCACA,0BAFY,EA1BhB,GAgCA;QACE,MAAQ;QACR,UAAY;QACZ,OAAS,CACP,EACE,MAAQ,kBACR,MAAQ,QACR,SAAW,CACT,CAAC,kDAAkD,OAAnD,GACA,CAAC,qDAAqD,UAAtD,CAFS,EAHb,CADO;QAUT,mBAAqB;QACrB,OAAS;QACT,SAAW;QACX,sBAAwB;QACxB,YAAc,CAAC,yBAAyB,6BAA1B;MAjBhB,CAhKwD,CAApC,GA0LhBsW,sDAAuB,EAC3B,OAAS,4CACT,OAAS,2CAFkB;AAKlB/M,QAAAA,2CACT,+BACWoM,EAAAA,0DAAwB,QAAQW,mDAA3C,CAFF;UAUMC,yDAA0B,EAC9B,OAAS,iDACT,UAAY,mDAFkB;AAKrBhN,QAAAA,2CACT,yBACWoM,EAAAA,0DAAwB,QAAQY,sDAA3C,CAFF;UAeMC,gFAAiD,EAOrDtW,mBAAmBA,SAEjBC,GAA2D;AAE3D,YAASC,CAAL,KAAKA,YAAT;AAGA,cAAMK,IAAW,KAAKC,SAAS,KAAd,GAEXiL,IADWlL,EAAS0B,YAATO,EACQwI,QAAT;AACX,eAAK0D,YAAL,KAAkC,SAAZjD,MACnBmD,IAAmB,EACvBzO,MAAM,iBACNO,QAAQ,EAACC,KAAKJ,EAASK,UAAU,IAAnB,EAAN,EAFe,GAKzBX,EAAQY,KAAK,EACXC,SAAS,MACTG,MAAMX,EAAAA,+BAAA,yBAAgCY,QAAQ,MAAMuK,CAA9C,GACNrK,UAAsBC,EAAAA,mDAAgB,MAAMuN,CAAlC,EAHC,CAAb;QAZF;MAF2D,EATR;QAgC5C/L,gDACT,mCACAyT,6EAFF;AAKWjN,QAAAA,2CACT,wBACWC,EAAAA,4DAA0B,+BAA+B,KAApE,CAFF;AAKWD,QAAAA,2CACT,4BACWC,EAAAA,4DACT,mCACA,KAFF,CAFF;UAuBaiN,2CAAyB,oBAAIC,IAAI,CAC5C,mBACA,uBACA,oBACA,gBACA,qBAL4C,CAAR,GAqBhCC,0EAA2C,4BAK3CC,kEAAmC,EAMvCC,iBAAiBA,WAAA;AAEf,YAAIpU,IAAsB;AAC1B,WAAG;AACD,cAAIgU,yCAAUK,IAAIrU,EAAMpC,IAApB,EACF,QAAOoC;AAETA,cAAQA,EAAM+S,kBAAN;QAJP,SAKM/S;AACT,eAAO;MATQ,GAgBjBX,UAAUA,SAAwCmO,GAAgB;AAChE,cAAM8G,IAAK,KAAK9V;YAKb8V,EAAG3B,cACJ,CAAA2B,EAAG3B,WAAH,MACCnF,EAAE5P,SAAgBgV,EAAAA,mDAAcpF,EAAE5P,SAAgB0S,EAAAA,uDAI/C/R,IAAU,CAAC,CAAC,KAAK6V,gBAAL,GAClB,KAAKvD,eACHtS,IAAU,OAAOR,EAAAA,+BAAA,gCADnB,GAII,CAAC,KAAKJ,YACR,KAAI;AAGSqV,YAAAA,kDAAc,KAAzB,GACA,KAAKpC,kBACH,CAACrS,GACD2V,uEAFF;QAJE,UAAJ;AASalB,YAAAA,kDAAc,IAAzB;QADQ;MA1BoD,EAtB3B;QAuD9B1S,gDACT,+BACA6T,+DAFF;AAMA5T,QAAAA,2CAAajD,qCAAb;AAvZA,UAAAkD,gCAAA,EAkCalD,QAAAA,uCA8RA0W,WAAAA,yCAhUb;AC4BO,UAAM1W,wCAASC,EAAAA,kEAAoC;QAExD,EACE,MAAQ,iBACR,UAAY,MACZ,OAAS,CACP,EACE,MAAQ,kBACR,MAAQ,QACR,SAAW,CACT,CAAC,6BAA6B,MAA9B,GACA,CAAC,8BAA8B,OAA/B,CAFS,EAHb,CADO,GAUT,QAAU,WACV,OAAS,gBACT,SAAW,gCACX,SAAW,+BAhBb;QAmBA;UACE,MAAQ;UACR,UAAY;UACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,OACR,OAAS,UAHX,CADO;UAOT,UAAY;UACZ,OAAS,CACP,EACE,MAAQ,mBACR,MAAQ,MAFV,CADO;UAMT,mBAAqB;UACrB,eAAiB;UACjB,OAAS;UACT,SAAW;UACX,sBAAwB;UACxB,SAAW;UACX,YAAc,CAAC,qBAAD;QAvBhB;QA0BA;UACE,MAAQ;UACR,UAAY;UACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,OACR,OAAS,UAHX,CADO;UAOT,UAAY;UACZ,OAAS,CACP,EACE,MAAQ,mBACR,MAAQ,MAFV,CADO;UAMT,UAAY;UACZ,OAAS,CACP,EACE,MAAQ,mBACR,MAAQ,OAFV,CADO;UAMT,mBAAqB;UACrB,eAAiB;UACjB,OAAS;UACT,SAAW;UACX,SAAW;UACX,sBAAwB;UACxB,YAAc,CAAC,qBAAD;QA9BhB;QAiCA;UACE,MAAQ;UACR,UAAY;UACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,IAFV,GAIA,EACE,MAAQ,kBACR,MAAQ,MACR,SAAW,CACT,CAAC,KAAK,IAAN,GACA,CAAC,KAAU,KAAX,GACA,CAAC,MAAW,IAAZ,GACA,CAAC,MAAgB,KAAjB,GACA,CAAC,MAAW,IAAZ,GACA,CAAC,MAAgB,KAAjB,CANS,EAHb,GAYA,EACE,MAAQ,eACR,MAAQ,IAFV,CAjBO;UAsBT,cAAgB;UAChB,QAAU;UACV,OAAS;UACT,SAAW;UACX,YAAc,CAAC,iBAAiB,kBAAlB;QA7BhB;QAgCA,EACE,MAAQ,mBACR,UAAY,YACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,KACR,OAAS,UAHX,GAKA,EACE,MAAQ,kBACR,MAAQ,MACR,SAAW,CACT,CAAC,8BAA8B,KAA/B,GACA,CAAC,6BAA6B,IAA9B,CAFS,EAHb,GAQA,EACE,MAAQ,eACR,MAAQ,KACR,OAAS,UAHX,CAdO,GAoBT,cAAgB,MAChB,QAAU,WACV,OAAS,gBACT,SAAW,kCACX,YAAc,CAAC,kBAAD,EA3BhB;QA8BA,EACE,MAAQ,gBACR,UAAY,6BACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,QACR,OAAS,UAHX,CADO,GAOT,QAAU,WACV,OAAS,gBACT,SAAW,+BACX,SAAW,8BAbb;QAgBA,EACE,MAAQ,cACR,UAAY,qBACZ,QAAU,MACV,OAAS,gBACT,SAAW,6BACX,SAAW,4BANb;QASA;UACE,MAAQ;UACR,UAAY;UACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,MACR,OAAS,UAHX,CADO;UAOT,UAAY;UACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,OAFV,CADO;UAMT,UAAY;UACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,OAFV,CADO;UAMT,QAAU;UACV,OAAS;UACT,SAAW;UACX,SAAW;UACX,YAAc,CAAC,eAAD;QA5BhB;QA+BA,EACE,MAAQ,kBACR,UAAY,kCACZ,eAAiB,MACjB,mBAAqB,OACrB,OAAS,gBACT,SAAW,gCANb;QASA;UACE,MAAQ;UACR,UAAY;UACZ,mBAAqB;UACrB,eAAiB;UACjB,mBAAqB;UACrB,OAAS;UACT,SAAW;QAPb;QAUA,EACE,MAAQ,oBACR,UAAY,sCACZ,mBAAqB,MACrB,mBAAqB,OACrB,OAAS,gBACT,SAAW,kCANb;MAzNwD,CAApC,GAyOhB0V,gDAAiB;QAErB,IAAM;QACN,KAAO;QACP,IAAM;QACN,KAAO;QACP,IAAM;QACN,KAAO;QAGP,KAAO;QACP,IAAM;MAXe;AAcZnM,QAAAA,2CACT,oBACWoM,EAAAA,0DAAwB,MAAMD,6CAAzC,CAFF;UA6BMsB,2DAA4B;QAChCC,cAAc;QACdC,YAAY;QAQZ3S,eAAeA,WAAA;AACb,cAAI,CAAC,KAAK0S,gBAAgB,CAAC,KAAKC,WAC9B,QAAO;AAET,gBAAM1S,IAAqBC,EAAAA,+CAAc,UAAvB;AACd,eAAKwS,gBACPzS,EAAUI,aAAa,UAAUsQ,OAAO,KAAK+B,YAAZ,CAAjC;AAEE,eAAKC,cACP1S,EAAUI,aAAa,QAAQ,GAA/B;AAEF,iBAAOJ;QAXM;QAmBfM,eAAeA,SAAyBC,GAAmB;AACzD,eAAKkS,eAAezP,SAASzC,EAAWC,aAAa,QAAxB,GAAoC,EAA7C,KAAoD;AACxE,eAAKkS,aAAa1P,SAASzC,EAAWC,aAAa,MAAxB,GAAkC,EAA3C,KAAkD;AACpE,eAAKmS,cAAL;QAHyD;QAU3DzP,gBAAgBA,WAAA;AACd,cAAI,CAAC,KAAKuP,gBAAgB,CAAC,KAAKC,WAC9B,QAAO;AAET,gBAAMtP,IAAQoE,uBAAOC,OAAO,IAAd;AACV,eAAKgL,iBACPrP,EAAA,cAAuB,KAAKqP;AAE1B,eAAKC,eACPtP,EAAA,UAAmB;AAErB,iBAAOA;QAXO;QAoBhBD,gBAAgBA,SAAyBC,GAAmB;AAC1D,eAAKqP,eAAerP,EAAA,eAAwB;AAC5C,eAAKsP,aAAatP,EAAA,UAAmB,IAAI;AACzC,eAAKH,aAAL;QAH0D;QAW5DI,WAAWA,SAAyB5G,GAAoB;AACtD,gBAAM6G,IAAiB7G,EAAU8G,SAAS,gBAAnB;AACtBD,YAA4BE,QAA5B;AACD,cAAIxF,IAAasF,EAAeM;AAChC,mBAAShC,IAAI,GAAGA,KAAK,KAAK6Q,cAAc7Q,KAAK;AAC3C,kBAAMgR,IAAcnW,EAAU8G,SAAS,oBAAnB;AACnBqP,cAAyBpP,QAAzB;AACDxF,cAAW0F,QAAQkP,EAAYjP,kBAA/B;gBACaiP,EAAYhP;UAJkB;AAMzC,eAAK8O,eACDG,IAAYpW,EAAU8G,SAAS,kBAAnB,GACjBsP,EAAuBrP,QAAvB,GACDxF,EAAW0F,QAAQmP,EAAUlP,kBAA7B;AAEF,iBAAOL;QAf+C;QAsBxDO,SAASA,SAAyBP,GAA8B;AAC1DwP,cACFxP,EAAeM,eAAgByE,YAA/B;AAGF,eAAKqK,aADL,KAAKD,eAAe;AAKpB,gBAAMM,IAA6C,CAAC,IAAD,GAC7CC,IAAiD,CAAC,IAAD;AACvD,cAAIC,IAA6C;AACjD,iBAAOH,KAAa;AAClB,gBAAI,CAAAA,EAAY9O,kBAAZ,EAIJ,SAAQ8O,EAAYjX,MAApB;cACE,KAAK;AACH,qBAAK4W;AAELM,kBAAiBxW,KACfuW,EAAY7O,gBADd;AAGA+O,kBAAqBzW,KACnBuW,EAAYxK,oBADd;AAGA;cACF,KAAK;AACH,qBAAKoK;AACLO,oBACEH,EAAYxK;AACd;cACF;AACE,sBAAMqJ,UAAU,yBAAyBmB,EAAYjX,IAA/C;YAjBV;AAHEiX,gBAAcA,EAAY5O,aAAZ;UAFE;AA0BpB,eAAKjB,aAAL;AAEA,eAAKiQ,sBACHH,GACAC,GACAC,CAHF;QAxC8D;QAmDhEzO,iBAAiBA,SAAyBlB,GAA8B;AAClEwP,cACFxP,EAAgBM,eAAgByE,YAAhC;AACF,cAAIzG,IAAI;AACR,iBAAOkR,KAAa;AAClB,gBAAI,CAAAA,EAAY9O,kBAAZ,EAIJ,SAAQ8O,EAAYjX,MAApB;cACE,KAAK;AACH,oBAAMsX,IAAU,KAAKpV,SAAS,OAAO6D,CAArB;AAChB,sBAAMwR,IAAU,KAAKrV,SAAS,OAAO6D,CAArB;AAChBkR,kBAAY7O,mBACVkP,KAAWA,EAAQnV,WAAYmG;AACjC2O,kBAAYxK,uBACV8K,KAAWA,EAAQpV,WAAYmG;AACjCvC;AACA;cAEF,KAAK;AACGwR,oBAAU,KAAKrV,SAAS,MAAd;AAChB+U,kBAAYxK,uBACV8K,KAAWA,EAAQpV,WAAYmG;;cAGnC;AACE,sBAAMwN,UAAU,yBAAyBmB,EAAYjX,IAA/C;YAlBV;AAHEiX,gBAAcA,EAAY5O,aAAZ;UAFE;QAJkD;QAmCxEyO,eAAeA,WAAA;AACb,gBAAMI,IAA6C,CAAC,IAAD,GAC7CC,IAAiD,CAAC,IAAD;AACvD,cAAIC,IAA6C;AAE7C,eAAKlV,SAAS,MAAd,MACFkV,IACE,KAAKlV,SAAS,MAAd,EAAuBC,WAAYmG;AAEvC,mBAASvC,IAAI,GAAG,KAAK7D,SAAS,OAAO6D,CAArB,GAAyBA,KAAK;AAC5C,kBAAMuR,IAAU,KAAKpV,SAAS,OAAO6D,CAArB,GACVwR,IAAU,KAAKrV,SAAS,OAAO6D,CAArB;AAChBmR,cAAiBxW,KAAK4W,EAASnV,WAAYmG,gBAA3C;AACA6O,cAAqBzW,KAAK6W,EAASpV,WAAYmG,gBAA/C;UAJ4C;AAM9C,eAAKlB,aAAL;AACA,eAAKiQ,sBACHH,GACAC,GACAC,CAHF;QAhBa;QA2BfhQ,cAAcA,WAAA;AAER,eAAKlF,SAAS,MAAd,KACF,KAAK2C,YAAY,MAAjB;mBAEOkB,IAAI,GAAG,KAAK7D,SAAS,OAAO6D,CAArB,GAAyBA,IACvC,MAAKlB,YAAY,OAAOkB,CAAxB,GACA,KAAKlB,YAAY,OAAOkB,CAAxB;AAGF,eAASA,IAAI,GAAGA,KAAK,KAAK6Q,cAAc7Q,IACtC,MAAK9C,iBAAiB,OAAO8C,CAA7B,EACG/D,SAAS,SADZ,EAEGkB,YAAY/C,EAAAA,+BAAA,sBAFf,GAGA,KAAK8J,qBAAqB,OAAOlE,CAAjC,EAAoC7C,YAClC/C,EAAAA,+BAAA,oBADF;AAIE,eAAK0W,cACP,KAAK5M,qBAAqB,MAA1B,EAAkC/G,YAChC/C,EAAAA,+BAAA,oBADF;QAnBU;QAiCdkX,uBAAuBA,SAErBH,GACAC,GACAC,GAA0C;AAE1C,mBAASrR,IAAI,GAAGA,KAAK,KAAK6Q,cAAc7Q,KAAK;AAC3C,gBAAA;AAAA,qBAAA,IAAAmR,EAAiBnR,CAAjB,MAAA,EAAqB2C,UAAU,MAAM,OAAO3C,CAA5C;AACA,gBAAA;AAAA,qBAAA,IAAAoR,EAAqBpR,CAArB,MAAA,EAAyB2C,UAAU,MAAM,OAAO3C,CAAhD;UAF2C;AAI7C,kBAAAqR,KAAAA,EAAyB1O,UAAU,MAAM,MAAzC;QAN0C;MAlPZ;AA4PvBkB,QAAAA,kDACT,uBACA+M,0DACA,MACA,CAAC,sBAAsB,kBAAvB,CAJF;UAWMa,+DAAgCA,WAAA;AACpC,aAAKvT,YACH,WAAA;AACE,cAAK,KAAK2S,gBAAiB,KAAKC,YAEzB;AAAA,gBAAI,CAAC,KAAKD,gBAAgB,KAAKC,WACpC,QAAO1W,EAAAA,+BAAA;AACF,gBAAI,KAAKyW,gBAAgB,CAAC,KAAKC,WACpC,QAAO1W,EAAAA,+BAAA;AACF,gBAAI,KAAKyW,gBAAgB,KAAKC,WACnC,QAAO1W,EAAAA,+BAAA;UALF,MADL,QAAOA,EAAAA,+BAAA;iBAQF;QAVT,GAWE6V,KAAK,IAXP,CADF;MADoC;AAiB3B9M,QAAAA,2CAAS,uBAAuBsO,4DAA3C;UAaMC,8DAA+B,EAOnChW,UAAUA,SAA8BmO,GAAgB;AACjD,aAAK8H,gBACR,KAAKA,cAAc,CAAC,MAAM,IAAP;AAGrB,YAAMC,IAAS,KAAK1P,oBAAoB,GAAzB;AACf,cAAM2P,IAAS,KAAK3P,oBAAoB,GAAzB;AAIb0P,aACAC,KACA,CAAC,KAAKhX,UAAUiX,kBAAkBC,aAChCH,EAAO5V,kBACP6V,EAAO7V,gBAFR,MAOMmR,EAAAA,6CAAStD,EAAEuD,KAAlB,GACM4E,IAAQ,KAAKL,YAAY,CAAjB,GACVK,MAAUJ,MACZA,EAAO/K,OAAP,GACImL,CAAAA,KAAUA,EAAMC,WAAN,KAAuBD,EAAME,SAAN,KAEnC,KAAK/V,SAAS,GAAd,EAAoBC,WAAY0F,QAAQkQ,EAAMhW,gBAA9C,IAGEmW,IAAQ,KAAKR,YAAY,CAAjB,GACVQ,MAAUN,MACZA,EAAOhL,OAAP,GACIsL,CAAAA,KAAUA,EAAMF,WAAN,KAAuBE,EAAMD,SAAN,KAEnC,KAAK/V,SAAS,GAAd,EAAoBC,WAAY0F,QAAQqQ,EAAMnW,gBAA9C,IAGJ,KAAK8K,eAAL,GACOqG,EAAAA,6CAAS,KAAhB;AAEF,aAAKwE,YAAY,CAAjB,IAAsB,KAAKzP,oBAAoB,GAAzB;AACtB,aAAKyP,YAAY,CAAjB,IAAsB,KAAKzP,oBAAoB,GAAzB;MAxCgC,EAPrB,GAuD/BkQ,yDAA0BA,WAAA;AAE9B,aAAK7S,MAAMmS,2DAAX;MAF8B;AAKrBvO,QAAAA,2CAAS,iBAAiBiP,sDAArC;UAUMC,8DAA+B,EACnCC,uBAAuB,MAMvB5W,UAAUA,SAA8BmO,GAAgB;AACtD,cAAM+H,IAAS,KAAK1P,oBAAoB,MAAzB,GACT2P,IAAS,KAAK3P,oBAAoB,MAAzB,GACTqQ,IAAmB,KAAKvW,iBAAkBuG;AAGhD,aAAKqP,KAAUC,MAAWU,EACxB,UAASvS,IAAI,GAAO,IAAJA,GAAOA,KAAK;AAC1B,gBAAM3D,IAAc,MAAN2D,IAAU4R,IAASC;AAE/BxV,eACA,CAACA,EAAMxB,UAAUiX,kBAAkBC,aACjC1V,EAAML,kBACNuW,CAFD,MAOMpF,EAAAA,6CAAStD,EAAEuD,KAAlB,GACImF,MAAqB,KAAKD,yBAC5B,KAAKzL,OAAL,GACA0L,EAAiB1U,eAAjB,EAAkCiJ,eAAlC,MAEAzK,EAAMwK,OAAN,GACAxK,EAAMyK,eAAN,IAEKqG,EAAAA,6CAAS,KAAhB;QAnBwB;AAuB9B,aAAKmF,wBAAwBC;MA9ByB,EAPrB;AAyC1B5V,QAAAA,gDAAc,iBAAiB0V,2DAA1C;AAGAzV,QAAAA,2CAAajD,qCAAb;AAvsBA,UAAAkD,gCAAA,EA4BalD,QAAAA,sCA5Bb;AC4BO,UAAMA,wCAASC,EAAAA,kEAAoC;QAMxD,EACE,MAAQ,sBACR,UAAY,mCACZ,QAAU,SACV,OAAS,eACT,SAAW,qCACX,SAAW,oCANb;QASA;UACE,MAAQ;UACR,UAAY;UACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,OAFV,GAIA,EACE,MAAQ,eACR,MAAQ,OACR,OAAS,SAHX,CALO;UAWT,QAAU;UACV,OAAS;UACT,SAAW;UACX,SAAW;QAjBb;QAoBA,EACE,MAAQ,iBACR,UAAY,iCACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,QACR,OAAS,QAHX,CADO,GAOT,QAAU,SACV,cAAgB,MAChB,OAAS,eACT,SAAW,gCACX,SAAW,+BAdb;QAiBA,EACE,MAAQ,iBACR,UAAY,8BACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,SACR,OAAS,CAAC,UAAU,OAAX,EAHX,CADO,GAOT,QAAU,WACV,OAAS,eACT,SAAW,gCACX,SAAW,+BAbb;QAgBA,EACE,MAAQ,gBACR,UAAY,6BACZ,OAAS,CACP,EACE,MAAQ,eACR,MAAQ,SACR,OAAS,CAAC,UAAU,OAAX,EAHX,CADO,GAOT,QAAU,UACV,OAAS,eACT,SAAW,+BACX,SAAW,8BAbb;MApEwD,CAApC,GAgGhB4Y,mDAAoB,EAIxBzV,MAAMA,WAAA;AACJ,aAAKC,WAAW5C,EAAAA,+BAAA,yBAAhB;AACA,aAAK6C,SAAS,aAAd;AACA,aAAKkE,aAAa;AAClB,aAAKE,aAAL;AACA,aAAKpD,UAAU,MAAM,OAArB;AACA,aAAKgF,WACH,IAAIC,EAAAA;UAAY,CAAC,wBAAD;UAA4B;QAA5C,CADF;AAGA,aAAKhF,WAAW9D,EAAAA,+BAAA,yBAAhB;MATI,GAeN+D,eAAeA,WAAA;AACb,cAAMC,IAAqBC,EAAAA,+CAAc,UAAvB;AAClBD,UAAUI,aAAa,SAASsQ,OAAO,KAAK3N,UAAZ,CAAhC;AACA,eAAO/C;MAHM,GAWfM,eAAeA,SAAiCC,GAAmB;AAC3D8T,YAAQ9T,EAAWC,aAAa,OAAxB;AACd,YAAI,CAAC6T,EAAO,OAAM,IAAI1C,UAAU,4BAAd;AAClB,aAAK5O,aAAaC,SAASqR,GAAO,EAAhB;AAClB,aAAKpR,aAAL;MAJiE,GAWnEC,gBAAgBA,WAAA;AACd,eAAO,EACL,WAAa,KAAKH,WADb;MADO,GAUhBI,gBAAgBA,SAAiCC,GAAyB;AACxE,aAAKL,aAAaK,EAAA;aACbH,aAAL;MAFwE,GAU1EI,WAAWA,SAET5G,GAAoB;AAEpB,cAAM6G,IAAiB7G,EAAU8G,SAC/B,6BADqB;AAGtBD,UAA4BE,QAA5B;AACD,YAAIxF,IAAasF,EAAevF,SAAS,OAAxB,EAAkCC;AACnD,iBAAS4D,IAAI,GAAGA,IAAI,KAAKmB,YAAYnB,KAAK;AACxC,gBAAM6B,IAAYhH,EAAU8G,SAC1B,wBADgB;AAGjBE,YAAuBD,QAAvB;AACD,cAAI,CAACC,EAAUE,mBACb,OAAU2Q,MAAM,qCAAV;AAERtW,YAAY0F,QAAQD,EAAUE,kBAA9B;AACA3F,cAAayF,EAAUG;QATiB;AAW1C,eAAON;MAlBa,GAyBtBO,SAASA,SAAiCP,GAAqB;AAC7D,YAAIG,IAA8BH,EAAeQ,oBAC/C,OADgC;AAKlC,aADMC,IAA4B,CAAA,GAC3BN,IACDA,GAAUO,kBAAV,KAIJD,EAAYxH,KAAKkH,EAAUQ,gBAA3B,GAHER,IAAYA,EAAUS,aAAV;aAOPtC,IAAI,GAAGA,IAAI,KAAKmB,YAAYnB,KAAK;AACxC,gBAAM5D,IAAa,KAAKD,SAAS,QAAQ6D,CAAtB,EAA0B5D,WAAYmG;AACrDnG,eAAc,CAAC+F,EAAYK,SAASpG,CAArB,KACjBA,EAAWqG,WAAX;QAHsC;AAM1C,aAAKtB,aAAagB,EAAYO;AAC9B,aAAKrB,aAAL;AAEA,aAASrB,IAAI,GAAGA,IAAI,KAAKmB,YAAYnB,KAAK;AACxC,cAAA;AAAA,mBAAA,IAAAmC,EAAYnC,CAAZ,MAAA,EAAgB2C,UAAU,MAAM,QAAQ3C,CAAxC;QADwC;MAxBmB,GAiC/D4C,iBAAiBA,SAAiClB,GAAqB;AACjEG,YAA8BH,EAAeQ,oBAC/C,OADgC;AAGlC,YAAIlC,IAAI;AACR,eAAO6B,KAAW;AAChB,cAAIA,EAAUO,kBAAV,GAA+B;AACjCP,gBAAYA,EAAUS,aAAV;AACZ;UAFiC;AAInC,gBAAMrC,IAAQ,KAAK9D,SAAS,QAAQ6D,CAAtB;AACeC,cAAAA;AAA7B4B,YAAUQ,mBACPE,SAD0BtC,IAAAA,KAC1BsC,SAD0BtC,EAAO7D,WACjCmG;AACHV,cAAYA,EAAUS,aAAV;;QARI;MALmD,GAoBvEjB,cAAcA,WAAA;AACR,aAAKF,cAAc,KAAKhF,SAAS,OAAd,IACrB,KAAK2C,YAAY,OAAjB,IACU,KAAKqC,cAAe,KAAKhF,SAAS,OAAd,KAC9B,KAAK4B,iBAAiB,OAAtB,EAA+BZ,YAC7B/C,EAAAA,+BAAA,wBADF;AAKF,iBAAS4F,IAAI,GAAGA,IAAI,KAAKmB,YAAYnB,IACnC,KAAI,CAAC,KAAK7D,SAAS,QAAQ6D,CAAtB,GAA0B;AAC7B,gBAAMC,IAAQ,KAAK/C,iBAAiB,QAAQ8C,CAA9B,EAAiC6C,SAASC,EAAAA,0CAAMC,KAAhD;AACJ,gBAAN/C,KACFC,EAAM9C,YAAY/C,EAAAA,+BAAA,4BAAlB;QAH2B;AAQjC,aAAS4F,IAAI,KAAKmB,YAAY,KAAKhF,SAAS,QAAQ6D,CAAtB,GAA0BA,IACtD,MAAKlB,YAAY,QAAQkB,CAAzB;MAnBU,EA7IU;AAoK1BrG,4CAAA,oBAA8B6Y;AAO9B,UAAMG,6DAA8B,EAIlC5V,MAAMA,WAAA;AACJ,aAAKE,SAAS,aAAd;AACA,aAAKc,iBAAL,EAAwBZ,YACtB/C,EAAAA,+BAAA,qCADF;AAGA,aAAK8J,qBAAqB,OAA1B;AACA,aAAKhG,WAAW9D,EAAAA,+BAAA,mCAAhB;AACA,aAAKkP,cAAc;MAPf,EAJ4B;4CAcpC,8BAAwCqJ;AASxC,UAAMC,wDAAyB,EAI7B7V,MAAMA,WAAA;AACJ,aAAKE,SAAS,aAAd;AACA,aAAKc,iBAAL,EAAwBZ,YAAY/C,EAAAA,+BAAA,4BAApC;AACA,aAAKgQ,qBAAqB,IAA1B;AACA,aAAKC,iBAAiB,IAAtB;AACA,aAAKnM,WAAW9D,EAAAA,+BAAA,8BAAhB;AACA,aAAKkP,cAAc;MANf,EAJuB;4CAa/B,yBAAmCsJ;UAO7BC,+CAAgB,EAIpB9V,MAAMA,WAAA;AACJ,YAAMiC,IAAY,CAChB,CAAC5E,EAAAA,+BAAA,sBAA6B,OAA9B,GACA,CAACA,EAAAA,+BAAA,qBAA4B,MAA7B,CAFgB;AAIlB,aAAK4C,WAAW5C,EAAAA,+BAAA,sBAAhB;AACA,aAAK6C,SAAS,aAAd;AACA,aAAKgB,UAAU,MAAM,QAArB;AACA,aAAKf,iBAAiB,OAAtB,EACGjB,SAAS,OADZ,EAEGkB,YAAY/C,EAAAA,+BAAA,4BAFf;AAGM0Y,YAAkCvV,EAAAA,+CAAS;UAC/CtD,MAAM;UACNF,SAASiF;QAFsC,CAAvB;AAI1B,YAAI,CAAC8T,EAAmB,OAAUJ,MAAM,0BAAV;AAC9B,aAAKxV,iBAAiB,MAAtB,EAA8BC,YAAY2V,GAAmB,KAA7D;AACA,aAAK9U,gBAAgB,IAArB;AACA,aAAKE,WAAW,MACP9D,EAAAA,+BAAA,uBAA8BY,QACnC,MACA,KAAKH,UAAUd,QAAQuJ,gBAAgB,MAAM,IAFxC,CADT;MAlBI,EAJc;AA8BtB3J,4CAAA,gBAA0BkZ;UASpBE,gDAAiB,EAIrBhW,MAAMA,WAAA;AACJ,YAAMiW,IAAO,CACX,CAAC5Y,EAAAA,+BAAA,qBAA4B,KAA7B,GACA,CAACA,EAAAA,+BAAA,4BAAmC,YAApC,GACA,CAACA,EAAAA,+BAAA,wBAA+B,QAAhC,CAHW;AAKb,aAAK6Y,gBAAgB;UACnB,CAAC7Y,EAAAA,+BAAA,4BAAmC,YAApC;UACA,CAACA,EAAAA,+BAAA,0BAAiC,UAAlC;UACA,CAACA,EAAAA,+BAAA,uBAA8B,OAA/B;UACA,CAACA,EAAAA,+BAAA,sBAA6B,MAA9B;UACA,CAACA,EAAAA,+BAAA,wBAA+B,QAAhC;QALmB;AAOrB,aAAK4C,WAAW5C,EAAAA,+BAAA,uBAAhB;AACA,aAAK6C,SAAS,aAAd;AACMiW,YAAyB3V,EAAAA,+CAAS,EACtCtD,MAAM,kBACNF,SAASiZ,EAF6B,CAAvB;AAIjBE,UAAS1V,aAEP,SAA+BC,GAAa;AACpC0V,cAAwB,aAAV1V;AACnB,eAAKI,eAAL,EAAwCuV,iBAAiBD,CAAzD;QAFyC,CAF9C;AAQA,aAAKjW,iBAAiB,OAAtB,EACGjB,SAAS,OADZ,EAEGkB,YAAY/C,EAAAA,+BAAA,6BAFf;aAGK2D,iBAAL,EACGZ,YAAY+V,GAAU,MADzB,EAEG/V,YAAY,IAAI,OAFnB;AAGMG,YAAqBC,EAAAA,+CAAS,EAClCtD,MAAM,kBACNF,SAAS,KAAKkZ,cAFoB,CAAvB;AAIb3V,UAAKE,aAEH,SAA+BC,GAAa;AAC1C,gBAAMC,IAA0B,KAAKC,SAAL;AAE1BC,cAAkB,iBAAVH,KAAoC,eAAVA;AACpCG,iBAFuB,iBAAbF,KAA0C,eAAbA,MAG3B,KAAKG,eAALxB,EACRyB,UAAUF,CAAhB;QANwC,CAF9C;AAaA,aAAKG,iBAAL,EAAwBZ,YAAYG,GAAM,OAA1C;AACA,aAAKS,iBAAiB,IAAtB;AACI3D,UAAAA,+BAAA,wBACF,KAAK2D,iBAAiB,MAAtB,EAA8BZ,YAAY/C,EAAAA,+BAAA,oBAA1C;aAEG4D,gBAAgB,IAArB;AACA,aAAKC,UAAU,IAAf;AACA,aAAKH,UAAU,IAAf;AACA,aAAKI,WAAW,MAAK;AACnB,gBAAMmV,IAAO,KAAKxX,cAAc,MAAnB,GACP6H,IAAQ,KAAK7H,cAAc,OAAnB;AACd,cAAI8H,IAAU;AACd,kBAAQ0P,IAAO,MAAM3P,GAArB;YACE,KAAK;YACL,KAAK;AACHC,kBAAUvJ,EAAAA,+BAAA;AACV;YACF,KAAK;AACHuJ,kBAAUvJ,EAAAA,+BAAA;AACV;YACF,KAAK;AACHuJ,kBAAUvJ,EAAAA,+BAAA;AACV;YACF,KAAK;AACHuJ,kBAAUvJ,EAAAA,+BAAA;;YAEZ,KAAK;YACL,KAAK;AACHuJ,kBAAUvJ,EAAAA,+BAAA;AACV;YACF,KAAK;AACHuJ,kBAAUvJ,EAAAA,+BAAA;AACV;YACF,KAAK;AACHuJ,kBAAUvJ,EAAAA,+BAAA;AACV;YACF,KAAK;AACHuJ,kBAAUvJ,EAAAA,+BAAA;AACV;YACF,KAAK;YACL,KAAK;AACHuJ,kBAAUvJ,EAAAA,+BAAA;AACV;YACF,KAAK;AACHuJ,kBAAUvJ,EAAAA,+BAAA;AACV;YACF,KAAK;AACHuJ,kBAAUvJ,EAAAA,+BAAA;AACV;YACF,KAAK;AACHuJ,kBAAUvJ,EAAAA,+BAAA;UAtCd;AAyCA,cAAc,iBAAVsJ,KAAoC,eAAVA,EAK5BC,MACE,QAJU,iBAAVD,IACItJ,EAAAA,+BAAA,iCACAA,EAAAA,+BAAA,8BAGAY;YAAQ;YAAM,KAAKH,UAAUd,QAAQuJ,gBAAgB,OAAO;UAAhE;AAEJ,iBAAOK;QAtDY,CAArB;MA1DI,GAyHNxF,eAAeA,WAAA;AACb,cAAMC,IAAqBC,EAAAA,+CAAc,UAAvB;AAElBD,UAAUI,aAAa,aAAasQ,OADhBqE,CAAC,KAAKnX,gBACU,CAApC;AACA,cAAM6C,IAAO,KAAK1C,SAAS,IAAd,aAA+BoC,EAAAA;AAC5CH,UAAUI,aAAa,MAAMsQ,OAAOjQ,CAAP,CAA7B;AACA,eAAOT;MANM,GAafM,eAAeA,SAA+BC,GAAmB;AAG/D,cAAMwU,IAAuD,WAAzCxU,EAAWC,aAAa,WAAxB;AACpB,aAAKwU,iBAAiBD,CAAtB;AACMtU,YAAyC,YAAlCF,EAAWC,aAAa,IAAxB;AACb,aAAKd,UAAUe,CAAf;MAN+D,GAcjEyC,gBAAgBA,WAAA;AAGd,eAAK,KAAKtF,mBAKH,OAJE,EACLmX,aAAa,KADR;MAJK,GAiBhB5R,gBAAgBA,SAA+BC,GAAyB;AAClEA,UAAA,cACF,KAAK4R,iBAAiB,IAAtB,IAC0B,aAAjB,OAAO5R,KAEhB,KAAK9C,cAAuB4U,EAAAA,2CAAU9R,CAAnB,CAAnB;MALoE,GAexE4R,kBAAkBA,SAA+BG,GAAqB;AAEhEA,cADiBC,CAAC,KAAKxX,qBAGxB,KAAK6K,OAAoD,MAAM,IAA/D,GACG0M,KACF,KAAKtV,UAAU,KAAf,GACA,KAAKmM,qBAAqB,IAA1B,GACA,KAAKC,iBAAiB,IAAtB,MAEA,KAAKD,qBAAqB,KAA1B,GACA,KAAKC,iBAAiB,KAAtB,GACA,KAAKpM,UAAU,IAAf;MAZgE,GAqBtEH,WAAWA,SAA+Be,GAAa;AAErD,aAAKC,YAAY,IAAjB;AACA,aAAKA,YAAY,WAAW,IAA5B;AAEID,aACF,KAAK3B,iBAAiB,IAAtB,EAA4BjB,SAAS,QAArC,GACI7B,EAAAA,+BAAA,yBACF,KAAK2D,iBAAiB,SAAtB,EAAiCZ,YAC/B/C,EAAAA,+BAAA,qBADF,KAKF,KAAK2D,iBAAiB,IAAtB;AAEE3D,UAAAA,+BAAA,wBACF,KAAK2E,gBAAgB,QAAQ,IAA7B;MAhBmD,EA7MlC;AAiOvBpF,4CAAA,iBAA2BoZ;UASrBU,gDAAiB,EAIrB1W,MAAMA,WAAA;AACJ,YAAMiW,IAAO,CACX,CAAC5Y,EAAAA,+BAAA,qBAA4B,KAA7B,GACA,CAACA,EAAAA,+BAAA,wBAA+B,QAAhC,CAFW;AAIb,aAAK6Y,gBAAgB,CACnB,CAAC7Y,EAAAA,+BAAA,4BAAmC,YAApC,GACA,CAACA,EAAAA,+BAAA,0BAAiC,UAAlC,GACA,CAACA,EAAAA,+BAAA,uBAA8B,OAA/B,GACA,CAACA,EAAAA,+BAAA,sBAA6B,MAA9B,GACA;UAACA,EAAAA,+BAAA;UAA+B;QAAhC,CALmB;AAOrB,aAAK4C,WAAW5C,EAAAA,+BAAA,uBAAhB;AACA,aAAK6C,SAAS,aAAd;AACA,aAAKC,iBAAiB,MAAtB,EACGjB,SAAS,OADZ,EAEGkB,YAAY/C,EAAAA,+BAAA,6BAFf;AAGMsZ,YAAkCnW,EAAAA,+CAAS,EAC/CtD,MAAM,kBACNF,SAASiZ,EAFsC,CAAvB;AAI1B,aAAKjV,iBAAL,EACGZ,YAAYuW,GAAmB,MADlC,EAEGvW,YAAY,IAAI,OAFnB;AAGMG,YAAqBC,EAAAA,+CAAS,EAClCtD,MAAM,kBACNF,SAAS,KAAKkZ,cAFoB,CAAvB;AAIb3V,UAAKE,aAEH,SAA+BC,GAAa;AAC1C,gBAAMC,IAA0B,KAAKC,SAAL;AAE1BC,cAAkB,iBAAVH,KAAoC,eAAVA;AACpCG,iBAFuB,iBAAbF,KAA0C,eAAbA,MAG3B,KAAKG,eAALxB,EACRyB,UAAUF,CAAhB;QANwC,CAF9C;AAaA,aAAKG,iBAAL,EAAwBZ,YAAYG,GAAM,OAA1C;AACA,aAAKS,iBAAiB,IAAtB;AACA,aAAKb,iBAAiB,IAAtB,EAA4BC,YAAY/C,EAAAA,+BAAA,wBAAxC;AACA,aAAK4D,gBAAgB,IAArB;AACA,aAAKoM,qBAAqB,IAA1B;AACA,aAAKC,iBAAiB,IAAtB;AACA,aAAKnM,WAAW9D,EAAAA,+BAAA,uBAAhB;AACA,aAAK0D,UAAU,IAAf;AACA,aAAKI,WAAW,MAAK;AACnB,gBAAMmV,IAAO,KAAKxX,cAAc,MAAnB,GACP6H,IAAQ,KAAK7H,cAAc,OAAnB;AACd,cAAI8H,IAAU;AACd,kBAAQ0P,IAAO,MAAM3P,GAArB;YACE,KAAK;YACL,KAAK;AACHC,kBAAUvJ,EAAAA,+BAAA;AACV;YACF,KAAK;AACHuJ,kBAAUvJ,EAAAA,+BAAA;AACV;YACF,KAAK;AACHuJ,kBAAUvJ,EAAAA,+BAAA;AACV;YACF,KAAK;AACHuJ,kBAAUvJ,EAAAA,+BAAA;AACV;YACF,KAAK;YACL,KAAK;AACHuJ,kBAAUvJ,EAAAA,+BAAA;AACV;YACF,KAAK;AACHuJ,kBAAUvJ,EAAAA,+BAAA;AACV;YACF,KAAK;AACHuJ,kBAAUvJ,EAAAA,+BAAA;AACV;YACF,KAAK;AACHuJ,kBAAUvJ,EAAAA,+BAAA;UAzBd;AA4BA,cAAc,iBAAVsJ,KAAoC,eAAVA,EAC5BC,MACE,OACAvJ,EAAAA,+BAAA,+BAAsCY,QACpC,MACA,KAAKH,UAAUd,QAAQuJ,gBAAgB,OAAO,IAFhD;AAKJ,iBAAOK;QAxCY,CAArB;MAjDI,GAiGNxF,eAAeA,WAAA;AACb,cAAMC,IAAqBC,EAAAA,+CAAc,UAAvB,GACZQ,IAAO,KAAK1C,SAAS,IAAd,aAA+BoC,EAAAA;AAC5CH,UAAUI,aAAa,MAAMsQ,OAAOjQ,CAAP,CAA7B;AACA,eAAOT;MAJM,GAWfM,eAAeA,SAA+BC,GAAmB;AAGzDE,YAAyC,YAAlCF,EAAWC,aAAa,IAAxB;AACb,aAAKd,UAAUe,CAAf;MAJ+D,GAejEyC,gBAAgBA,WAAA;AACd,eAAO;MADO,GAShBC,gBAAgBA,WAAA;MAAA,GAOhBzD,WAAWA,SAA+Be,GAAa;AAErD,aAAKC,YAAY,IAAjB;AACA,aAAKA,YAAY,WAAW,IAA5B;AAEID,aACF,KAAK3B,iBAAiB,IAAtB,EAA4BjB,SAAS,QAArC,GACI7B,EAAAA,+BAAA,yBACF,KAAK2D,iBAAiB,SAAtB,EAAiCZ,YAC/B/C,EAAAA,+BAAA,qBADF,KAKF,KAAK2D,iBAAiB,IAAtB;AAEF,aAAKgB,gBAAgB,MAAM,IAA3B;AACI,aAAK5C,SAAS,SAAd,KACF,KAAK4C,gBAAgB,WAAW,IAAhC;MAjBmD,EA/IlC;AAoKvBpF,4CAAA,iBAA2B8Z;UAUrBE,kDAAmB,EAIvB5W,MAAMA,WAAA;AACJ,aAAA,kBAA0B,CACxB,CAAC3C,EAAAA,+BAAA,oCAA2C,YAA5C,GACA,CAACA,EAAAA,+BAAA,kCAAyC,UAA1C,GACA,CAACA,EAAAA,+BAAA,+BAAsC,OAAvC,CAHwB;AAK1B,aAAA,kBAA0B;UACxB,CAACA,EAAAA,+BAAA,kCAAyC,YAA1C;UACA,CAACA,EAAAA,+BAAA,gCAAuC,UAAxC;UACA,CAACA,EAAAA,+BAAA,4BAAmC,MAApC;QAHwB;AAK1B,aAAK4C,WAAW5C,EAAAA,+BAAA,yBAAhB;AACA,aAAK6C,SAAS,aAAd;AACA,aAAKC,iBAAiB,MAAtB,EACGjB,SAAS,OADZ,EAEGkB,YAAY/C,EAAAA,+BAAA,+BAFf;AAGA,cAAMgD,IAAcC,OAA2B;AAC7C,gBAAMC,IAAqBC,EAAAA,+CAAS,EAClCtD,MAAM,kBACNF,SACE,KAAM,mBAAmBsD,CAAzB,EAHgC,CAAvB;AAKbC,YAAKE,aAEH,SAA+BC,GAAa;AAC1C,kBAAMC,IAA0B,KAAKC,SAAL;AAE1BC,gBAAkB,iBAAVH,KAAoC,eAAVA;AACpCG,mBAFuB,iBAAbF,KAA0C,eAAbA,MAG3B,KAAKG,eAALxB,EACRyB,UAAUT,GAAGO,CAAnB;UANwC,CAF9C;AAaA,iBAAON;QAnBsC;AAqB/C,aAAKS,iBAAiB,cAAtB,EAAsCZ,YAAYC,EAAW,CAAX,GAAe,QAAjE;AACA,aAAKW,iBAAiB,KAAtB;AACA,aAAKA,iBAAiB,cAAtB,EAAsCZ,YAAYC,EAAW,CAAX,GAAe,QAAjE;AACA,aAAKW,iBAAiB,KAAtB;AACI3D,UAAAA,+BAAA,0BACF,KAAK2D,iBAAiB,MAAtB,EAA8BZ,YAAY/C,EAAAA,+BAAA,sBAA1C;AAEF,aAAK4D,gBAAgB,IAArB;AACA,aAAKC,UAAU,MAAM,OAArB;AACA,aAAKH;UAAU;UAAG;QAAlB;AACA,aAAKA,UAAU,GAAG,IAAlB;AACA,aAAKI,WAAW9D,EAAAA,+BAAA,yBAAhB;MAhDI,GAuDN+D,eAAeA,WAAA;AACb,cAAMC,IAAqBC,EAAAA,+CAAc,UAAvB;AAClB,YAAMC,IAAQ,KAAKnC,SAAS,KAAd,aAAgCoC,EAAAA;AAC9CH,UAAUI,aAAa,OAAOsQ,OAAOxQ,CAAP,CAA9B;AACMG,YAAQ,KAAKtC,SAAS,KAAd,aAAgCoC,EAAAA;AAC9CH,UAAUI,aAAa,OAAOsQ,OAAOrQ,CAAP,CAA9B;AACA,eAAOL;MANM,GAafM,eAAeA,SAAiCC,GAAmB;AACjE,cAAML,IAA2C,WAAnCK,EAAWC,aAAa,KAAxB;YACmC,WAAnCD,EAAWC,aAAa,KAAxB;AACd,aAAKd,UAAU,GAAGQ,CAAlB;AACA,aAAKR,UAAU,GAAGW,CAAlB;MAJiE,GAenE6C,gBAAgBA,WAAA;AACd,eAAO;MADO,GAShBC,gBAAgBA,WAAA;MAAA,GAShBzD,WAAWA,SAAiCT,GAAUwB,GAAa;AAGjE,aAAKC,YAAY,OAAOzB,CAAxB;AACA,aAAKyB,YAAY,YAAYzB,GAAG,IAAhC;AAEIwB,aACF,KAAK3B,iBAAiB,OAAOG,CAA7B,EAAgCpB,SAAS,QAAzC,GACI7B,EAAAA,+BAAA,yBACF,KAAK2D,iBAAiB,YAAYV,CAAlC,EAAqCF,YACnC/C,EAAAA,+BAAA,qBADF,KAKF,KAAK2D,iBAAiB,OAAOV,CAA7B;AAEQ,cAANA,MACF,KAAK0B,gBAAgB,OAAO,cAA5B,GACI,KAAK5C,SAAS,UAAd,KACF,KAAK4C,gBAAgB,YAAY,cAAjC;AAGA3E,UAAAA,+BAAA,0BACF,KAAK2E,gBAAgB,QAAQ,IAA7B;MAvB+D,EAzG5C;AAoIzBpF,4CAAA,mBAA6Bga;4CAI7B,aAAuB,EAIrB5W,MAAMA,WAAA;AACJ,aAAKkC,SAAS;UACZ,UAAY;UACZ,OAAS,CACP,EACE,MAAQ,kBACR,MAAQ,QACR,SAAW,CACT,CAAC,kCAAkC,SAAnC,GACA,CAAC,+BAA+B,MAAhC,GACA,CAAC,qCAAqC,aAAtC,CAHS,EAHb,GASA,EACE,MAAQ,kBACR,MAAQ,aACR,SAAW,CACT,CAAC,qCAAqC,GAAtC,GACA,CAAC,sCAAsC,IAAvC,CAFS,EAHb,GAQA,EACE,MAAQ,eACR,MAAQ,QACR,OAAS,QAHX,CAlBO;UAwBT,QAAU;UACV,OAAS;UACT,SAAW;UACX,SAAW;QA7BC,CAAd;MADI,EAJe;4CAyCvB,cAAwB;QAItBlC,MAAMA,WAAA;AACJ,gBAAMuC,IAAyB/B,EAAAA,+CAAS,EACtCtD,MAAM,kBACNF,SAAS,CACP,CAACK,EAAAA,+BAAA,4BAAmC,OAApC,GACA,CAACA,EAAAA,+BAAA,4BAAmC,MAApC,CAFO,EAF6B,CAAvB;AAOjB,cAAI,CAACkF,EAAU,OAAUoT,MAAM,0BAAV;AACrBpT,YAAS9B,aAAcoW,OAAW;AAChC,iBAAKzU,YAAYyU,CAAjB;UADgC,CAAlC;AAGA,eAAK5W,WAAW5C,EAAAA,+BAAA,mBAAhB;AACA,eAAK6C,SAAS,aAAd;AACA,eAAKC,iBAAiB,OAAtB,EACGjB,SAAS,QADZ,EAEGkB;YAAYmC;YAAU;UAFzB;AAGA,eAAKpC,iBAAiB,OAAtB,EACGjB,SAAS,QADZ,EAEGkB,YAAY/C,EAAAA,+BAAA,0BAFf;AAGA,eAAK4D,gBAAgB,IAArB;AACA,eAAKC,UAAU,MAAM,OAArB;AACA,eAAKC,WAAW,MAAK;AACnB,kBAAMmV,IAAO,KAAKxX,cAAc,MAAnB;AACb,gBAAa,YAATwX,EACF,QAAOjZ,EAAAA,+BAAA;AACF,gBAAa,WAATiZ,EACT,QAAOjZ,EAAAA,+BAAA;AAET,kBAAMsY,MAAM,mBAAmBW,CAAzB;UAPa,CAArB;QAtBI;QAqCNlU,aAAaA,SAA4ByU,GAAe;AAEtD,cADa,KAAK/X,cAAc,MAAnBwX,MACAO,GAAS;AACpB,kBAAMC,IAAkB,KAAK1X,SAAS,OAAd,EAAwBC;AAChDyX,cAAiBC,aAAa,IAA9B;AACA,kBAAMC,IAAaF,EAAiBpN,YAAjB;AAEfsN,kBACFF,EAAiBpR,WAAjB,GACIsR,EAAW7B,SAAX,IACF6B,EAAW/F,QAAQ,KAAnB,IAEA,KAAKlH,eAAL;UAVgB;AAcN,sBAAZ8M,KACF,KAAK5X,iBAAkBC,SAAS,OAAhC,GACA,KAAKE,SAAS,OAAd,EAAwBF,SAAS,QAAjC,MAEA,KAAKD,iBAAkBC,SAAS,QAAhC,GACA,KAAKE,SAAS,OAAd,EAAwBF,SAAS,OAAjC;QArBoD;QA6BxDkC,eAAeA,WAAA;AACb,gBAAMC,IAAqBC,EAAAA,+CAAc,UAAvB;AAClBD,YAAUI,aAAa,QAAQ,KAAK3C,cAAc,MAAnB,CAA/B;AACA,iBAAOuC;QAHM;QAUfM,eAAeA,SAA4BC,GAAmB;AAC5D,eAAKQ,YAAYR,EAAWC,aAAa,MAAxB,CAAjB;QAD4D;QAS9D0C,gBAAgBA,WAAA;AACd,iBAAO,EAAC,MAAQ,KAAKzF,cAAc,MAAnB,EAAT;QADO;QAOhB0F,gBAAgBA,SAA4BC,GAAqB;AAC/D,eAAKrC,YAAYqC,EAAA,IAAjB;QAD+D;MAhG3C;AAsGxB5E,QAAAA,2CAAajD,qCAAb;AAxiCA,UAAAkD,gCAAA,EA4BalD,QAAAA,sCA5Bb;ACiCO,UAAMA,yCAA2CiM,OAAOoO,OAC7D,CAAA,GACMra,uCACAA,uCACAA,uCACDA,sCACMA,4CACLA,sCACIA,2CACOA,iDATqC,GAjCxDkD,iCAAA;QAiCalD,QAAAA;QAdXsa,OAAAA;QACAC,OAAAA;QACAC,OAAAA;QACAC,MAAAA;QACAC,YAAAA;QACAC,OAAAA;QACAC,WAAAA;QACAC,kBAAAA;MA1BF;;;;;;;;ACAA;AAAA;AACC,KAAC,SAAS,MAAM,SAAS;AACxB,UAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAC9C,eAAO,CAAC,gBAAgB,kBAAkB,gBAAgB,GAAG,OAAO;AAAA,MACtE,WAAW,OAAO,YAAY,UAAU;AACtC,eAAO,UAAU,QAAQ,8BAAyB,cAA2B,2BAAyB;AAAA,MACxG,OAAO;AACL,aAAK,UAAU,QAAQ,KAAK,SAAS,KAAK,QAAQ,KAAK,KAAK,QAAQ,MAAM;AAAA,MAC5E;AAAA,IACF,GAAE,SAAM,SAASC,UAAS,IAAI,QAAQ;AAoBtC;AAGA,MAAAA,SAAQ,UAAU,EAAE;AAEpB,aAAOA;AAAA,IACP,CAAC;AAAA;AAAA;;;ACnCD,mBAAoB;AACb,IAAM;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,aAAAC;", "names": ["<PERSON><PERSON>", "blocks", "createBlockDefinitionsFromJsonArray", "CUSTOM_CONTEXT_MENU_VARIABLE_GETTER_SETTER_MIXIN", "customContextMenu", "options", "isInFlyout", "type", "oppositeType", "contextMenuMsg", "Msg", "<PERSON><PERSON><PERSON>", "getField", "newVarBlockState", "fields", "VAR", "saveState", "push", "enabled", "workspace", "remainingCapacity", "text", "replace", "getText", "callback", "callbackFactory", "renameOption", "renameOptionCallbackFactory", "name", "deleteOption", "deleteOptionCallbackFactory", "unshift", "onchange", "_e", "id", "getFieldValue", "variableModel", "getVariable", "outputConnection", "<PERSON><PERSON><PERSON><PERSON>", "getType", "getInput", "connection", "block", "variable", "variableField", "renameVariable", "deleteVariable", "getWorkspace", "registerMixin", "defineBlocks", "$jscomp$tmp$exports$module$name", "GET_SUBSTRING_BLOCK", "init", "setHelpUrl", "setStyle", "appendValueInput", "appendField", "createMenu", "n", "menu", "fromJson", "setValidator", "value", "oldValue", "getValue", "newAt", "getSourceBlock", "updateAt_", "appendDummyInput", "setInputsInline", "setOutput", "setTooltip", "mutationToDom", "container", "createElement", "isAt1", "ValueInput", "setAttribute", "isAt2", "domToMutation", "xmlElement", "getAttribute", "isAt", "removeInput", "moveInputBefore", "OPERATORS", "jsonInit", "PROMPT_COMMON", "updateType_", "newOp", "TYPES", "dropdown", "mixin", "QUOTE_IMAGE_MIXIN", "newQuote_", "QUOTE_IMAGE_LEFT_DATAURI", "QUOTE_IMAGE_RIGHT_DATAURI", "QUOTE_IMAGE_WIDTH", "QUOTE_IMAGE_HEIGHT", "quoteField_", "fieldName", "i", "input", "inputList", "j", "field", "fieldRow", "insertFieldAt", "console", "warn", "toDevString", "open", "isLeft", "RTL", "src", "width", "height", "alt", "QUOTES_EXTENSION", "JOIN_MUTATOR_MIXIN", "itemCount_", "parseInt", "updateShape_", "saveExtraState", "loadExtraState", "state", "decompose", "containerBlock", "newBlock", "initSvg", "itemBlock", "connect", "previousConnection", "nextConnection", "compose", "getInputTargetBlock", "connections", "isInsertionMarker", "valueConnection_", "getNextBlock", "targetConnection", "includes", "disconnect", "length", "reconnect", "saveConnections", "setAlign", "Align", "RIGHT", "JOIN_EXTENSION", "setMutator", "MutatorIcon", "register", "buildTooltipWithFieldText", "INDEXOF_TOOLTIP_EXTENSION", "oneBasedIndex", "CHARAT_MUTATOR_MIXIN", "isAt_", "CHARAT_EXTENSION", "where", "tooltip", "msg", "registerMutator", "PROCEDURE_DEF_COMMON", "setStatements_", "hasStatements", "hasStatements_", "appendStatementInput", "updateParams_", "paramString", "arguments_", "join", "disable", "setFieldValue", "enable", "opt_paramIds", "argumentVarModels_", "parameter", "argModel", "getName", "getId", "paramIds_", "append<PERSON><PERSON><PERSON>", "childNode", "childNodes", "nodeName", "toLowerCase", "childElement", "varName", "varId", "getOrCreateVariablePackage", "log", "mutateCallers", "Object", "create", "param", "containerBlockNode", "statementNode", "argBlockNode", "fieldNode", "argumentName", "createTextNode", "nextNode", "node", "dom<PERSON>o<PERSON>lock", "paramBlock", "targetBlock", "statementConnection_", "stackConnection", "stackBlock", "unplug", "bumpNeighbours", "getVars", "getVarModels", "renameVarById", "oldId", "newId", "oldVariable", "getVariableById", "old<PERSON>ame", "newVar", "change", "displayRenamedVar_", "updateVarName", "newName", "mutator", "getIcon", "<PERSON><PERSON><PERSON>", "TYPE", "bubbleIsVisible", "getAllBlocks", "Names", "equals", "callProcedureBlockState", "callType_", "extraState", "params", "isCollapsed", "argVar", "getVarBlockState", "initName", "findLegalName", "nameField", "rename", "setSpellcheck", "comments", "parentWorkspace", "setCommentText", "getProcedureDef", "PROCEDURES_MUTATORCONTAINER", "checked", "contextMenu", "ProcedureArgumentField", "FieldTextInput", "constructor", "arguments", "editingInteractively", "showEditor_", "e", "editingVariable", "undefined", "onFinishEditing_", "PROCEDURES_MUTATORARGUMENT", "DEFAULT_ARG", "validator_", "setPreviousStatement", "setNextStatement", "sourceBlock", "outerWs", "getRootWorkspace", "targetWorkspace", "caselessName", "otherVar", "model", "renameVariableById", "createVariable", "DISABLED_PROCEDURE_DEFINITION_DISABLED_REASON", "PROCEDURE_CALL_COMMON", "getProcedureCall", "renameProcedure", "setProcedureParameters_", "paramNames", "paramIds", "defBlock", "getDefinition", "mutatorOpen", "mutatorIcon", "setCollapsed", "quarkConnections_", "quarkIds_", "RangeError", "concat", "quarkId", "<PERSON>rgField", "setValue", "newField", "topRow", "removeField", "args", "ids", "fill", "event", "isFlyout", "recordUndo", "BLOCK_CREATE", "def", "defType_", "JSON", "stringify", "isEnabled", "setDisabledReason", "setWarningText", "setGroup", "group", "xml", "xy", "getRelativeToSurfaceXY", "y", "config", "snapRadius", "x", "mutation", "callName", "domToWorkspace", "BLOCK_DELETE", "dispose", "BLOCK_CHANGE", "element", "blockId", "oldGroup", "getGroup", "valid", "isMovable", "centerOnBlock", "getFocusManager", "focusNode", "UNPARENTED_IFRETURN_DISABLED_REASON", "PROCEDURES_IFRETURN", "hasReturnValue_", "String", "Number", "isDragging", "BLOCK_MOVE", "legal", "FUNCTION_TYPES", "getSurroundParent", "setRecordUndo", "TOOLTIPS_BY_OP", "buildTooltipForDropdown", "IS_DIVISIBLEBY_MUTATOR_MIXIN", "divisorInput", "inputExists", "IS_DIVISIBLE_MUTATOR_EXTENSION", "option", "LIST_MODES_MUTATOR_MIXIN", "op", "TypeError", "LIST_MODES_MUTATOR_EXTENSION", "bind", "WHILE_UNTIL_TOOLTIPS", "BREAK_CONTINUE_TOOLTIPS", "CUSTOM_CONTEXT_MENU_CREATE_VARIABLES_GET_MIXIN", "loopTypes", "Set", "CONTROL_FLOW_NOT_IN_LOOP_DISABLED_REASON", "CONTROL_FLOW_IN_LOOP_CHECK_MIXIN", "getSurroundLoop", "has", "ws", "CONTROLS_IF_MUTATOR_MIXIN", "elseifCount_", "elseCount_", "rebuildShape_", "elseifBlock", "else<PERSON>lock", "clauseBlock", "valueConnections", "statementConnections", "elseStatementConnection", "reconnectChildBlocks_", "inputIf", "inputDo", "CONTROLS_IF_TOOLTIP_EXTENSION", "LOGIC_COMPARE_ONCHANGE_MIXIN", "prevBlocks_", "blockA", "blockB", "connection<PERSON><PERSON>cker", "doTypeChecks", "prevA", "isDisposed", "is<PERSON><PERSON>ow", "prevB", "LOGIC_COMPARE_EXTENSION", "LOGIC_TERNARY_ONCHANGE_MIXIN", "prevParentConnection_", "parentConnection", "LISTS_CREATE_WITH", "items", "Error", "LISTS_CREATE_WITH_CONTAINER", "LISTS_CREATE_WITH_ITEM", "LISTS_INDEXOF", "operatorsDropdown", "LISTS_GETINDEX", "MODE", "WHERE_OPTIONS", "modeMenu", "isStatement", "updateStatement_", "mode", "textToDom", "newStatement", "oldStatement", "LISTS_SETINDEX", "operationDropdown", "LISTS_GETSUBLIST", "newMode", "inputConnection", "setShadowDom", "inputBlock", "assign", "lists", "logic", "loops", "math", "procedures", "texts", "variables", "variablesDynamic", "<PERSON><PERSON>", "<PERSON><PERSON>"]}