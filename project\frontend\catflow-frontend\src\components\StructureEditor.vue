<template>
  <div class="structure-editor">
    <div class="editor-header">
      <h2>CatFlow 智能体结构编辑器</h2>
      <div class="editor-controls">
        <button @click="loadFromAPI" class="btn-load" :disabled="loading">
          {{ loading ? '加载中...' : '从API加载' }}
        </button>
        <button @click="addExampleStructure" class="btn-example">添加示例</button>
        <button @click="generateJSON" class="btn-generate">生成JSON</button>
        <button @click="saveToAPI" class="btn-save" :disabled="!hasChanges">保存到API</button>
        <button @click="clearWorkspace" class="btn-clear">清空</button>
      </div>
    </div>

    <div class="editor-content">
      <div class="structure-selector" v-if="availableStructures.length > 0">
        <label>选择结构:</label>
        <select v-model="selectedStructure" @change="loadSelectedStructure">
          <option value="">-- 选择一个结构 --</option>
          <option v-for="structName in availableStructures" :key="structName" :value="structName">
            {{ structName }}
          </option>
        </select>
      </div>

      <div class="workspace-container">
        <div ref="blocklyDiv" class="blockly-workspace"></div>
        
        <div class="output-panel" v-if="generatedJSON">
          <h3>生成的JSON配置:</h3>
          <pre><code>{{ generatedJSON }}</code></pre>
          <div class="output-controls">
            <button @click="copyToClipboard" class="btn-copy">复制到剪贴板</button>
            <button @click="downloadJSON" class="btn-download">下载JSON</button>
          </div>
        </div>
      </div>
    </div>

    <div class="status-bar">
      <span class="status-text">{{ statusMessage }}</span>
      <span class="api-status" :class="{ 'connected': apiConnected, 'disconnected': !apiConnected }">
        API: {{ apiConnected ? '已连接' : '未连接' }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as Blockly from 'blockly'
import 'blockly/javascript'
import { registerCustomBlocks, generateStructureCode, structConfigToBlocklyXML } from '../utils/customBlocks'
import { StructConfigService, HealthService, type StructConfig } from '../services/api'

/**
 * 结构体关系编辑器组件
 * 基于Blockly实现可视化的智能体结构编辑
 */

const blocklyDiv = ref<HTMLElement>()
const loading = ref(false)
const hasChanges = ref(false)
const statusMessage = ref('就绪')
const apiConnected = ref(false)
const availableStructures = ref<string[]>([])
const selectedStructure = ref('')
const generatedJSON = ref('')
const originalMetaData = ref<any>(null)
const allStructuresData = ref<any>(null)

let workspace: Blockly.WorkspaceSvg | null = null

/**
 * 初始化Blockly工作区
 */
const initBlockly = () => {
  if (!blocklyDiv.value) return

  // 注册自定义块
  registerCustomBlocks()

  // 定义工具箱配置
  const toolbox = {
    kind: 'categoryToolbox',
    contents: [
      {
        kind: 'category',
        name: '结构组件',
        colour: '#5C81A6',
        contents: [
          {
            kind: 'block',
            type: 'structure_block'
          },
          {
            kind: 'block',
            type: 'main_agent_block'
          },
          {
            kind: 'block',
            type: 'sub_structure_block'
          },
          {
            kind: 'block',
            type: 'agent_ref_block'
          },
          {
            kind: 'block',
            type: 'config_block'
          }
        ]
      }
    ]
  }

  // 创建工作区
  workspace = Blockly.inject(blocklyDiv.value, {
    toolbox: toolbox,
    grid: {
      spacing: 20,
      length: 3,
      colour: '#ccc',
      snap: true
    },
    zoom: {
      controls: true,
      wheel: true,
      startScale: 0.8,
      maxScale: 3,
      minScale: 0.2,
      scaleSpeed: 1.2
    },
    trashcan: true,
    scrollbars: true,
    sounds: false,
    oneBasedIndex: false,
    renderer: 'zelos',
    theme: 'modern'
  })

  // 监听工作区变化
  workspace.addChangeListener(() => {
    hasChanges.value = true
  })

  // 不自动添加示例结构，等待用户从API加载真实数据
  // setTimeout(() => {
  //   addExampleStructure()
  // }, 100)
}

/**
 * 添加示例结构
 */
const addExampleStructure = () => {
  if (!workspace) {
    console.warn('工作区未初始化，跳过添加示例结构')
    return
  }

  try {
    const xml = Blockly.utils.xml.textToDom(`
      <xml xmlns="https://developers.google.com/blockly/xml">
        <block type="structure_block" x="20" y="20">
          <field name="STRUCT_NAME">示例结构</field>
          <field name="STRUCT_TYPE">coordinator</field>
          <field name="DESCRIPTION">展示正确的层次关系：主agent → 子结构 → 智能体</field>
          <statement name="COMPONENTS">
            <block type="main_agent_block">
              <field name="AGENT_NAME">main_agent</field>
              <field name="AGENT_TYPE">loop_agent</field>
              <field name="MODEL">qwen-max</field>
              <field name="INSTRUCTION">协调各个子结构的执行</field>
              <statement name="SUB_STRUCTURES">
                <block type="sub_structure_block">
                  <field name="SUB_NAME">weather_structure</field>
                  <field name="SUB_TYPE">sequential</field>
                  <field name="SUB_DESCRIPTION">处理天气查询相关任务</field>
                  <statement name="SUB_AGENT_REFS">
                    <block type="agent_ref_block">
                      <field name="AGENT_NAME">weatherAgent</field>
                      <next>
                        <block type="agent_ref_block">
                          <field name="AGENT_NAME">locationAgent</field>
                        </block>
                      </next>
                    </block>
                  </statement>
                  <next>
                    <block type="sub_structure_block">
                      <field name="SUB_NAME">summary_structure</field>
                      <field name="SUB_TYPE">sequential</field>
                      <field name="SUB_DESCRIPTION">处理总结相关任务</field>
                      <statement name="SUB_AGENT_REFS">
                        <block type="agent_ref_block">
                          <field name="AGENT_NAME">summaryAgent</field>
                          <next>
                            <block type="agent_ref_block">
                              <field name="AGENT_NAME">reportAgent</field>
                            </block>
                          </next>
                        </block>
                      </statement>
                    </block>
                  </next>
                </block>
              </statement>
            </block>
          </statement>
        </block>
      </xml>
    `)
    Blockly.Xml.domToWorkspace(xml, workspace)
    hasChanges.value = false
    console.log('示例结构添加成功')
  } catch (error) {
    console.error('添加示例结构失败:', error)
  }
}

/**
 * 检查API连接状态
 */
const checkAPIConnection = async () => {
  try {
    await HealthService.checkHealth()
    apiConnected.value = true
    statusMessage.value = 'API连接正常'
  } catch (error) {
    apiConnected.value = false
    statusMessage.value = 'API连接失败'
    console.error('API连接检查失败:', error)
  }
}

/**
 * 从API加载结构配置
 */
const loadFromAPI = async () => {
  if (!apiConnected.value) {
    statusMessage.value = 'API未连接，无法加载数据'
    return
  }

  loading.value = true
  statusMessage.value = '正在从API加载结构配置...'

  try {
    const response = await StructConfigService.getAllStructConfigs()
    console.log('API响应:', response)

    if (response.structures) {
      // 保存完整的数据和meta信息
      allStructuresData.value = response
      originalMetaData.value = response.meta

      availableStructures.value = Object.keys(response.structures)
      statusMessage.value = `成功加载 ${availableStructures.value.length} 个结构配置`

      console.log('可用结构:', availableStructures.value)
      console.log('原始meta数据:', originalMetaData.value)
    } else {
      statusMessage.value = '未找到结构配置数据'
      console.warn('响应中没有structures字段:', response)
    }
  } catch (error) {
    statusMessage.value = '加载结构配置失败'
    console.error('加载失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 加载选中的结构
 */
const loadSelectedStructure = async () => {
  if (!selectedStructure.value || !workspace) return

  try {
    // 优先从已加载的数据中获取
    let structConfig
    if (allStructuresData.value && allStructuresData.value.structures[selectedStructure.value]) {
      structConfig = allStructuresData.value.structures[selectedStructure.value]
      console.log('从缓存加载结构:', structConfig)
    } else {
      // 如果缓存中没有，则从API获取
      structConfig = await StructConfigService.getStructConfig(selectedStructure.value)
      console.log('从API加载结构:', structConfig)
    }

    loadStructureToWorkspace(structConfig)
    statusMessage.value = `已加载结构: ${selectedStructure.value}`
    hasChanges.value = false
  } catch (error) {
    statusMessage.value = `加载结构失败: ${selectedStructure.value}`
    console.error('加载结构失败:', error)
  }
}

/**
 * 将结构配置加载到工作区
 */
const loadStructureToWorkspace = (structConfig: StructConfig) => {
  if (!workspace) return

  workspace.clear()

  try {
    // 使用完整的转换函数
    const xmlString = structConfigToBlocklyXML(structConfig)
    console.log('生成的XML:', xmlString)

    const xml = Blockly.utils.xml.textToDom(xmlString)
    Blockly.Xml.domToWorkspace(xml, workspace)

    console.log('结构已加载到工作区')
  } catch (error) {
    console.error('加载结构到工作区失败:', error)
    statusMessage.value = '加载结构到工作区失败'

    // 如果转换失败，添加一个基本的结构块
    addBasicStructureBlock(structConfig)
  }
}

/**
 * 添加基本结构块（备用方案）
 */
const addBasicStructureBlock = (structConfig: StructConfig) => {
  if (!workspace) return

  const xmlString = `
    <xml xmlns="https://developers.google.com/blockly/xml">
      <block type="structure_block" x="20" y="20">
        <field name="STRUCT_NAME">${structConfig.name || '未命名结构'}</field>
        <field name="STRUCT_TYPE">${structConfig.type || 'coordinator'}</field>
        <field name="DESCRIPTION">${structConfig.description || ''}</field>
      </block>
    </xml>
  `

  try {
    const xml = Blockly.utils.xml.textToDom(xmlString)
    Blockly.Xml.domToWorkspace(xml, workspace)
  } catch (error) {
    console.error('添加基本结构块失败:', error)
  }
}

/**
 * 生成JSON配置
 */
const generateJSON = () => {
  if (!workspace) return

  try {
    // 使用原始meta数据生成JSON
    const structureData = generateStructureCode(workspace, originalMetaData.value)
    generatedJSON.value = JSON.stringify(structureData, null, 2)
    statusMessage.value = 'JSON配置生成成功'
    console.log('生成的JSON数据:', structureData)
  } catch (error) {
    statusMessage.value = 'JSON生成失败'
    console.error('生成JSON失败:', error)
  }
}

/**
 * 保存到API
 */
const saveToAPI = async () => {
  if (!generatedJSON.value || !apiConnected.value) {
    statusMessage.value = '没有可保存的数据或API未连接'
    return
  }

  try {
    const data = JSON.parse(generatedJSON.value)
    const structNames = Object.keys(data.structures || {})

    if (structNames.length === 0) {
      statusMessage.value = '没有找到可保存的结构'
      return
    }

    statusMessage.value = '正在保存结构配置...'

    // 保存所有结构
    let savedCount = 0
    for (const structName of structNames) {
      try {
        const structConfig = data.structures[structName]
        console.log(`保存结构 ${structName}:`, structConfig)

        await StructConfigService.createOrUpdateStructConfig(structName, structConfig)
        savedCount++
        console.log(`结构 ${structName} 保存成功`)
      } catch (error) {
        console.error(`保存结构 ${structName} 失败:`, error)
        statusMessage.value = `保存结构 ${structName} 失败`
        return
      }
    }

    statusMessage.value = `成功保存 ${savedCount} 个结构配置`
    hasChanges.value = false

    // 重新加载数据以确保同步
    await loadFromAPI()
  } catch (error) {
    statusMessage.value = '保存失败'
    console.error('保存失败:', error)
  }
}

/**
 * 清空工作区
 */
const clearWorkspace = () => {
  if (!workspace) return
  
  if (confirm('确定要清空工作区吗？')) {
    workspace.clear()
    generatedJSON.value = ''
    hasChanges.value = false
    statusMessage.value = '工作区已清空'
  }
}

/**
 * 复制到剪贴板
 */
const copyToClipboard = async () => {
  if (!generatedJSON.value) return

  try {
    await navigator.clipboard.writeText(generatedJSON.value)
    statusMessage.value = 'JSON已复制到剪贴板'
  } catch (error) {
    statusMessage.value = '复制失败'
    console.error('复制失败:', error)
  }
}

/**
 * 下载JSON文件
 */
const downloadJSON = () => {
  if (!generatedJSON.value) return

  const blob = new Blob([generatedJSON.value], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'structure-config.json'
  a.click()
  URL.revokeObjectURL(url)
  statusMessage.value = 'JSON文件下载完成'
}

onMounted(async () => {
  initBlockly()
  await checkAPIConnection()
  if (apiConnected.value) {
    await loadFromAPI()
  }
})

onUnmounted(() => {
  if (workspace) {
    workspace.dispose()
    workspace = null
  }
})
</script>

<style scoped>
.structure-editor {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
}

.editor-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.editor-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.editor-controls {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.editor-controls button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.btn-load {
  background-color: #28a745;
  color: white;
}

.btn-load:hover:not(:disabled) {
  background-color: #218838;
  transform: translateY(-1px);
}

.btn-load:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.btn-example {
  background-color: #17a2b8;
  color: white;
}

.btn-example:hover {
  background-color: #138496;
  transform: translateY(-1px);
}

.btn-generate {
  background-color: #007bff;
  color: white;
}

.btn-generate:hover {
  background-color: #0056b3;
  transform: translateY(-1px);
}

.btn-save {
  background-color: #ffc107;
  color: #212529;
}

.btn-save:hover:not(:disabled) {
  background-color: #e0a800;
  transform: translateY(-1px);
}

.btn-save:disabled {
  background-color: #6c757d;
  color: white;
  cursor: not-allowed;
}

.btn-clear {
  background-color: #dc3545;
  color: white;
}

.btn-clear:hover {
  background-color: #c82333;
  transform: translateY(-1px);
}

.editor-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  gap: 1rem;
  min-height: 0; /* 允许flex子元素收缩 */
  overflow: hidden; /* 防止内容溢出 */
}

.structure-selector {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.structure-selector label {
  font-weight: 600;
  color: #495057;
}

.structure-selector select {
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.9rem;
  min-width: 200px;
}

.workspace-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  min-height: 0;
  overflow: hidden;
}

.blockly-workspace {
  flex: 1;
  min-height: 0; /* 移除固定最小高度，让它自适应 */
  border: 2px solid #dee2e6;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* 确保Blockly工具栏图标正确显示 */
.blockly-workspace :deep(.blocklyToolboxDiv) {
  background-color: #f8f9fa;
  border-right: 1px solid #dee2e6;
}

.blockly-workspace :deep(.blocklyZoom) {
  position: absolute !important;
  bottom: 10px !important;
  right: 10px !important;
  z-index: 1000 !important;
}

.blockly-workspace :deep(.blocklyZoom .blocklyZoomButton) {
  background-color: white !important;
  border: 1px solid #ccc !important;
  border-radius: 4px !important;
  margin: 2px !important;
  padding: 4px !important;
  cursor: pointer !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
  font-size: 14px !important;
  width: 24px !important;
  height: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.blockly-workspace :deep(.blocklyZoom .blocklyZoomButton:hover) {
  background-color: #f0f0f0 !important;
}

.blockly-workspace :deep(.blocklyTrash) {
  position: absolute !important;
  bottom: 10px !important;
  left: 10px !important;
  z-index: 1000 !important;
}

.output-panel {
  background: white;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  max-height: 250px; /* 减少最大高度 */
  min-height: 150px; /* 设置最小高度 */
  overflow-y: auto;
  flex-shrink: 0; /* 防止被压缩 */
}

.output-panel h3 {
  margin: 0 0 1rem 0;
  color: #495057;
  font-size: 1.1rem;
}

.output-panel pre {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 1rem;
  margin: 0 0 1rem 0;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  line-height: 1.4;
}

.output-controls {
  display: flex;
  gap: 0.5rem;
}

.btn-copy, .btn-download {
  padding: 0.4rem 0.8rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-copy {
  background-color: #6c757d;
  color: white;
}

.btn-copy:hover {
  background-color: #5a6268;
}

.btn-download {
  background-color: #17a2b8;
  color: white;
}

.btn-download:hover {
  background-color: #138496;
}

.status-bar {
  background: #343a40;
  color: white;
  padding: 0.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
}

.status-text {
  flex: 1;
}

.api-status {
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
}

.api-status.connected {
  background-color: #28a745;
  color: white;
}

.api-status.disconnected {
  background-color: #dc3545;
  color: white;
}

/* PC浏览器优化 */
@media (min-width: 1024px) {
  .editor-header {
    padding: 1.5rem 3rem;
  }

  .editor-header h2 {
    font-size: 1.8rem;
  }

  .editor-controls button {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }

  .editor-content {
    padding: 1.5rem;
  }

  .blockly-workspace {
    min-height: calc(100vh - 300px); /* 确保PC上有足够的工作区高度 */
  }

  .output-panel {
    max-height: 300px;
    min-height: 200px;
  }
}

/* 响应式设计 - 平板和手机 */
@media (max-width: 1023px) {
  .editor-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
    padding: 1rem;
  }

  .editor-controls {
    flex-wrap: wrap;
    justify-content: center;
  }

  .structure-selector {
    flex-direction: column;
    align-items: flex-start;
  }

  .blockly-workspace {
    min-height: 400px;
  }

  .status-bar {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
}
</style>
