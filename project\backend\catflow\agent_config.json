{"meta": {"description": "智能体配置文件", "purpose": "定义系统中所有智能体的配置信息，包括模型、指令、工具等", "structure": {"agents": {"type": "object", "description": "智能体配置对象，包含所有智能体的定义", "properties": {"[agent_name]": {"type": "object", "description": "单个智能体的配置", "properties": {"name": {"type": "string", "description": "智能体名称，必须唯一", "required": true}, "model": {"type": "string", "description": "使用的语言模型名称，需在llm_config.json中定义", "required": true}, "instruction": {"type": "string", "description": "智能体的系统指令，定义其行为和职责", "required": true}, "tools": {"type": "array", "description": "智能体可使用的工具列表，工具名需在tool_config.json中定义", "items": "string", "required": false}, "agent_type": {"type": "string", "description": "智能体类型，如LlmAgent、SequentialAgent等", "required": true}, "generate_content_config": {"type": "object", "description": "内容生成配置", "properties": {"temperature": {"type": "number", "description": "生成温度，控制输出的随机性，范围0-1", "range": "0.0-1.0"}, "max_output_tokens": {"type": "integer", "description": "最大输出token数量"}}}}}}}}, "usage": {"description": "使用方法", "steps": ["1. 在agents对象中定义新的智能体", "2. 设置智能体的基本属性：name、model、instruction", "3. 根据需要配置tools数组，添加所需工具", "4. 设置agent_type指定智能体类型", "5. 配置generate_content_config调整生成参数"], "examples": {"basic_agent": {"name": "exampleAgent", "model": "qwen-max", "instruction": "你是一个示例智能体", "tools": [], "agent_type": "LlmAgent", "generate_content_config": {"temperature": 0.7, "max_output_tokens": 204800}}}}}, "agents": {"planTravelAgent": {"name": "planTravelAgent", "model": "qwen-max", "instruction": "你是一个旅游计划规划的智能助手，通过用户的问题规划一个旅游计划，包括旅游目的地每天的天气、餐厅推荐（如没有要求可以随机）、旅游景点、住宿等。", "tools": [], "agent_type": "LlmAgent", "generate_content_config": {"temperature": 0.7, "max_output_tokens": 204800}}, "weatherAgent": {"name": "weatherAgent", "model": "qwen-max", "instruction": "你是一个查询天气的智能助手，必须使用可用的工具来回答用户问题。只能通过调用weather_query和Time工具来获取信息并回答问题，用户没有具体要求时间，则跟据当地时间获取。", "tools": ["weather_query", "get_current_time"], "agent_type": "LlmAgent", "generate_content_config": {"temperature": 0.7, "max_output_tokens": 204800}}, "restaurantAgent": {"name": "restaurantAgent", "model": "qwen-max", "instruction": "你是一个查询餐厅的智能助手，必须使用可用的工具来回答用户问题。只能通过调用restaurant_recommend工具来获取信息并回答问题。", "tools": ["restaurant_recommend"], "agent_type": "LlmAgent", "generate_content_config": {"temperature": 0.7, "max_output_tokens": 204800}}, "hotelAgent": {"name": "hotelAgent", "model": "qwen-max", "instruction": "你是一个查询住宿的智能助手，必须使用可用的工具来回答用户问题。若用户无提供具体参数，可随机生成参数，只能通过调用hotel_recommend工具来获取信息并回答问题。", "tools": ["hotel_recommend"], "agent_type": "LlmAgent", "generate_content_config": {"temperature": 0.7, "max_output_tokens": 204800}}, "summaryAgent": {"name": "summaryAgent", "model": "qwen-max", "instruction": "你是一个擅长总结的智能助手，不要丢弃任何之前对话的结果，需通过之前的agent获取信息，并生成一个**HTML内容极为丰富**的页面来总结回答问题，不限于图片，表格，文本，界面显示要丰富美观。该HTML页面需完全独立，不依赖任何外部文件（包括但不限于CSS文件、JavaScript文件、图片文件等），所有样式、交互逻辑及所需资源均需内嵌于HTML文件中。最终生成的HTML文件需通过工具保存到D:\\Ready\\Adk\\reporting目录下，且文件名需为随机名称。", "tools": ["write_file"], "agent_type": "LlmAgent", "generate_content_config": {"temperature": 0.4, "max_output_tokens": 204800}}}}