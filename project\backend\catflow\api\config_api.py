from fastapi import FastAP<PERSON>, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import Dict, Any, Optional
import json
import os
from pathlib import Path

# 获取配置文件路径
CURRENT_DIR = Path(__file__).parent.parent
LLM_CONFIG_PATH = CURRENT_DIR / "llm_config.json"
TOOL_CONFIG_PATH = CURRENT_DIR / "tool_config.json"

app = FastAPI(
    title="CatFlow配置管理API",
    description="管理LLM配置和工具配置的API接口",
    version="1.0.0"
)

# Pydantic模型定义
class LLMConfig(BaseModel):
    """LLM配置模型"""
    model: str
    api_base: str
    api_key: str
    api_key_env: str
    temperature: float
    input_cost_per_token: float
    output_cost_per_token: float

class LLMConfigUpdate(BaseModel):
    """LLM配置更新模型"""
    model: Optional[str] = None
    api_base: Optional[str] = None
    api_key: Optional[str] = None
    api_key_env: Optional[str] = None
    temperature: Optional[float] = None
    input_cost_per_token: Optional[float] = None
    output_cost_per_token: Optional[float] = None

class ToolConfig(BaseModel):
    """工具配置模型"""
    name: str
    descriptions: Dict[str, Any]
    type: str
    llm_name: str
    instruction: str
    temperature: Optional[float] = None
    json_schema: Optional[Dict[str, Any]] = None
    connection_type: Optional[str] = None
    connection_params: Optional[Dict[str, Any]] = None

class ToolConfigUpdate(BaseModel):
    """工具配置更新模型"""
    name: Optional[str] = None
    descriptions: Optional[Dict[str, Any]] = None
    type: Optional[str] = None
    llm_name: Optional[str] = None
    instruction: Optional[str] = None
    temperature: Optional[float] = None
    json_schema: Optional[Dict[str, Any]] = None
    connection_type: Optional[str] = None
    connection_params: Optional[Dict[str, Any]] = None

# 辅助函数
def read_json_file(file_path: Path) -> Dict[str, Any]:
    """读取JSON文件"""
    try:
        if not file_path.exists():
            raise HTTPException(status_code=404, detail=f"配置文件不存在: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except json.JSONDecodeError as e:
        raise HTTPException(status_code=400, detail=f"JSON格式错误: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"读取文件失败: {str(e)}")

def write_json_file(file_path: Path, data: Dict[str, Any]) -> None:
    """写入JSON文件"""
    try:
        # 确保目录存在
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"写入文件失败: {str(e)}")

# LLM配置相关API
@app.get("/llm-config", response_model=Dict[str, Any], summary="获取所有LLM配置")
async def get_llm_config():
    """获取所有LLM配置信息"""
    return read_json_file(LLM_CONFIG_PATH)

@app.get("/llm-config/{llm_name}", response_model=LLMConfig, summary="获取指定LLM配置")
async def get_llm_config_by_name(llm_name: str):
    """根据名称获取指定的LLM配置"""
    config_data = read_json_file(LLM_CONFIG_PATH)
    
    if "llms" not in config_data or llm_name not in config_data["llms"]:
        raise HTTPException(status_code=404, detail=f"LLM配置不存在: {llm_name}")
    
    return config_data["llms"][llm_name]

@app.post("/llm-config/{llm_name}", summary="创建或更新LLM配置")
async def create_or_update_llm_config(llm_name: str, config: LLMConfig):
    """创建或更新指定的LLM配置"""
    config_data = read_json_file(LLM_CONFIG_PATH)
    
    if "llms" not in config_data:
        config_data["llms"] = {}
    
    config_data["llms"][llm_name] = config.dict()
    write_json_file(LLM_CONFIG_PATH, config_data)
    
    return {"message": f"LLM配置 '{llm_name}' 已成功保存"}

@app.put("/llm-config/{llm_name}", summary="部分更新LLM配置")
async def update_llm_config(llm_name: str, config_update: LLMConfigUpdate):
    """部分更新指定的LLM配置"""
    config_data = read_json_file(LLM_CONFIG_PATH)
    
    if "llms" not in config_data or llm_name not in config_data["llms"]:
        raise HTTPException(status_code=404, detail=f"LLM配置不存在: {llm_name}")
    
    # 只更新提供的字段
    update_data = config_update.dict(exclude_unset=True)
    config_data["llms"][llm_name].update(update_data)
    
    write_json_file(LLM_CONFIG_PATH, config_data)
    
    return {"message": f"LLM配置 '{llm_name}' 已成功更新"}

@app.delete("/llm-config/{llm_name}", summary="删除LLM配置")
async def delete_llm_config(llm_name: str):
    """删除指定的LLM配置"""
    config_data = read_json_file(LLM_CONFIG_PATH)
    
    if "llms" not in config_data or llm_name not in config_data["llms"]:
        raise HTTPException(status_code=404, detail=f"LLM配置不存在: {llm_name}")
    
    del config_data["llms"][llm_name]
    write_json_file(LLM_CONFIG_PATH, config_data)
    
    return {"message": f"LLM配置 '{llm_name}' 已成功删除"}

# 工具配置相关API
@app.get("/tool-config", response_model=Dict[str, Any], summary="获取所有工具配置")
async def get_tool_config():
    """获取所有工具配置信息"""
    return read_json_file(TOOL_CONFIG_PATH)

@app.get("/tool-config/{tool_name}", response_model=ToolConfig, summary="获取指定工具配置")
async def get_tool_config_by_name(tool_name: str):
    """根据名称获取指定的工具配置"""
    config_data = read_json_file(TOOL_CONFIG_PATH)
    
    if "tools" not in config_data or tool_name not in config_data["tools"]:
        raise HTTPException(status_code=404, detail=f"工具配置不存在: {tool_name}")
    
    return config_data["tools"][tool_name]

@app.post("/tool-config/{tool_name}", summary="创建或更新工具配置")
async def create_or_update_tool_config(tool_name: str, config: ToolConfig):
    """创建或更新指定的工具配置"""
    config_data = read_json_file(TOOL_CONFIG_PATH)
    
    if "tools" not in config_data:
        config_data["tools"] = {}
    
    config_data["tools"][tool_name] = config.dict(exclude_unset=True)
    write_json_file(TOOL_CONFIG_PATH, config_data)
    
    return {"message": f"工具配置 '{tool_name}' 已成功保存"}

@app.put("/tool-config/{tool_name}", summary="部分更新工具配置")
async def update_tool_config(tool_name: str, config_update: ToolConfigUpdate):
    """部分更新指定的工具配置"""
    config_data = read_json_file(TOOL_CONFIG_PATH)
    
    if "tools" not in config_data or tool_name not in config_data["tools"]:
        raise HTTPException(status_code=404, detail=f"工具配置不存在: {tool_name}")
    
    # 只更新提供的字段
    update_data = config_update.dict(exclude_unset=True)
    config_data["tools"][tool_name].update(update_data)
    
    write_json_file(TOOL_CONFIG_PATH, config_data)
    
    return {"message": f"工具配置 '{tool_name}' 已成功更新"}

@app.delete("/tool-config/{tool_name}", summary="删除工具配置")
async def delete_tool_config(tool_name: str):
    """删除指定的工具配置"""
    config_data = read_json_file(TOOL_CONFIG_PATH)
    
    if "tools" not in config_data or tool_name not in config_data["tools"]:
        raise HTTPException(status_code=404, detail=f"工具配置不存在: {tool_name}")
    
    del config_data["tools"][tool_name]
    write_json_file(TOOL_CONFIG_PATH, config_data)
    
    return {"message": f"工具配置 '{tool_name}' 已成功删除"}

# 健康检查
@app.get("/health", summary="健康检查")
async def health_check():
    """API健康检查"""
    return {"status": "healthy", "message": "CatFlow配置管理API运行正常"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)