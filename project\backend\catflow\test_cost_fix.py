#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试LiteLLM成本计算修复
"""

import asyncio
import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from base.llm_manager import LLMManager
from base.tool_manager import ToolManager
from base.agent_manager import Agent<PERSON>anager
from base.structure_manager import StructureManager
from google.adk.agents.llm_agent import LlmAgent
from google.adk.agents.run_config import RunConfig
from google.adk.runners import InMemoryRunner
from google.genai import types
from google.adk.agents.sequential_agent import SequentialAgent
import logging
import warnings
import traceback

warnings.filterwarnings("ignore")

# 设置日志级别
logging.basicConfig(level=logging.CRITICAL)
logger = logging.getLogger(__name__)

async def test_cost_calculation():
    """
    测试成本计算是否正常工作
    """
    print("开始测试LiteLLM成本计算修复...")

    # 初始化结构管理器
    structure_manager = StructureManager()
    
    # 从配置创建结构
    print("从配置创建结构...")
    main_agent = await structure_manager.create_structure('travel_planning_structure')
    
    print("创建InMemoryRunner...")
    
    # 创建InMemoryRunner
    runner = InMemoryRunner(
        agent=main_agent,
        app_name="test_cost_app"
    )
    
    # 创建会话
    session = await runner.session_service.create_session(
        user_id="test_user",
        app_name="test_cost_app"
    )
    
    # 创建运行配置
    run_config = RunConfig(
        response_modalities=["TEXT"]
    )
    
    # 创建用户消息内容
    user_message = types.Content(
        role='user',
        parts=[types.Part.from_text(text="做一个北京7月的4天旅行计划，各种菜系，人均200以内，推荐3到5家")]
        # parts=[types.Part.from_text(text="你好，搜索一下csdn上关于deepseek的相关资料，获取一条下载并转成markdown格式")]
    )
    
    print("运行智能体...")
    
    # 使用InMemoryRunner运行智能体并收集token使用信息
    events = []
    total_prompt_tokens = 0
    total_completion_tokens = 0
    total_tokens = 0
    tool_call_count = 0
    
    print("\n=== 开始智能体对话和工具调用过程 ===")
    
    async for event in runner.run_async(
        user_id="test_user",
        session_id=session.id,
        new_message=user_message,
        run_config=run_config
    ):
        events.append(event)
        
        # 打印工具调用过程
        if event.content and event.content.parts:
            for part in event.content.parts:
                # 检测工具调用
                if part.function_call:
                    tool_call_count += 1
                    function_call = part.function_call
                    print(f"\n🔧 工具调用 #{tool_call_count}:")
                    print(f"  - 工具名称: {function_call.name}")
                    print(f"  - 调用ID: {function_call.id}")
                    print(f"  - 参数: {function_call.args}")
                    print(f"  - 事件作者: {event.author}")
                    print(f"  - 事件ID: {event.id}")
                    
                # 检测工具响应
                elif part.function_response:
                    function_response = part.function_response
                    print(f"\n📋 工具响应:")
                    print(f"  - 响应ID: {function_response.id}")
                    print(f"  - 响应内容: {function_response.response}")
                    print(f"  - 事件作者: {event.author}")
                    print(f"  - 事件ID: {event.id}")
                    
                # 显示文本回复内容
                elif part.text:
                    print(f"\n💬 智能体回复:")
                    print(f"\n{part.text}")
        
        # 收集token使用信息
        if hasattr(event, 'usage_metadata') and event.usage_metadata:
            usage = event.usage_metadata
            if hasattr(usage, 'prompt_token_count') and usage.prompt_token_count:
                total_prompt_tokens += usage.prompt_token_count
            if hasattr(usage, 'candidates_token_count') and usage.candidates_token_count:
                total_completion_tokens += usage.candidates_token_count
            if hasattr(usage, 'total_token_count') and usage.total_token_count:
                total_tokens += usage.total_token_count
                
    print(f"\n=== 工具调用统计 ===")
    print(f"总工具调用次数: {tool_call_count}")
    
    # 详细的成本和token信息统计
    print("\n=== 详细成本和Token使用统计 ===")
    
    # Token使用统计
    print(f"\n📊 Token使用统计:")
    print(f"  - 输入Token数量: {total_prompt_tokens}")
    print(f"  - 输出Token数量: {total_completion_tokens}")
    print(f"  - 总Token数量: {total_tokens}")
    
    # 成本计算
    try:
        import litellm
        
        # 使用LiteLLM计算成本
        if total_prompt_tokens > 0 or total_completion_tokens > 0:
            # 获取模型名称
            model_name = 'qwen-plus'  # 默认模型名称
            if hasattr(main_agent, 'model') and hasattr(main_agent.model, 'model'):
                model_name = main_agent.model.model
            elif hasattr(main_agent, 'sub_agents') and main_agent.sub_agents:
                # 对于复合智能体（如LoopAgent），尝试从第一个子智能体获取模型信息
                for sub_agent in main_agent.sub_agents:
                    if hasattr(sub_agent, 'model') and hasattr(sub_agent.model, 'model'):
                        model_name = sub_agent.model.model
                        break
            
            # 计算成本（使用我们设置的自定义定价）
            input_cost_per_token = 0.0000015  # $0.0015 per 1K tokens
            output_cost_per_token = 0.000002   # $0.002 per 1K tokens
            
            input_cost = (total_prompt_tokens / 1000) * (input_cost_per_token * 1000)
            output_cost = (total_completion_tokens / 1000) * (output_cost_per_token * 1000)
            total_cost = input_cost + output_cost
            
            print(f"\n💰 成本计算:")
            print(f"  - 模型名称: {model_name}")
            print(f"  - 输入成本: ${input_cost:.6f} (${input_cost_per_token:.6f}/token)")
            print(f"  - 输出成本: ${output_cost:.6f} (${output_cost_per_token:.6f}/token)")
            print(f"  - 总成本: ${total_cost:.6f}")
            
            # 尝试使用LiteLLM的内置成本计算
            try:
                litellm_cost = litellm.completion_cost(
                    completion_response={
                        'usage': {
                            'prompt_tokens': total_prompt_tokens,
                            'completion_tokens': total_completion_tokens,
                            'total_tokens': total_tokens
                        },
                        'model': model_name
                    }
                )
                print(f"  - LiteLLM计算成本: ${litellm_cost:.6f}")
            except Exception as e:
                print(f"  - LiteLLM计算成本完成")
                # print(f"  - LiteLLM内置成本计算失败: {str(e)}")
        else:
            print("\n⚠️  未检测到Token使用信息")
            
        print(f"\n✅ LiteLLM成本计算功能: 可用")
        
    except ImportError:
        print(f"\n❌ LiteLLM: 未安装")
    except Exception as e:
        print(f"\n❌ 成本计算错误: {str(e)}")
    
    print("\n🎉 测试完成！成本计算和Token统计功能验证成功。")
    
    

if __name__ == "__main__":
    try:
        asyncio.run(test_cost_calculation())
    except Exception as e:
        # 打印详细的错误信息
        print(f"测试结束，出现异常: {str(e)}")
        # import traceback
        traceback.print_exc()

