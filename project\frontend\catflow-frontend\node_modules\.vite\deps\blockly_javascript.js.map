{"version": 3, "sources": ["../../blockly/generators/javascript/lists.ts", "../../blockly/generators/javascript/logic.ts", "../../blockly/generators/javascript/loops.ts", "../../blockly/generators/javascript/math.ts", "../../blockly/generators/javascript/procedures.ts", "../../blockly/generators/javascript/text.ts", "../../blockly/generators/javascript/variables.ts", "../../blockly/generators/javascript/javascript_generator.ts", "../../blockly/generators/javascript/variables_dynamic.ts", "../../blockly/generators/javascript.ts", "../../blockly/javascript.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright 2012 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n/**\n * @file Generating JavaScript for list blocks.\n */\n\n// Former goog.module ID: Blockly.JavaScript.lists\n\nimport type {CreateWithBlock} from '../../blocks/lists.js';\nimport type {Block} from '../../core/block.js';\nimport {NameType} from '../../core/names.js';\nimport type {JavascriptGenerator} from './javascript_generator.js';\nimport {Order} from './javascript_generator.js';\n\nexport function lists_create_empty(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Create an empty list.\n  return ['[]', Order.ATOMIC];\n}\n\nexport function lists_create_with(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Create a list with any number of elements of any type.\n  const createWithBlock = block as CreateWithBlock;\n  const elements = new Array(createWithBlock.itemCount_);\n  for (let i = 0; i < createWithBlock.itemCount_; i++) {\n    elements[i] = generator.valueToCode(block, 'ADD' + i, Order.NONE) || 'null';\n  }\n  const code = '[' + elements.join(', ') + ']';\n  return [code, Order.ATOMIC];\n}\n\nexport function lists_repeat(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Create a list with one element repeated.\n  const functionName = generator.provideFunction_(\n    'listsRepeat',\n    `\nfunction ${generator.FUNCTION_NAME_PLACEHOLDER_}(value, n) {\n  var array = [];\n  for (var i = 0; i < n; i++) {\n    array[i] = value;\n  }\n  return array;\n}\n`,\n  );\n  const element = generator.valueToCode(block, 'ITEM', Order.NONE) || 'null';\n  const repeatCount = generator.valueToCode(block, 'NUM', Order.NONE) || '0';\n  const code = functionName + '(' + element + ', ' + repeatCount + ')';\n  return [code, Order.FUNCTION_CALL];\n}\n\nexport function lists_length(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // String or array length.\n  const list = generator.valueToCode(block, 'VALUE', Order.MEMBER) || '[]';\n  return [list + '.length', Order.MEMBER];\n}\n\nexport function lists_isEmpty(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Is the string null or array empty?\n  const list = generator.valueToCode(block, 'VALUE', Order.MEMBER) || '[]';\n  return ['!' + list + '.length', Order.LOGICAL_NOT];\n}\n\nexport function lists_indexOf(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Find an item in the list.\n  const operator =\n    block.getFieldValue('END') === 'FIRST' ? 'indexOf' : 'lastIndexOf';\n  const item = generator.valueToCode(block, 'FIND', Order.NONE) || \"''\";\n  const list = generator.valueToCode(block, 'VALUE', Order.MEMBER) || '[]';\n  const code = list + '.' + operator + '(' + item + ')';\n  if (block.workspace.options.oneBasedIndex) {\n    return [code + ' + 1', Order.ADDITION];\n  }\n  return [code, Order.FUNCTION_CALL];\n}\n\nexport function lists_getIndex(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] | string {\n  // Get element at index.\n  // Note: Until January 2013 this block did not have MODE or WHERE inputs.\n  const mode = block.getFieldValue('MODE') || 'GET';\n  const where = block.getFieldValue('WHERE') || 'FROM_START';\n  const listOrder = where === 'RANDOM' ? Order.NONE : Order.MEMBER;\n  const list = generator.valueToCode(block, 'VALUE', listOrder) || '[]';\n\n  switch (where) {\n    case 'FIRST':\n      if (mode === 'GET') {\n        const code = list + '[0]';\n        return [code, Order.MEMBER];\n      } else if (mode === 'GET_REMOVE') {\n        const code = list + '.shift()';\n        return [code, Order.MEMBER];\n      } else if (mode === 'REMOVE') {\n        return list + '.shift();\\n';\n      }\n      break;\n    case 'LAST':\n      if (mode === 'GET') {\n        const code = list + '.slice(-1)[0]';\n        return [code, Order.MEMBER];\n      } else if (mode === 'GET_REMOVE') {\n        const code = list + '.pop()';\n        return [code, Order.MEMBER];\n      } else if (mode === 'REMOVE') {\n        return list + '.pop();\\n';\n      }\n      break;\n    case 'FROM_START': {\n      const at = generator.getAdjusted(block, 'AT');\n      if (mode === 'GET') {\n        const code = list + '[' + at + ']';\n        return [code, Order.MEMBER];\n      } else if (mode === 'GET_REMOVE') {\n        const code = list + '.splice(' + at + ', 1)[0]';\n        return [code, Order.FUNCTION_CALL];\n      } else if (mode === 'REMOVE') {\n        return list + '.splice(' + at + ', 1);\\n';\n      }\n      break;\n    }\n    case 'FROM_END': {\n      const at = generator.getAdjusted(block, 'AT', 1, true);\n      if (mode === 'GET') {\n        const code = list + '.slice(' + at + ')[0]';\n        return [code, Order.FUNCTION_CALL];\n      } else if (mode === 'GET_REMOVE') {\n        const code = list + '.splice(' + at + ', 1)[0]';\n        return [code, Order.FUNCTION_CALL];\n      } else if (mode === 'REMOVE') {\n        return list + '.splice(' + at + ', 1);';\n      }\n      break;\n    }\n    case 'RANDOM': {\n      const functionName = generator.provideFunction_(\n        'listsGetRandomItem',\n        `\nfunction ${generator.FUNCTION_NAME_PLACEHOLDER_}(list, remove) {\n  var x = Math.floor(Math.random() * list.length);\n  if (remove) {\n    return list.splice(x, 1)[0];\n  } else {\n    return list[x];\n  }\n}\n`,\n      );\n      const code = functionName + '(' + list + ', ' + (mode !== 'GET') + ')';\n      if (mode === 'GET' || mode === 'GET_REMOVE') {\n        return [code, Order.FUNCTION_CALL];\n      } else if (mode === 'REMOVE') {\n        return code + ';\\n';\n      }\n      break;\n    }\n  }\n  throw Error('Unhandled combination (lists_getIndex).');\n}\n\nexport function lists_setIndex(block: Block, generator: JavascriptGenerator) {\n  // Set element at index.\n  // Note: Until February 2013 this block did not have MODE or WHERE inputs.\n  let list = generator.valueToCode(block, 'LIST', Order.MEMBER) || '[]';\n  const mode = block.getFieldValue('MODE') || 'GET';\n  const where = block.getFieldValue('WHERE') || 'FROM_START';\n  const value = generator.valueToCode(block, 'TO', Order.ASSIGNMENT) || 'null';\n  // Cache non-trivial values to variables to prevent repeated look-ups.\n  // Closure, which accesses and modifies 'list'.\n  function cacheList() {\n    if (list.match(/^\\w+$/)) {\n      return '';\n    }\n    const listVar = generator.nameDB_!.getDistinctName(\n      'tmpList',\n      NameType.VARIABLE,\n    )!;\n    const code = 'var ' + listVar + ' = ' + list + ';\\n';\n    list = listVar;\n    return code;\n  }\n  switch (where) {\n    case 'FIRST':\n      if (mode === 'SET') {\n        return list + '[0] = ' + value + ';\\n';\n      } else if (mode === 'INSERT') {\n        return list + '.unshift(' + value + ');\\n';\n      }\n      break;\n    case 'LAST':\n      if (mode === 'SET') {\n        let code = cacheList();\n        code += list + '[' + list + '.length - 1] = ' + value + ';\\n';\n        return code;\n      } else if (mode === 'INSERT') {\n        return list + '.push(' + value + ');\\n';\n      }\n      break;\n    case 'FROM_START': {\n      const at = generator.getAdjusted(block, 'AT');\n      if (mode === 'SET') {\n        return list + '[' + at + '] = ' + value + ';\\n';\n      } else if (mode === 'INSERT') {\n        return list + '.splice(' + at + ', 0, ' + value + ');\\n';\n      }\n      break;\n    }\n    case 'FROM_END': {\n      const at = generator.getAdjusted(\n        block,\n        'AT',\n        1,\n        false,\n        Order.SUBTRACTION,\n      );\n      let code = cacheList();\n      if (mode === 'SET') {\n        code += list + '[' + list + '.length - ' + at + '] = ' + value + ';\\n';\n        return code;\n      } else if (mode === 'INSERT') {\n        code +=\n          list +\n          '.splice(' +\n          list +\n          '.length - ' +\n          at +\n          ', 0, ' +\n          value +\n          ');\\n';\n        return code;\n      }\n      break;\n    }\n    case 'RANDOM': {\n      let code = cacheList();\n      const xVar = generator.nameDB_!.getDistinctName(\n        'tmpX',\n        NameType.VARIABLE,\n      );\n      code +=\n        'var ' + xVar + ' = Math.floor(Math.random() * ' + list + '.length);\\n';\n      if (mode === 'SET') {\n        code += list + '[' + xVar + '] = ' + value + ';\\n';\n        return code;\n      } else if (mode === 'INSERT') {\n        code += list + '.splice(' + xVar + ', 0, ' + value + ');\\n';\n        return code;\n      }\n      break;\n    }\n  }\n  throw Error('Unhandled combination (lists_setIndex).');\n}\n\n/**\n * Returns an expression calculating the index into a list.\n * @param listName Name of the list, used to calculate length.\n * @param where The method of indexing, selected by dropdown in Blockly\n * @param opt_at The optional offset when indexing from start/end.\n * @returns Index expression.\n */\nconst getSubstringIndex = function (\n  listName: string,\n  where: string,\n  opt_at?: string,\n): string | undefined {\n  if (where === 'FIRST') {\n    return '0';\n  } else if (where === 'FROM_END') {\n    return listName + '.length - 1 - ' + opt_at;\n  } else if (where === 'LAST') {\n    return listName + '.length - 1';\n  } else {\n    return opt_at;\n  }\n};\n\nexport function lists_getSublist(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Get sublist.\n  // Dictionary of WHEREn field choices and their CamelCase equivalents.\n  const wherePascalCase = {\n    'FIRST': 'First',\n    'LAST': 'Last',\n    'FROM_START': 'FromStart',\n    'FROM_END': 'FromEnd',\n  };\n  type WhereOption = keyof typeof wherePascalCase;\n  const list = generator.valueToCode(block, 'LIST', Order.MEMBER) || '[]';\n  const where1 = block.getFieldValue('WHERE1') as WhereOption;\n  const where2 = block.getFieldValue('WHERE2') as WhereOption;\n  let code;\n  if (where1 === 'FIRST' && where2 === 'LAST') {\n    code = list + '.slice(0)';\n  } else if (\n    list.match(/^\\w+$/) ||\n    (where1 !== 'FROM_END' && where2 === 'FROM_START')\n  ) {\n    // If the list is a variable or doesn't require a call for length, don't\n    // generate a helper function.\n    let at1;\n    switch (where1) {\n      case 'FROM_START':\n        at1 = generator.getAdjusted(block, 'AT1');\n        break;\n      case 'FROM_END':\n        at1 = generator.getAdjusted(block, 'AT1', 1, false, Order.SUBTRACTION);\n        at1 = list + '.length - ' + at1;\n        break;\n      case 'FIRST':\n        at1 = '0';\n        break;\n      default:\n        throw Error('Unhandled option (lists_getSublist).');\n    }\n    let at2;\n    switch (where2) {\n      case 'FROM_START':\n        at2 = generator.getAdjusted(block, 'AT2', 1);\n        break;\n      case 'FROM_END':\n        at2 = generator.getAdjusted(block, 'AT2', 0, false, Order.SUBTRACTION);\n        at2 = list + '.length - ' + at2;\n        break;\n      case 'LAST':\n        at2 = list + '.length';\n        break;\n      default:\n        throw Error('Unhandled option (lists_getSublist).');\n    }\n    code = list + '.slice(' + at1 + ', ' + at2 + ')';\n  } else {\n    const at1 = generator.getAdjusted(block, 'AT1');\n    const at2 = generator.getAdjusted(block, 'AT2');\n    // The value for 'FROM_END' and'FROM_START' depends on `at` so\n    // we add it as a parameter.\n    const at1Param =\n      where1 === 'FROM_END' || where1 === 'FROM_START' ? ', at1' : '';\n    const at2Param =\n      where2 === 'FROM_END' || where2 === 'FROM_START' ? ', at2' : '';\n    const functionName = generator.provideFunction_(\n      'subsequence' + wherePascalCase[where1] + wherePascalCase[where2],\n      `\nfunction ${\n        generator.FUNCTION_NAME_PLACEHOLDER_\n      }(sequence${at1Param}${at2Param}) {\n  var start = ${getSubstringIndex('sequence', where1, 'at1')};\n  var end = ${getSubstringIndex('sequence', where2, 'at2')} + 1;\n  return sequence.slice(start, end);\n}\n`,\n    );\n    code =\n      functionName +\n      '(' +\n      list +\n      // The value for 'FROM_END' and 'FROM_START' depends on `at` so we\n      // pass it.\n      (where1 === 'FROM_END' || where1 === 'FROM_START' ? ', ' + at1 : '') +\n      (where2 === 'FROM_END' || where2 === 'FROM_START' ? ', ' + at2 : '') +\n      ')';\n  }\n  return [code, Order.FUNCTION_CALL];\n}\n\nexport function lists_sort(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Block for sorting a list.\n  const list =\n    generator.valueToCode(block, 'LIST', Order.FUNCTION_CALL) || '[]';\n  const direction = block.getFieldValue('DIRECTION') === '1' ? 1 : -1;\n  const type = block.getFieldValue('TYPE');\n  const getCompareFunctionName = generator.provideFunction_(\n    'listsGetSortCompare',\n    `\nfunction ${generator.FUNCTION_NAME_PLACEHOLDER_}(type, direction) {\n  var compareFuncs = {\n    'NUMERIC': function(a, b) {\n        return Number(a) - Number(b); },\n    'TEXT': function(a, b) {\n        return String(a) > String(b) ? 1 : -1; },\n    'IGNORE_CASE': function(a, b) {\n        return String(a).toLowerCase() > String(b).toLowerCase() ? 1 : -1; },\n  };\n  var compare = compareFuncs[type];\n  return function(a, b) { return compare(a, b) * direction; };\n}\n      `,\n  );\n  return [\n    list +\n      '.slice().sort(' +\n      getCompareFunctionName +\n      '(\"' +\n      type +\n      '\", ' +\n      direction +\n      '))',\n    Order.FUNCTION_CALL,\n  ];\n}\n\nexport function lists_split(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Block for splitting text into a list, or joining a list into text.\n  let input = generator.valueToCode(block, 'INPUT', Order.MEMBER);\n  const delimiter = generator.valueToCode(block, 'DELIM', Order.NONE) || \"''\";\n  const mode = block.getFieldValue('MODE');\n  let functionName;\n  if (mode === 'SPLIT') {\n    if (!input) {\n      input = \"''\";\n    }\n    functionName = 'split';\n  } else if (mode === 'JOIN') {\n    if (!input) {\n      input = '[]';\n    }\n    functionName = 'join';\n  } else {\n    throw Error('Unknown mode: ' + mode);\n  }\n  const code = input + '.' + functionName + '(' + delimiter + ')';\n  return [code, Order.FUNCTION_CALL];\n}\n\nexport function lists_reverse(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Block for reversing a list.\n  const list =\n    generator.valueToCode(block, 'LIST', Order.FUNCTION_CALL) || '[]';\n  const code = list + '.slice().reverse()';\n  return [code, Order.FUNCTION_CALL];\n}\n", "/**\n * @license\n * Copyright 2012 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n/**\n * @file Generating JavaScript for logic blocks.\n */\n\n// Former goog.module ID: Blockly.JavaScript.logic\n\nimport type {Block} from '../../core/block.js';\nimport type {JavascriptGenerator} from './javascript_generator.js';\nimport {Order} from './javascript_generator.js';\n\nexport function controls_if(block: Block, generator: JavascriptGenerator) {\n  // If/elseif/else condition.\n  let n = 0;\n  let code = '';\n  if (generator.STATEMENT_PREFIX) {\n    // Automatic prefix insertion is switched off for this block.  Add manually.\n    code += generator.injectId(generator.STATEMENT_PREFIX, block);\n  }\n  do {\n    const conditionCode =\n      generator.valueToCode(block, 'IF' + n, Order.NONE) || 'false';\n    let branchCode = generator.statementToCode(block, 'DO' + n);\n    if (generator.STATEMENT_SUFFIX) {\n      branchCode =\n        generator.prefixLines(\n          generator.injectId(generator.STATEMENT_SUFFIX, block),\n          generator.INDENT,\n        ) + branchCode;\n    }\n    code +=\n      (n > 0 ? ' else ' : '') +\n      'if (' +\n      conditionCode +\n      ') {\\n' +\n      branchCode +\n      '}';\n    n++;\n  } while (block.getInput('IF' + n));\n\n  if (block.getInput('ELSE') || generator.STATEMENT_SUFFIX) {\n    let branchCode = block.getInput('ELSE')\n      ? generator.statementToCode(block, 'ELSE')\n      : '';\n    if (generator.STATEMENT_SUFFIX) {\n      branchCode =\n        generator.prefixLines(\n          generator.injectId(generator.STATEMENT_SUFFIX, block),\n          generator.INDENT,\n        ) + branchCode;\n    }\n    code += ' else {\\n' + branchCode + '}';\n  }\n  return code + '\\n';\n}\n\nexport const controls_ifelse = controls_if;\n\nexport function logic_compare(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Comparison operator.\n  const OPERATORS = {\n    'EQ': '==',\n    'NEQ': '!=',\n    'LT': '<',\n    'LTE': '<=',\n    'GT': '>',\n    'GTE': '>=',\n  };\n  type OperatorOption = keyof typeof OPERATORS;\n  const operator = OPERATORS[block.getFieldValue('OP') as OperatorOption];\n  const order =\n    operator === '==' || operator === '!=' ? Order.EQUALITY : Order.RELATIONAL;\n  const argument0 = generator.valueToCode(block, 'A', order) || '0';\n  const argument1 = generator.valueToCode(block, 'B', order) || '0';\n  const code = argument0 + ' ' + operator + ' ' + argument1;\n  return [code, order];\n}\n\nexport function logic_operation(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Operations 'and', 'or'.\n  const operator = block.getFieldValue('OP') === 'AND' ? '&&' : '||';\n  const order = operator === '&&' ? Order.LOGICAL_AND : Order.LOGICAL_OR;\n  let argument0 = generator.valueToCode(block, 'A', order);\n  let argument1 = generator.valueToCode(block, 'B', order);\n  if (!argument0 && !argument1) {\n    // If there are no arguments, then the return value is false.\n    argument0 = 'false';\n    argument1 = 'false';\n  } else {\n    // Single missing arguments have no effect on the return value.\n    const defaultArgument = operator === '&&' ? 'true' : 'false';\n    if (!argument0) {\n      argument0 = defaultArgument;\n    }\n    if (!argument1) {\n      argument1 = defaultArgument;\n    }\n  }\n  const code = argument0 + ' ' + operator + ' ' + argument1;\n  return [code, order];\n}\n\nexport function logic_negate(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Negation.\n  const order = Order.LOGICAL_NOT;\n  const argument0 = generator.valueToCode(block, 'BOOL', order) || 'true';\n  const code = '!' + argument0;\n  return [code, order];\n}\n\nexport function logic_boolean(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Boolean values true and false.\n  const code = block.getFieldValue('BOOL') === 'TRUE' ? 'true' : 'false';\n  return [code, Order.ATOMIC];\n}\n\nexport function logic_null(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Null data type.\n  return ['null', Order.ATOMIC];\n}\n\nexport function logic_ternary(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Ternary operator.\n  const value_if =\n    generator.valueToCode(block, 'IF', Order.CONDITIONAL) || 'false';\n  const value_then =\n    generator.valueToCode(block, 'THEN', Order.CONDITIONAL) || 'null';\n  const value_else =\n    generator.valueToCode(block, 'ELSE', Order.CONDITIONAL) || 'null';\n  const code = value_if + ' ? ' + value_then + ' : ' + value_else;\n  return [code, Order.CONDITIONAL];\n}\n", "/**\n * @license\n * Copyright 2012 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n/**\n * @file Generating JavaScript for loop blocks.\n */\n\n// Former goog.module ID: Blockly.JavaScript.loops\n\nimport type {ControlFlowInLoopBlock} from '../../blocks/loops.js';\nimport type {Block} from '../../core/block.js';\nimport {NameType} from '../../core/names.js';\nimport * as stringUtils from '../../core/utils/string.js';\nimport type {JavascriptGenerator} from './javascript_generator.js';\nimport {Order} from './javascript_generator.js';\n\nexport function controls_repeat_ext(\n  block: Block,\n  generator: JavascriptGenerator,\n) {\n  // Repeat n times.\n  let repeats;\n  if (block.getField('TIMES')) {\n    // Internal number.\n    repeats = String(Number(block.getFieldValue('TIMES')));\n  } else {\n    // External number.\n    repeats = generator.valueToCode(block, 'TIMES', Order.ASSIGNMENT) || '0';\n  }\n  let branch = generator.statementToCode(block, 'DO');\n  branch = generator.addLoopTrap(branch, block);\n  let code = '';\n  const loopVar = generator.nameDB_!.getDistinctName(\n    'count',\n    NameType.VARIABLE,\n  );\n  let endVar = repeats;\n  if (!repeats.match(/^\\w+$/) && !stringUtils.isNumber(repeats)) {\n    endVar = generator.nameDB_!.getDistinctName(\n      'repeat_end',\n      NameType.VARIABLE,\n    );\n    code += 'var ' + endVar + ' = ' + repeats + ';\\n';\n  }\n  code +=\n    'for (var ' +\n    loopVar +\n    ' = 0; ' +\n    loopVar +\n    ' < ' +\n    endVar +\n    '; ' +\n    loopVar +\n    '++) {\\n' +\n    branch +\n    '}\\n';\n  return code;\n}\n\nexport const controls_repeat = controls_repeat_ext;\n\nexport function controls_whileUntil(\n  block: Block,\n  generator: JavascriptGenerator,\n) {\n  // Do while/until loop.\n  const until = block.getFieldValue('MODE') === 'UNTIL';\n  let argument0 =\n    generator.valueToCode(\n      block,\n      'BOOL',\n      until ? Order.LOGICAL_NOT : Order.NONE,\n    ) || 'false';\n  let branch = generator.statementToCode(block, 'DO');\n  branch = generator.addLoopTrap(branch, block);\n  if (until) {\n    argument0 = '!' + argument0;\n  }\n  return 'while (' + argument0 + ') {\\n' + branch + '}\\n';\n}\n\nexport function controls_for(block: Block, generator: JavascriptGenerator) {\n  // For loop.\n  const variable0 = generator.getVariableName(block.getFieldValue('VAR'));\n  const argument0 =\n    generator.valueToCode(block, 'FROM', Order.ASSIGNMENT) || '0';\n  const argument1 = generator.valueToCode(block, 'TO', Order.ASSIGNMENT) || '0';\n  const increment = generator.valueToCode(block, 'BY', Order.ASSIGNMENT) || '1';\n  let branch = generator.statementToCode(block, 'DO');\n  branch = generator.addLoopTrap(branch, block);\n  let code;\n  if (\n    stringUtils.isNumber(argument0) &&\n    stringUtils.isNumber(argument1) &&\n    stringUtils.isNumber(increment)\n  ) {\n    // All arguments are simple numbers.\n    const up = Number(argument0) <= Number(argument1);\n    code =\n      'for (' +\n      variable0 +\n      ' = ' +\n      argument0 +\n      '; ' +\n      variable0 +\n      (up ? ' <= ' : ' >= ') +\n      argument1 +\n      '; ' +\n      variable0;\n    const step = Math.abs(Number(increment));\n    if (step === 1) {\n      code += up ? '++' : '--';\n    } else {\n      code += (up ? ' += ' : ' -= ') + step;\n    }\n    code += ') {\\n' + branch + '}\\n';\n  } else {\n    code = '';\n    // Cache non-trivial values to variables to prevent repeated look-ups.\n    let startVar = argument0;\n    if (!argument0.match(/^\\w+$/) && !stringUtils.isNumber(argument0)) {\n      startVar = generator.nameDB_!.getDistinctName(\n        variable0 + '_start',\n        NameType.VARIABLE,\n      );\n      code += 'var ' + startVar + ' = ' + argument0 + ';\\n';\n    }\n    let endVar = argument1;\n    if (!argument1.match(/^\\w+$/) && !stringUtils.isNumber(argument1)) {\n      endVar = generator.nameDB_!.getDistinctName(\n        variable0 + '_end',\n        NameType.VARIABLE,\n      );\n      code += 'var ' + endVar + ' = ' + argument1 + ';\\n';\n    }\n    // Determine loop direction at start, in case one of the bounds\n    // changes during loop execution.\n    const incVar = generator.nameDB_!.getDistinctName(\n      variable0 + '_inc',\n      NameType.VARIABLE,\n    );\n    code += 'var ' + incVar + ' = ';\n    if (stringUtils.isNumber(increment)) {\n      code += Math.abs(Number(increment)) + ';\\n';\n    } else {\n      code += 'Math.abs(' + increment + ');\\n';\n    }\n    code += 'if (' + startVar + ' > ' + endVar + ') {\\n';\n    code += generator.INDENT + incVar + ' = -' + incVar + ';\\n';\n    code += '}\\n';\n    code +=\n      'for (' +\n      variable0 +\n      ' = ' +\n      startVar +\n      '; ' +\n      incVar +\n      ' >= 0 ? ' +\n      variable0 +\n      ' <= ' +\n      endVar +\n      ' : ' +\n      variable0 +\n      ' >= ' +\n      endVar +\n      '; ' +\n      variable0 +\n      ' += ' +\n      incVar +\n      ') {\\n' +\n      branch +\n      '}\\n';\n  }\n  return code;\n}\n\nexport function controls_forEach(block: Block, generator: JavascriptGenerator) {\n  // For each loop.\n  const variable0 = generator.getVariableName(block.getFieldValue('VAR'));\n  const argument0 =\n    generator.valueToCode(block, 'LIST', Order.ASSIGNMENT) || '[]';\n  let branch = generator.statementToCode(block, 'DO');\n  branch = generator.addLoopTrap(branch, block);\n  let code = '';\n  // Cache non-trivial values to variables to prevent repeated look-ups.\n  let listVar = argument0;\n  if (!argument0.match(/^\\w+$/)) {\n    listVar = generator.nameDB_!.getDistinctName(\n      variable0 + '_list',\n      NameType.VARIABLE,\n    );\n    code += 'var ' + listVar + ' = ' + argument0 + ';\\n';\n  }\n  const indexVar = generator.nameDB_!.getDistinctName(\n    variable0 + '_index',\n    NameType.VARIABLE,\n  );\n  branch =\n    generator.INDENT +\n    variable0 +\n    ' = ' +\n    listVar +\n    '[' +\n    indexVar +\n    '];\\n' +\n    branch;\n  code += 'for (var ' + indexVar + ' in ' + listVar + ') {\\n' + branch + '}\\n';\n  return code;\n}\n\nexport function controls_flow_statements(\n  block: Block,\n  generator: JavascriptGenerator,\n) {\n  // Flow statements: continue, break.\n  let xfix = '';\n  if (generator.STATEMENT_PREFIX) {\n    // Automatic prefix insertion is switched off for this block.  Add manually.\n    xfix += generator.injectId(generator.STATEMENT_PREFIX, block);\n  }\n  if (generator.STATEMENT_SUFFIX) {\n    // Inject any statement suffix here since the regular one at the end\n    // will not get executed if the break/continue is triggered.\n    xfix += generator.injectId(generator.STATEMENT_SUFFIX, block);\n  }\n  if (generator.STATEMENT_PREFIX) {\n    const loop = (block as ControlFlowInLoopBlock).getSurroundLoop();\n    if (loop && !loop.suppressPrefixSuffix) {\n      // Inject loop's statement prefix here since the regular one at the end\n      // of the loop will not get executed if 'continue' is triggered.\n      // In the case of 'break', a prefix is needed due to the loop's suffix.\n      xfix += generator.injectId(generator.STATEMENT_PREFIX, loop);\n    }\n  }\n  switch (block.getFieldValue('FLOW')) {\n    case 'BREAK':\n      return xfix + 'break;\\n';\n    case 'CONTINUE':\n      return xfix + 'continue;\\n';\n  }\n  throw Error('Unknown flow statement.');\n}\n", "/**\n * @license\n * Copyright 2012 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n/**\n * @file Generating JavaScript for math blocks.\n */\n\n// Former goog.module ID: Blockly.JavaScript.math\n\nimport type {Block} from '../../core/block.js';\nimport type {JavascriptGenerator} from './javascript_generator.js';\nimport {Order} from './javascript_generator.js';\n\nexport function math_number(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Numeric value.\n  const number = Number(block.getFieldValue('NUM'));\n  const order = number >= 0 ? Order.ATOMIC : Order.UNARY_NEGATION;\n  return [String(number), order];\n}\n\nexport function math_arithmetic(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Basic arithmetic operators, and power.\n  const OPERATORS: Record<string, [string | null, Order]> = {\n    'ADD': [' + ', Order.ADDITION],\n    'MINUS': [' - ', Order.SUBTRACTION],\n    'MULTIPLY': [' * ', Order.MULTIPLICATION],\n    'DIVIDE': [' / ', Order.DIVISION],\n    'POWER': [null, Order.NONE], // Handle power separately.\n  };\n  type OperatorOption = keyof typeof OPERATORS;\n  const tuple = OPERATORS[block.getFieldValue('OP') as OperatorOption];\n  const operator = tuple[0];\n  const order = tuple[1];\n  const argument0 = generator.valueToCode(block, 'A', order) || '0';\n  const argument1 = generator.valueToCode(block, 'B', order) || '0';\n  let code;\n  // Power in JavaScript requires a special case since it has no operator.\n  if (!operator) {\n    code = 'Math.pow(' + argument0 + ', ' + argument1 + ')';\n    return [code, Order.FUNCTION_CALL];\n  }\n  code = argument0 + operator + argument1;\n  return [code, order];\n}\n\nexport function math_single(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Math operators with single operand.\n  const operator = block.getFieldValue('OP');\n  let code;\n  let arg;\n  if (operator === 'NEG') {\n    // Negation is a special case given its different operator precedence.\n    arg = generator.valueToCode(block, 'NUM', Order.UNARY_NEGATION) || '0';\n    if (arg[0] === '-') {\n      // --3 is not legal in JS.\n      arg = ' ' + arg;\n    }\n    code = '-' + arg;\n    return [code, Order.UNARY_NEGATION];\n  }\n  if (operator === 'SIN' || operator === 'COS' || operator === 'TAN') {\n    arg = generator.valueToCode(block, 'NUM', Order.DIVISION) || '0';\n  } else {\n    arg = generator.valueToCode(block, 'NUM', Order.NONE) || '0';\n  }\n  // First, handle cases which generate values that don't need parentheses\n  // wrapping the code.\n  switch (operator) {\n    case 'ABS':\n      code = 'Math.abs(' + arg + ')';\n      break;\n    case 'ROOT':\n      code = 'Math.sqrt(' + arg + ')';\n      break;\n    case 'LN':\n      code = 'Math.log(' + arg + ')';\n      break;\n    case 'EXP':\n      code = 'Math.exp(' + arg + ')';\n      break;\n    case 'POW10':\n      code = 'Math.pow(10,' + arg + ')';\n      break;\n    case 'ROUND':\n      code = 'Math.round(' + arg + ')';\n      break;\n    case 'ROUNDUP':\n      code = 'Math.ceil(' + arg + ')';\n      break;\n    case 'ROUNDDOWN':\n      code = 'Math.floor(' + arg + ')';\n      break;\n    case 'SIN':\n      code = 'Math.sin(' + arg + ' / 180 * Math.PI)';\n      break;\n    case 'COS':\n      code = 'Math.cos(' + arg + ' / 180 * Math.PI)';\n      break;\n    case 'TAN':\n      code = 'Math.tan(' + arg + ' / 180 * Math.PI)';\n      break;\n  }\n  if (code) {\n    return [code, Order.FUNCTION_CALL];\n  }\n  // Second, handle cases which generate values that may need parentheses\n  // wrapping the code.\n  switch (operator) {\n    case 'LOG10':\n      code = 'Math.log(' + arg + ') / Math.log(10)';\n      break;\n    case 'ASIN':\n      code = 'Math.asin(' + arg + ') / Math.PI * 180';\n      break;\n    case 'ACOS':\n      code = 'Math.acos(' + arg + ') / Math.PI * 180';\n      break;\n    case 'ATAN':\n      code = 'Math.atan(' + arg + ') / Math.PI * 180';\n      break;\n    default:\n      throw Error('Unknown math operator: ' + operator);\n  }\n  return [code, Order.DIVISION];\n}\n\nexport function math_constant(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Constants: PI, E, the Golden Ratio, sqrt(2), 1/sqrt(2), INFINITY.\n  const CONSTANTS: Record<string, [string, Order]> = {\n    'PI': ['Math.PI', Order.MEMBER],\n    'E': ['Math.E', Order.MEMBER],\n    'GOLDEN_RATIO': ['(1 + Math.sqrt(5)) / 2', Order.DIVISION],\n    'SQRT2': ['Math.SQRT2', Order.MEMBER],\n    'SQRT1_2': ['Math.SQRT1_2', Order.MEMBER],\n    'INFINITY': ['Infinity', Order.ATOMIC],\n  };\n  type ConstantOption = keyof typeof CONSTANTS;\n  return CONSTANTS[block.getFieldValue('CONSTANT') as ConstantOption];\n}\n\nexport function math_number_property(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Check if a number is even, odd, prime, whole, positive, or negative\n  // or if it is divisible by certain number. Returns true or false.\n  const PROPERTIES: Record<string, [string | null, Order, Order]> = {\n    'EVEN': [' % 2 === 0', Order.MODULUS, Order.EQUALITY],\n    'ODD': [' % 2 === 1', Order.MODULUS, Order.EQUALITY],\n    'WHOLE': [' % 1 === 0', Order.MODULUS, Order.EQUALITY],\n    'POSITIVE': [' > 0', Order.RELATIONAL, Order.RELATIONAL],\n    'NEGATIVE': [' < 0', Order.RELATIONAL, Order.RELATIONAL],\n    'DIVISIBLE_BY': [null, Order.MODULUS, Order.EQUALITY],\n    'PRIME': [null, Order.NONE, Order.FUNCTION_CALL],\n  };\n  type PropertyOption = keyof typeof PROPERTIES;\n  const dropdownProperty = block.getFieldValue('PROPERTY') as PropertyOption;\n  const [suffix, inputOrder, outputOrder] = PROPERTIES[dropdownProperty];\n  const numberToCheck =\n    generator.valueToCode(block, 'NUMBER_TO_CHECK', inputOrder) || '0';\n  let code;\n  if (dropdownProperty === 'PRIME') {\n    // Prime is a special case as it is not a one-liner test.\n    const functionName = generator.provideFunction_(\n      'mathIsPrime',\n      `\nfunction ${generator.FUNCTION_NAME_PLACEHOLDER_}(n) {\n  // https://en.wikipedia.org/wiki/Primality_test#Naive_methods\n  if (n == 2 || n == 3) {\n    return true;\n  }\n  // False if n is NaN, negative, is 1, or not whole.\n  // And false if n is divisible by 2 or 3.\n  if (isNaN(n) || n <= 1 || n % 1 !== 0 || n % 2 === 0 || n % 3 === 0) {\n    return false;\n  }\n  // Check all the numbers of form 6k +/- 1, up to sqrt(n).\n  for (var x = 6; x <= Math.sqrt(n) + 1; x += 6) {\n    if (n % (x - 1) === 0 || n % (x + 1) === 0) {\n      return false;\n    }\n  }\n  return true;\n}\n`,\n    );\n    code = functionName + '(' + numberToCheck + ')';\n  } else if (dropdownProperty === 'DIVISIBLE_BY') {\n    const divisor =\n      generator.valueToCode(block, 'DIVISOR', Order.MODULUS) || '0';\n    code = numberToCheck + ' % ' + divisor + ' === 0';\n  } else {\n    code = numberToCheck + suffix;\n  }\n  return [code, outputOrder];\n}\n\nexport function math_change(block: Block, generator: JavascriptGenerator) {\n  // Add to a variable in place.\n  const argument0 =\n    generator.valueToCode(block, 'DELTA', Order.ADDITION) || '0';\n  const varName = generator.getVariableName(block.getFieldValue('VAR'));\n  return (\n    varName +\n    ' = (typeof ' +\n    varName +\n    \" === 'number' ? \" +\n    varName +\n    ' : 0) + ' +\n    argument0 +\n    ';\\n'\n  );\n}\n\n// Rounding functions have a single operand.\nexport const math_round = math_single;\n// Trigonometry functions have a single operand.\nexport const math_trig = math_single;\n\nexport function math_on_list(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Math functions for lists.\n  const func = block.getFieldValue('OP');\n  let list;\n  let code;\n  switch (func) {\n    case 'SUM':\n      list = generator.valueToCode(block, 'LIST', Order.MEMBER) || '[]';\n      code = list + '.reduce(function(x, y) {return x + y;}, 0)';\n      break;\n    case 'MIN':\n      list = generator.valueToCode(block, 'LIST', Order.NONE) || '[]';\n      code = 'Math.min.apply(null, ' + list + ')';\n      break;\n    case 'MAX':\n      list = generator.valueToCode(block, 'LIST', Order.NONE) || '[]';\n      code = 'Math.max.apply(null, ' + list + ')';\n      break;\n    case 'AVERAGE': {\n      // mathMean([null,null,1,3]) === 2.0.\n      const functionName = generator.provideFunction_(\n        'mathMean',\n        `\nfunction ${generator.FUNCTION_NAME_PLACEHOLDER_}(myList) {\n  return myList.reduce(function(x, y) {return x + y;}, 0) / myList.length;\n}\n`,\n      );\n      list = generator.valueToCode(block, 'LIST', Order.NONE) || '[]';\n      code = functionName + '(' + list + ')';\n      break;\n    }\n    case 'MEDIAN': {\n      // mathMedian([null,null,1,3]) === 2.0.\n      const functionName = generator.provideFunction_(\n        'mathMedian',\n        `\nfunction ${generator.FUNCTION_NAME_PLACEHOLDER_}(myList) {\n  var localList = myList.filter(function (x) {return typeof x === 'number';});\n  if (!localList.length) return null;\n  localList.sort(function(a, b) {return b - a;});\n  if (localList.length % 2 === 0) {\n    return (localList[localList.length / 2 - 1] + localList[localList.length / 2]) / 2;\n  } else {\n    return localList[(localList.length - 1) / 2];\n  }\n}\n`,\n      );\n      list = generator.valueToCode(block, 'LIST', Order.NONE) || '[]';\n      code = functionName + '(' + list + ')';\n      break;\n    }\n    case 'MODE': {\n      // As a list of numbers can contain more than one mode,\n      // the returned result is provided as an array.\n      // Mode of [3, 'x', 'x', 1, 1, 2, '3'] -> ['x', 1].\n      const functionName = generator.provideFunction_(\n        'mathModes',\n        `\nfunction ${generator.FUNCTION_NAME_PLACEHOLDER_}(values) {\n  var modes = [];\n  var counts = [];\n  var maxCount = 0;\n  for (var i = 0; i < values.length; i++) {\n    var value = values[i];\n    var found = false;\n    var thisCount;\n    for (var j = 0; j < counts.length; j++) {\n      if (counts[j][0] === value) {\n        thisCount = ++counts[j][1];\n        found = true;\n        break;\n      }\n    }\n    if (!found) {\n      counts.push([value, 1]);\n      thisCount = 1;\n    }\n    maxCount = Math.max(thisCount, maxCount);\n  }\n  for (var j = 0; j < counts.length; j++) {\n    if (counts[j][1] === maxCount) {\n      modes.push(counts[j][0]);\n    }\n  }\n  return modes;\n}\n`,\n      );\n      list = generator.valueToCode(block, 'LIST', Order.NONE) || '[]';\n      code = functionName + '(' + list + ')';\n      break;\n    }\n    case 'STD_DEV': {\n      const functionName = generator.provideFunction_(\n        'mathStandardDeviation',\n        `\nfunction ${generator.FUNCTION_NAME_PLACEHOLDER_}(numbers) {\n  var n = numbers.length;\n  if (!n) return null;\n  var mean = numbers.reduce(function(x, y) {return x + y;}) / n;\n  var variance = 0;\n  for (var j = 0; j < n; j++) {\n    variance += Math.pow(numbers[j] - mean, 2);\n  }\n  variance /= n;\n  return Math.sqrt(variance);\n}\n`,\n      );\n      list = generator.valueToCode(block, 'LIST', Order.NONE) || '[]';\n      code = functionName + '(' + list + ')';\n      break;\n    }\n    case 'RANDOM': {\n      const functionName = generator.provideFunction_(\n        'mathRandomList',\n        `\nfunction ${generator.FUNCTION_NAME_PLACEHOLDER_}(list) {\n  var x = Math.floor(Math.random() * list.length);\n  return list[x];\n}\n`,\n      );\n      list = generator.valueToCode(block, 'LIST', Order.NONE) || '[]';\n      code = functionName + '(' + list + ')';\n      break;\n    }\n    default:\n      throw Error('Unknown operator: ' + func);\n  }\n  return [code, Order.FUNCTION_CALL];\n}\n\nexport function math_modulo(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Remainder computation.\n  const argument0 =\n    generator.valueToCode(block, 'DIVIDEND', Order.MODULUS) || '0';\n  const argument1 =\n    generator.valueToCode(block, 'DIVISOR', Order.MODULUS) || '0';\n  const code = argument0 + ' % ' + argument1;\n  return [code, Order.MODULUS];\n}\n\nexport function math_constrain(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Constrain a number between two limits.\n  const argument0 = generator.valueToCode(block, 'VALUE', Order.NONE) || '0';\n  const argument1 = generator.valueToCode(block, 'LOW', Order.NONE) || '0';\n  const argument2 =\n    generator.valueToCode(block, 'HIGH', Order.NONE) || 'Infinity';\n  const code =\n    'Math.min(Math.max(' +\n    argument0 +\n    ', ' +\n    argument1 +\n    '), ' +\n    argument2 +\n    ')';\n  return [code, Order.FUNCTION_CALL];\n}\n\nexport function math_random_int(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Random integer between [X] and [Y].\n  const argument0 = generator.valueToCode(block, 'FROM', Order.NONE) || '0';\n  const argument1 = generator.valueToCode(block, 'TO', Order.NONE) || '0';\n  const functionName = generator.provideFunction_(\n    'mathRandomInt',\n    `\nfunction ${generator.FUNCTION_NAME_PLACEHOLDER_}(a, b) {\n  if (a > b) {\n    // Swap a and b to ensure a is smaller.\n    var c = a;\n    a = b;\n    b = c;\n  }\n  return Math.floor(Math.random() * (b - a + 1) + a);\n}\n`,\n  );\n  const code = functionName + '(' + argument0 + ', ' + argument1 + ')';\n  return [code, Order.FUNCTION_CALL];\n}\n\nexport function math_random_float(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Random fraction between 0 and 1.\n  return ['Math.random()', Order.FUNCTION_CALL];\n}\n\nexport function math_atan2(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Arctangent of point (X, Y) in degrees from -180 to 180.\n  const argument0 = generator.valueToCode(block, 'X', Order.NONE) || '0';\n  const argument1 = generator.valueToCode(block, 'Y', Order.NONE) || '0';\n  return [\n    'Math.atan2(' + argument1 + ', ' + argument0 + ') / Math.PI * 180',\n    Order.DIVISION,\n  ];\n}\n", "/**\n * @license\n * Copyright 2012 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n/**\n * @file Generating JavaScript for procedure blocks.\n */\n\n// Former goog.module ID: Blockly.JavaScript.procedures\n\nimport type {IfReturnBlock} from '../../blocks/procedures.js';\nimport type {Block} from '../../core/block.js';\nimport type {JavascriptGenerator} from './javascript_generator.js';\nimport {Order} from './javascript_generator.js';\n\nexport function procedures_defreturn(\n  block: Block,\n  generator: JavascriptGenerator,\n) {\n  // Define a procedure with a return value.\n  const funcName = generator.getProcedureName(block.getFieldValue('NAME'));\n  let xfix1 = '';\n  if (generator.STATEMENT_PREFIX) {\n    xfix1 += generator.injectId(generator.STATEMENT_PREFIX, block);\n  }\n  if (generator.STATEMENT_SUFFIX) {\n    xfix1 += generator.injectId(generator.STATEMENT_SUFFIX, block);\n  }\n  if (xfix1) {\n    xfix1 = generator.prefixLines(xfix1, generator.INDENT);\n  }\n  let loopTrap = '';\n  if (generator.INFINITE_LOOP_TRAP) {\n    loopTrap = generator.prefixLines(\n      generator.injectId(generator.INFINITE_LOOP_TRAP, block),\n      generator.INDENT,\n    );\n  }\n  let branch = '';\n  if (block.getInput('STACK')) {\n    // The 'procedures_defreturn' block might not have a STACK input.\n    branch = generator.statementToCode(block, 'STACK');\n  }\n  let returnValue = '';\n  if (block.getInput('RETURN')) {\n    // The 'procedures_defnoreturn' block (which shares this code)\n    // does not have a RETURN input.\n    returnValue = generator.valueToCode(block, 'RETURN', Order.NONE) || '';\n  }\n  let xfix2 = '';\n  if (branch && returnValue) {\n    // After executing the function body, revisit this block for the return.\n    xfix2 = xfix1;\n  }\n  if (returnValue) {\n    returnValue = generator.INDENT + 'return ' + returnValue + ';\\n';\n  }\n  const args = [];\n  const variables = block.getVars();\n  for (let i = 0; i < variables.length; i++) {\n    args[i] = generator.getVariableName(variables[i]);\n  }\n  let code =\n    'function ' +\n    funcName +\n    '(' +\n    args.join(', ') +\n    ') {\\n' +\n    xfix1 +\n    loopTrap +\n    branch +\n    xfix2 +\n    returnValue +\n    '}';\n  code = generator.scrub_(block, code);\n  // Add % so as not to collide with helper functions in definitions list.\n  // TODO(#7600): find better approach than casting to any to override\n  // CodeGenerator declaring .definitions protected.\n  (generator as AnyDuringMigration).definitions_['%' + funcName] = code;\n  return null;\n}\n\n// Defining a procedure without a return value uses the same generator as\n// a procedure with a return value.\nexport const procedures_defnoreturn = procedures_defreturn;\n\nexport function procedures_callreturn(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Call a procedure with a return value.\n  const funcName = generator.getProcedureName(block.getFieldValue('NAME'));\n  const args = [];\n  const variables = block.getVars();\n  for (let i = 0; i < variables.length; i++) {\n    args[i] = generator.valueToCode(block, 'ARG' + i, Order.NONE) || 'null';\n  }\n  const code = funcName + '(' + args.join(', ') + ')';\n  return [code, Order.FUNCTION_CALL];\n}\n\nexport function procedures_callnoreturn(\n  block: Block,\n  generator: JavascriptGenerator,\n) {\n  // Call a procedure with no return value.\n  // Generated code is for a function call as a statement is the same as a\n  // function call as a value, with the addition of line ending.\n  const tuple = generator.forBlock['procedures_callreturn'](\n    block,\n    generator,\n  ) as [string, Order];\n  return tuple[0] + ';\\n';\n}\n\nexport function procedures_ifreturn(\n  block: Block,\n  generator: JavascriptGenerator,\n) {\n  // Conditionally return value from a procedure.\n  const condition =\n    generator.valueToCode(block, 'CONDITION', Order.NONE) || 'false';\n  let code = 'if (' + condition + ') {\\n';\n  if (generator.STATEMENT_SUFFIX) {\n    // Inject any statement suffix here since the regular one at the end\n    // will not get executed if the return is triggered.\n    code += generator.prefixLines(\n      generator.injectId(generator.STATEMENT_SUFFIX, block),\n      generator.INDENT,\n    );\n  }\n  if ((block as IfReturnBlock).hasReturnValue_) {\n    const value = generator.valueToCode(block, 'VALUE', Order.NONE) || 'null';\n    code += generator.INDENT + 'return ' + value + ';\\n';\n  } else {\n    code += generator.INDENT + 'return;\\n';\n  }\n  code += '}\\n';\n  return code;\n}\n", "/**\n * @license\n * Copyright 2012 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n/**\n * @file Generating JavaScript for text blocks.\n */\n\n// Former goog.module ID: Blockly.JavaScript.texts\n\nimport type {JoinMutatorBlock} from '../../blocks/text.js';\nimport type {Block} from '../../core/block.js';\nimport type {JavascriptGenerator} from './javascript_generator.js';\nimport {Order} from './javascript_generator.js';\n\n/**\n * Regular expression to detect a single-quoted string literal.\n */\nconst strRegExp = /^\\s*'([^']|\\\\')*'\\s*$/;\n\n/**\n * Enclose the provided value in 'String(...)' function.\n * Leave string literals alone.\n * @param value Code evaluating to a value.\n * @returns Array containing code evaluating to a string\n *     and the order of the returned code.[string, number]\n */\nconst forceString = function (value: string): [string, Order] {\n  if (strRegExp.test(value)) {\n    return [value, Order.ATOMIC];\n  }\n  return ['String(' + value + ')', Order.FUNCTION_CALL];\n};\n\n/**\n * Returns an expression calculating the index into a string.\n * @param stringName Name of the string, used to calculate length.\n * @param where The method of indexing, selected by dropdown in Blockly\n * @param opt_at The optional offset when indexing from start/end.\n * @returns Index expression.\n */\nconst getSubstringIndex = function (\n  stringName: string,\n  where: string,\n  opt_at?: string,\n): string | undefined {\n  if (where === 'FIRST') {\n    return '0';\n  } else if (where === 'FROM_END') {\n    return stringName + '.length - 1 - ' + opt_at;\n  } else if (where === 'LAST') {\n    return stringName + '.length - 1';\n  } else {\n    return opt_at;\n  }\n};\n\nexport function text(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Text value.\n  const code = generator.quote_(block.getFieldValue('TEXT'));\n  return [code, Order.ATOMIC];\n}\n\nexport function text_join(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Create a string made up of any number of elements of any type.\n  const joinBlock = block as JoinMutatorBlock;\n  switch (joinBlock.itemCount_) {\n    case 0:\n      return [\"''\", Order.ATOMIC];\n    case 1: {\n      const element =\n        generator.valueToCode(joinBlock, 'ADD0', Order.NONE) || \"''\";\n      const codeAndOrder = forceString(element);\n      return codeAndOrder;\n    }\n    case 2: {\n      const element0 =\n        generator.valueToCode(joinBlock, 'ADD0', Order.NONE) || \"''\";\n      const element1 =\n        generator.valueToCode(joinBlock, 'ADD1', Order.NONE) || \"''\";\n      const code = forceString(element0)[0] + ' + ' + forceString(element1)[0];\n      return [code, Order.ADDITION];\n    }\n    default: {\n      const elements = new Array(joinBlock.itemCount_);\n      for (let i = 0; i < joinBlock.itemCount_; i++) {\n        elements[i] =\n          generator.valueToCode(joinBlock, 'ADD' + i, Order.NONE) || \"''\";\n      }\n      const code = '[' + elements.join(',') + \"].join('')\";\n      return [code, Order.FUNCTION_CALL];\n    }\n  }\n}\n\nexport function text_append(block: Block, generator: JavascriptGenerator) {\n  // Append to a variable in place.\n  const varName = generator.getVariableName(block.getFieldValue('VAR'));\n  const value = generator.valueToCode(block, 'TEXT', Order.NONE) || \"''\";\n  const code = varName + ' += ' + forceString(value)[0] + ';\\n';\n  return code;\n}\n\nexport function text_length(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // String or array length.\n  const text = generator.valueToCode(block, 'VALUE', Order.MEMBER) || \"''\";\n  return [text + '.length', Order.MEMBER];\n}\n\nexport function text_isEmpty(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Is the string null or array empty?\n  const text = generator.valueToCode(block, 'VALUE', Order.MEMBER) || \"''\";\n  return ['!' + text + '.length', Order.LOGICAL_NOT];\n}\n\nexport function text_indexOf(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Search the text for a substring.\n  const operator =\n    block.getFieldValue('END') === 'FIRST' ? 'indexOf' : 'lastIndexOf';\n  const substring = generator.valueToCode(block, 'FIND', Order.NONE) || \"''\";\n  const text = generator.valueToCode(block, 'VALUE', Order.MEMBER) || \"''\";\n  const code = text + '.' + operator + '(' + substring + ')';\n  // Adjust index if using one-based indices.\n  if (block.workspace.options.oneBasedIndex) {\n    return [code + ' + 1', Order.ADDITION];\n  }\n  return [code, Order.FUNCTION_CALL];\n}\n\nexport function text_charAt(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Get letter at index.\n  // Note: Until January 2013 this block did not have the WHERE input.\n  const where = block.getFieldValue('WHERE') || 'FROM_START';\n  const textOrder = where === 'RANDOM' ? Order.NONE : Order.MEMBER;\n  const text = generator.valueToCode(block, 'VALUE', textOrder) || \"''\";\n  switch (where) {\n    case 'FIRST': {\n      const code = text + '.charAt(0)';\n      return [code, Order.FUNCTION_CALL];\n    }\n    case 'LAST': {\n      const code = text + '.slice(-1)';\n      return [code, Order.FUNCTION_CALL];\n    }\n    case 'FROM_START': {\n      const at = generator.getAdjusted(block, 'AT');\n      // Adjust index if using one-based indices.\n      const code = text + '.charAt(' + at + ')';\n      return [code, Order.FUNCTION_CALL];\n    }\n    case 'FROM_END': {\n      const at = generator.getAdjusted(block, 'AT', 1, true);\n      const code = text + '.slice(' + at + ').charAt(0)';\n      return [code, Order.FUNCTION_CALL];\n    }\n    case 'RANDOM': {\n      const functionName = generator.provideFunction_(\n        'textRandomLetter',\n        `\nfunction ${generator.FUNCTION_NAME_PLACEHOLDER_}(text) {\n  var x = Math.floor(Math.random() * text.length);\n  return text[x];\n}\n`,\n      );\n      const code = functionName + '(' + text + ')';\n      return [code, Order.FUNCTION_CALL];\n    }\n  }\n  throw Error('Unhandled option (text_charAt).');\n}\n\nexport function text_getSubstring(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Dictionary of WHEREn field choices and their CamelCase equivalents. */\n  const wherePascalCase = {\n    'FIRST': 'First',\n    'LAST': 'Last',\n    'FROM_START': 'FromStart',\n    'FROM_END': 'FromEnd',\n  };\n  type WhereOption = keyof typeof wherePascalCase;\n  // Get substring.\n  const where1 = block.getFieldValue('WHERE1') as WhereOption;\n  const where2 = block.getFieldValue('WHERE2') as WhereOption;\n  const requiresLengthCall =\n    where1 !== 'FROM_END' &&\n    where1 !== 'LAST' &&\n    where2 !== 'FROM_END' &&\n    where2 !== 'LAST';\n  const textOrder = requiresLengthCall ? Order.MEMBER : Order.NONE;\n  const text = generator.valueToCode(block, 'STRING', textOrder) || \"''\";\n  let code;\n  if (where1 === 'FIRST' && where2 === 'LAST') {\n    code = text;\n    return [code, Order.NONE];\n  } else if (text.match(/^'?\\w+'?$/) || requiresLengthCall) {\n    // If the text is a variable or literal or doesn't require a call for\n    // length, don't generate a helper function.\n    let at1;\n    switch (where1) {\n      case 'FROM_START':\n        at1 = generator.getAdjusted(block, 'AT1');\n        break;\n      case 'FROM_END':\n        at1 = generator.getAdjusted(block, 'AT1', 1, false, Order.SUBTRACTION);\n        at1 = text + '.length - ' + at1;\n        break;\n      case 'FIRST':\n        at1 = '0';\n        break;\n      default:\n        throw Error('Unhandled option (text_getSubstring).');\n    }\n    let at2;\n    switch (where2) {\n      case 'FROM_START':\n        at2 = generator.getAdjusted(block, 'AT2', 1);\n        break;\n      case 'FROM_END':\n        at2 = generator.getAdjusted(block, 'AT2', 0, false, Order.SUBTRACTION);\n        at2 = text + '.length - ' + at2;\n        break;\n      case 'LAST':\n        at2 = text + '.length';\n        break;\n      default:\n        throw Error('Unhandled option (text_getSubstring).');\n    }\n    code = text + '.slice(' + at1 + ', ' + at2 + ')';\n  } else {\n    const at1 = generator.getAdjusted(block, 'AT1');\n    const at2 = generator.getAdjusted(block, 'AT2');\n    // The value for 'FROM_END' and'FROM_START' depends on `at` so\n    // we add it as a parameter.\n    const at1Param =\n      where1 === 'FROM_END' || where1 === 'FROM_START' ? ', at1' : '';\n    const at2Param =\n      where2 === 'FROM_END' || where2 === 'FROM_START' ? ', at2' : '';\n    const functionName = generator.provideFunction_(\n      'subsequence' + wherePascalCase[where1] + wherePascalCase[where2],\n      `\nfunction ${\n        generator.FUNCTION_NAME_PLACEHOLDER_\n      }(sequence${at1Param}${at2Param}) {\n  var start = ${getSubstringIndex('sequence', where1, 'at1')};\n  var end = ${getSubstringIndex('sequence', where2, 'at2')} + 1;\n  return sequence.slice(start, end);\n}\n`,\n    );\n    code =\n      functionName +\n      '(' +\n      text +\n      // The value for 'FROM_END' and 'FROM_START' depends on `at` so we\n      // pass it.\n      (where1 === 'FROM_END' || where1 === 'FROM_START' ? ', ' + at1 : '') +\n      (where2 === 'FROM_END' || where2 === 'FROM_START' ? ', ' + at2 : '') +\n      ')';\n  }\n  return [code, Order.FUNCTION_CALL];\n}\n\nexport function text_changeCase(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Change capitalization.\n  const OPERATORS = {\n    'UPPERCASE': '.toUpperCase()',\n    'LOWERCASE': '.toLowerCase()',\n    'TITLECASE': null,\n  };\n  type OperatorOption = keyof typeof OPERATORS;\n  const operator = OPERATORS[block.getFieldValue('CASE') as OperatorOption];\n  const textOrder = operator ? Order.MEMBER : Order.NONE;\n  const text = generator.valueToCode(block, 'TEXT', textOrder) || \"''\";\n  let code;\n  if (operator) {\n    // Upper and lower case are functions built into generator.\n    code = text + operator;\n  } else {\n    // Title case is not a native JavaScript function.  Define one.\n    const functionName = generator.provideFunction_(\n      'textToTitleCase',\n      `\nfunction ${generator.FUNCTION_NAME_PLACEHOLDER_}(str) {\n  return str.replace(/\\\\S+/g,\n      function(txt) {return txt[0].toUpperCase() + txt.substring(1).toLowerCase();});\n}\n`,\n    );\n    code = functionName + '(' + text + ')';\n  }\n  return [code, Order.FUNCTION_CALL];\n}\n\nexport function text_trim(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Trim spaces.\n  const OPERATORS = {\n    'LEFT': \".replace(/^[\\\\s\\\\xa0]+/, '')\",\n    'RIGHT': \".replace(/[\\\\s\\\\xa0]+$/, '')\",\n    'BOTH': '.trim()',\n  };\n  type OperatorOption = keyof typeof OPERATORS;\n  const operator = OPERATORS[block.getFieldValue('MODE') as OperatorOption];\n  const text = generator.valueToCode(block, 'TEXT', Order.MEMBER) || \"''\";\n  return [text + operator, Order.FUNCTION_CALL];\n}\n\nexport function text_print(block: Block, generator: JavascriptGenerator) {\n  // Print statement.\n  const msg = generator.valueToCode(block, 'TEXT', Order.NONE) || \"''\";\n  return 'window.alert(' + msg + ');\\n';\n}\n\nexport function text_prompt_ext(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Prompt function.\n  let msg;\n  if (block.getField('TEXT')) {\n    // Internal message.\n    msg = generator.quote_(block.getFieldValue('TEXT'));\n  } else {\n    // External message.\n    msg = generator.valueToCode(block, 'TEXT', Order.NONE) || \"''\";\n  }\n  let code = 'window.prompt(' + msg + ')';\n  const toNumber = block.getFieldValue('TYPE') === 'NUMBER';\n  if (toNumber) {\n    code = 'Number(' + code + ')';\n  }\n  return [code, Order.FUNCTION_CALL];\n}\n\nexport const text_prompt = text_prompt_ext;\n\nexport function text_count(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  const text = generator.valueToCode(block, 'TEXT', Order.NONE) || \"''\";\n  const sub = generator.valueToCode(block, 'SUB', Order.NONE) || \"''\";\n  const functionName = generator.provideFunction_(\n    'textCount',\n    `\nfunction ${generator.FUNCTION_NAME_PLACEHOLDER_}(haystack, needle) {\n  if (needle.length === 0) {\n    return haystack.length + 1;\n  } else {\n    return haystack.split(needle).length - 1;\n  }\n}\n`,\n  );\n  const code = functionName + '(' + text + ', ' + sub + ')';\n  return [code, Order.FUNCTION_CALL];\n}\n\nexport function text_replace(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  const text = generator.valueToCode(block, 'TEXT', Order.NONE) || \"''\";\n  const from = generator.valueToCode(block, 'FROM', Order.NONE) || \"''\";\n  const to = generator.valueToCode(block, 'TO', Order.NONE) || \"''\";\n  // The regex escaping code below is taken from the implementation of\n  // goog.string.regExpEscape.\n  const functionName = generator.provideFunction_(\n    'textReplace',\n    `\nfunction ${generator.FUNCTION_NAME_PLACEHOLDER_}(haystack, needle, replacement) {\n  needle = needle.replace(/([-()\\\\[\\\\]{}+?*.$\\\\^|,:#<!\\\\\\\\])/g, '\\\\\\\\$1')\n                 .replace(/\\\\x08/g, '\\\\\\\\x08');\n  return haystack.replace(new RegExp(needle, 'g'), replacement);\n}\n`,\n  );\n  const code = functionName + '(' + text + ', ' + from + ', ' + to + ')';\n  return [code, Order.FUNCTION_CALL];\n}\n\nexport function text_reverse(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  const text = generator.valueToCode(block, 'TEXT', Order.MEMBER) || \"''\";\n  const code = text + \".split('').reverse().join('')\";\n  return [code, Order.FUNCTION_CALL];\n}\n", "/**\n * @license\n * Copyright 2012 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n/**\n * @file Generating JavaScript for variable blocks.\n */\n\n// Former goog.module ID: Blockly.JavaScript.variables\n\nimport type {Block} from '../../core/block.js';\nimport type {JavascriptGenerator} from './javascript_generator.js';\nimport {Order} from './javascript_generator.js';\n\nexport function variables_get(\n  block: Block,\n  generator: JavascriptGenerator,\n): [string, Order] {\n  // Variable getter.\n  const code = generator.getVariableName(block.getFieldValue('VAR'));\n  return [code, Order.ATOMIC];\n}\n\nexport function variables_set(block: Block, generator: JavascriptGenerator) {\n  // Variable setter.\n  const argument0 =\n    generator.valueToCode(block, 'VALUE', Order.ASSIGNMENT) || '0';\n  const varName = generator.getVariableName(block.getFieldValue('VAR'));\n  return varName + ' = ' + argument0 + ';\\n';\n}\n", "/**\n * @license\n * Copyright 2012 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n/**\n * @file JavaScript code generator class, including helper methods for\n * generating JavaScript for blocks.\n */\n\n// Former goog.module ID: Blockly.JavaScript\n\nimport type {Block} from '../../core/block.js';\nimport {CodeGenerator} from '../../core/generator.js';\nimport {inputTypes} from '../../core/inputs/input_types.js';\nimport {Names, NameType} from '../../core/names.js';\nimport * as stringUtils from '../../core/utils/string.js';\nimport * as Variables from '../../core/variables.js';\nimport type {Workspace} from '../../core/workspace.js';\n\n/**\n * Order of operation ENUMs.\n * https://developer.mozilla.org/en/JavaScript/Reference/Operators/Operator_Precedence\n */\n// prettier-ignore\nexport enum Order {\n  ATOMIC = 0,            // 0 \"\" ...\n  NEW = 1.1,             // new\n  MEMBER = 1.2,          // . []\n  FUNCTION_CALL = 2,     // ()\n  INCREMENT = 3,         // ++\n  DECREMENT = 3,         // --\n  BITWISE_NOT = 4.1,     // ~\n  UNARY_PLUS = 4.2,      // +\n  UNARY_NEGATION = 4.3,  // -\n  LOGICAL_NOT = 4.4,     // !\n  TYPEOF = 4.5,          // typeof\n  VOID = 4.6,            // void\n  DELETE = 4.7,          // delete\n  AWAIT = 4.8,           // await\n  EXPONENTIATION = 5.0,  // **\n  MULTIPLICATION = 5.1,  // *\n  DIVISION = 5.2,        // /\n  MODULUS = 5.3,         // %\n  SUBTRACTION = 6.1,     // -\n  ADDITION = 6.2,        // +\n  BITWISE_SHIFT = 7,     // << >> >>>\n  RELATIONAL = 8,        // < <= > >=\n  IN = 8,                // in\n  INSTANCEOF = 8,        // instanceof\n  EQUALITY = 9,          // == != === !==\n  BITWISE_AND = 10,      // &\n  BITWISE_XOR = 11,      // ^\n  BITWISE_OR = 12,       // |\n  LOGICAL_AND = 13,      // &&\n  LOGICAL_OR = 14,       // ||\n  CONDITIONAL = 15,      // ?:\n  ASSIGNMENT = 16,       // = += -= **= *= /= %= <<= >>= ...\n  YIELD = 17,            // yield\n  COMMA = 18,            // ,\n  NONE = 99,             // (...)\n}\n\n/**\n * JavaScript code generator class.\n */\nexport class JavascriptGenerator extends CodeGenerator {\n  /** List of outer-inner pairings that do NOT require parentheses. */\n  ORDER_OVERRIDES: [Order, Order][] = [\n    // (foo()).bar -> foo().bar\n    // (foo())[0] -> foo()[0]\n    [Order.FUNCTION_CALL, Order.MEMBER],\n    // (foo())() -> foo()()\n    [Order.FUNCTION_CALL, Order.FUNCTION_CALL],\n    // (foo.bar).baz -> foo.bar.baz\n    // (foo.bar)[0] -> foo.bar[0]\n    // (foo[0]).bar -> foo[0].bar\n    // (foo[0])[1] -> foo[0][1]\n    [Order.MEMBER, Order.MEMBER],\n    // (foo.bar)() -> foo.bar()\n    // (foo[0])() -> foo[0]()\n    [Order.MEMBER, Order.FUNCTION_CALL],\n\n    // !(!foo) -> !!foo\n    [Order.LOGICAL_NOT, Order.LOGICAL_NOT],\n    // a * (b * c) -> a * b * c\n    [Order.MULTIPLICATION, Order.MULTIPLICATION],\n    // a + (b + c) -> a + b + c\n    [Order.ADDITION, Order.ADDITION],\n    // a && (b && c) -> a && b && c\n    [Order.LOGICAL_AND, Order.LOGICAL_AND],\n    // a || (b || c) -> a || b || c\n    [Order.LOGICAL_OR, Order.LOGICAL_OR],\n  ];\n\n  /** @param name Name of the language the generator is for. */\n  constructor(name = 'JavaScript') {\n    super(name);\n    this.isInitialized = false;\n\n    // Copy Order values onto instance for backwards compatibility\n    // while ensuring they are not part of the publically-advertised\n    // API.\n    //\n    // TODO(#7085): deprecate these in due course.  (Could initially\n    // replace data properties with get accessors that call\n    // deprecate.warn().)\n    for (const key in Order) {\n      // Must assign Order[key] to a temporary to get the type guard to work;\n      // see https://github.com/microsoft/TypeScript/issues/10530.\n      const value = Order[key];\n      // Skip reverse-lookup entries in the enum.  Due to\n      // https://github.com/microsoft/TypeScript/issues/55713 this (as\n      // of TypeScript 5.5.2) actually narrows the type of value to\n      // never - but that still allows the following assignment to\n      // succeed.\n      if (typeof value === 'string') continue;\n      (this as unknown as Record<string, Order>)['ORDER_' + key] = value;\n    }\n\n    // List of illegal variable names.  This is not intended to be a\n    // security feature.  Blockly is 100% client-side, so bypassing\n    // this list is trivial.  This is intended to prevent users from\n    // accidentally clobbering a built-in object or function.\n    //\n    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Lexical_grammar#Keywords\n    this.addReservedWords(\n      'break,case,catch,class,const,continue,debugger,default,delete,do,' +\n        'else,export,extends,finally,for,function,if,import,in,instanceof,' +\n        'new,return,super,switch,this,throw,try,typeof,var,void,' +\n        'while,with,yield,' +\n        'enum,' +\n        'implements,interface,let,package,private,protected,public,static,' +\n        'await,' +\n        'null,true,false,' +\n        // Magic variable.\n        'arguments,' +\n        // Everything in the current environment (835 items in Chrome,\n        // 104 in Node).\n        Object.getOwnPropertyNames(globalThis).join(','),\n    );\n  }\n\n  /**\n   * Initialise the database of variable names.\n   *\n   * @param workspace Workspace to generate code from.\n   */\n  init(workspace: Workspace) {\n    super.init(workspace);\n\n    if (!this.nameDB_) {\n      this.nameDB_ = new Names(this.RESERVED_WORDS_);\n    } else {\n      this.nameDB_.reset();\n    }\n\n    this.nameDB_.setVariableMap(workspace.getVariableMap());\n    this.nameDB_.populateVariables(workspace);\n    this.nameDB_.populateProcedures(workspace);\n\n    const defvars = [];\n    // Add developer variables (not created or named by the user).\n    const devVarList = Variables.allDeveloperVariables(workspace);\n    for (let i = 0; i < devVarList.length; i++) {\n      defvars.push(\n        this.nameDB_.getName(devVarList[i], NameType.DEVELOPER_VARIABLE),\n      );\n    }\n\n    // Add user variables, but only ones that are being used.\n    const variables = Variables.allUsedVarModels(workspace);\n    for (let i = 0; i < variables.length; i++) {\n      defvars.push(\n        this.nameDB_.getName(variables[i].getId(), NameType.VARIABLE),\n      );\n    }\n\n    // Declare all of the variables.\n    if (defvars.length) {\n      this.definitions_['variables'] = 'var ' + defvars.join(', ') + ';';\n    }\n    this.isInitialized = true;\n  }\n\n  /**\n   * Prepend the generated code with the variable definitions.\n   *\n   * @param code Generated code.\n   * @returns Completed code.\n   */\n  finish(code: string): string {\n    // Convert the definitions dictionary into a list.\n    const definitions = Object.values(this.definitions_);\n    // Call Blockly.CodeGenerator's finish.\n    super.finish(code);\n    this.isInitialized = false;\n\n    this.nameDB_!.reset();\n    return definitions.join('\\n\\n') + '\\n\\n\\n' + code;\n  }\n\n  /**\n   * Naked values are top-level blocks with outputs that aren't plugged into\n   * anything.  A trailing semicolon is needed to make this legal.\n   *\n   * @param line Line of generated code.\n   * @returns Legal line of code.\n   */\n  scrubNakedValue(line: string): string {\n    return line + ';\\n';\n  }\n\n  /**\n   * Encode a string as a properly escaped JavaScript string, complete with\n   * quotes.\n   *\n   * @param string Text to encode.\n   * @returns JavaScript string.\n   */\n  quote_(string: string): string {\n    // Can't use goog.string.quote since Google's style guide recommends\n    // JS string literals use single quotes.\n    string = string\n      .replace(/\\\\/g, '\\\\\\\\')\n      .replace(/\\n/g, '\\\\\\n')\n      .replace(/'/g, \"\\\\'\");\n    return \"'\" + string + \"'\";\n  }\n\n  /**\n   * Encode a string as a properly escaped multiline JavaScript string, complete\n   * with quotes.\n   * @param string Text to encode.\n   * @returns JavaScript string.\n   */\n  multiline_quote_(string: string): string {\n    // Can't use goog.string.quote since Google's style guide recommends\n    // JS string literals use single quotes.\n    const lines = string.split(/\\n/g).map(this.quote_);\n    return lines.join(\" + '\\\\n' +\\n\");\n  }\n\n  /**\n   * Common tasks for generating JavaScript from blocks.\n   * Handles comments for the specified block and any connected value blocks.\n   * Calls any statements following this block.\n   *\n   * @param block The current block.\n   * @param code The JavaScript code created for this block.\n   * @param thisOnly True to generate code for only this statement.\n   * @returns JavaScript code with comments and subsequent blocks added.\n   */\n  scrub_(block: Block, code: string, thisOnly = false): string {\n    let commentCode = '';\n    // Only collect comments for blocks that aren't inline.\n    if (!block.outputConnection || !block.outputConnection.targetConnection) {\n      // Collect comment for this block.\n      let comment = block.getCommentText();\n      if (comment) {\n        comment = stringUtils.wrap(comment, this.COMMENT_WRAP - 3);\n        commentCode += this.prefixLines(comment + '\\n', '// ');\n      }\n      // Collect comments for all value arguments.\n      // Don't collect comments for nested statements.\n      for (let i = 0; i < block.inputList.length; i++) {\n        if (block.inputList[i].type === inputTypes.VALUE) {\n          const childBlock = block.inputList[i].connection!.targetBlock();\n          if (childBlock) {\n            comment = this.allNestedComments(childBlock);\n            if (comment) {\n              commentCode += this.prefixLines(comment, '// ');\n            }\n          }\n        }\n      }\n    }\n    const nextBlock =\n      block.nextConnection && block.nextConnection.targetBlock();\n    const nextCode = thisOnly ? '' : this.blockToCode(nextBlock);\n    return commentCode + code + nextCode;\n  }\n\n  /**\n   * Generate code representing the specified value input, adjusted to take into\n   * account indexing (zero- or one-based) and optionally by a specified delta\n   * and/or by negation.\n   *\n   * @param block The block.\n   * @param atId The ID of the input block to get (and adjust) the value of.\n   * @param delta Value to add.\n   * @param negate Whether to negate the value.\n   * @param order The highest order acting on this value.\n   * @returns The adjusted value or code that evaluates to it.\n   */\n  getAdjusted(\n    block: Block,\n    atId: string,\n    delta = 0,\n    negate = false,\n    order = Order.NONE,\n  ): string {\n    if (block.workspace.options.oneBasedIndex) {\n      delta--;\n    }\n    const defaultAtIndex = block.workspace.options.oneBasedIndex ? '1' : '0';\n\n    let orderForInput = order;\n    if (delta > 0) {\n      orderForInput = Order.ADDITION;\n    } else if (delta < 0) {\n      orderForInput = Order.SUBTRACTION;\n    } else if (negate) {\n      orderForInput = Order.UNARY_NEGATION;\n    }\n\n    let at = this.valueToCode(block, atId, orderForInput) || defaultAtIndex;\n\n    // Easy case: no adjustments.\n    if (delta === 0 && !negate) {\n      return at;\n    }\n    // If the index is a naked number, adjust it right now.\n    if (stringUtils.isNumber(at)) {\n      at = String(Number(at) + delta);\n      if (negate) {\n        at = String(-Number(at));\n      }\n      return at;\n    }\n    // If the index is dynamic, adjust it in code.\n    if (delta > 0) {\n      at = `${at} + ${delta}`;\n    } else if (delta < 0) {\n      at = `${at} - ${-delta}`;\n    }\n    if (negate) {\n      at = delta ? `-(${at})` : `-${at}`;\n    }\n    if (Math.floor(order) >= Math.floor(orderForInput)) {\n      at = `(${at})`;\n    }\n    return at;\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n/**\n * @file Generating JavaScript for dynamic variable blocks.\n */\n\n// Former goog.module ID: Blockly.JavaScript.variablesDynamic\n\n// JavaScript is dynamically typed.\nexport {\n  variables_get as variables_get_dynamic,\n  variables_set as variables_set_dynamic,\n} from './variables.js';\n", "/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n/**\n * @file Instantiate a JavascriptGenerator and populate it with the\n * complete set of block generator functions for JavaScript.  This is\n * the entrypoint for javascript_compressed.js.\n */\n\n// Former goog.module ID: Blockly.JavaScript.all\n\nimport {JavascriptGenerator} from './javascript/javascript_generator.js';\nimport * as lists from './javascript/lists.js';\nimport * as logic from './javascript/logic.js';\nimport * as loops from './javascript/loops.js';\nimport * as math from './javascript/math.js';\nimport * as procedures from './javascript/procedures.js';\nimport * as text from './javascript/text.js';\nimport * as variables from './javascript/variables.js';\nimport * as variablesDynamic from './javascript/variables_dynamic.js';\n\nexport * from './javascript/javascript_generator.js';\n\n/**\n * JavaScript code generator instance.\n * @type {!JavascriptGenerator}\n */\nexport const javascriptGenerator = new JavascriptGenerator();\n\n// Install per-block-type generator functions:\nconst generators: typeof javascriptGenerator.forBlock = {\n  ...lists,\n  ...logic,\n  ...loops,\n  ...math,\n  ...procedures,\n  ...text,\n  ...variables,\n  ...variablesDynamic,\n};\nfor (const name in generators) {\n  javascriptGenerator.forBlock[name] = generators[name];\n}\n", "import javascript from './javascript_compressed.js';\nexport const {\n  JavascriptGenerator,\n  Order,\n  javascriptGenerator,\n} = javascript;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAkBMA,UAAAA,mEAAAA,SACJC,GACAC,GAA8BF;AAG9BA,eAAOA,CAACA,MAAMG,mEAAMC,MAAbJ;MAHuBA,GAM1BK,kEAAAA,SACJJ,GACAC,GAA8BG;AAI9BA,cAAMC,IAAeC,MADGN,EACmBO,UAA1BH;AACjBA,iBAASI,IAAIJ,GAAGI,IAFQR,EAEYO,YAAYC,IAC9CH,GAASG,CAATJ,IAAcH,EAAUQ,YAAYT,GAAOI,QAAQI,GAAGN,mEAAMQ,IAA9CN,KAAuDA;AAGvEA,eAAOA,CADMA,MAAMC,EAASM,KAAKP,IAAdA,IAAsBA,KAC3BF,mEAAMC,MAAbC;MATuBA,GAY1BQ,6DAAAA,SACJZ,GACAC,GAA8BW;AAG9BA,cAAMC,IAAeZ,EAAUa,iBAC7BF,eACAA;WACOX,EAAUc,0BADjBH;;;;;;;CAFmBA,GAYfI,IAAUf,EAAUQ,YAAYT,GAAOY,QAAQV,mEAAMQ,IAA3CE,KAAoDA;AAC9DK,YAAchB,EAAUQ,YAAYT,GAAOY,OAAOV,mEAAMQ,IAA1CE,KAAmDA;AAEvEA,eAAOA,CADMC,IAAeD,MAAMI,IAAUJ,OAAOK,IAAcL,KACnDV,mEAAMgB,aAAbN;MAlBuBA,GAqB1BO,6DAAAA,SACJnB,GACAC,GAA8BkB;AAI9BA,eAAOA,EADMlB,EAAUQ,YAAYT,GAAOmB,SAASjB,mEAAMkB,MAA5CD,KAAuDA,QACrDA,WAAWjB,mEAAMkB,MAAzBD;MAJuBA,GAO1BE,8DAAAA,SACJrB,GACAC,GAA8BoB;AAI9BA,eAAOA,CAACA,OADKpB,EAAUQ,YAAYT,GAAOqB,SAASnB,mEAAMkB,MAA5CC,KAAuDA,QAC/CA,WAAWnB,mEAAMoB,WAA/BD;MAJuBA,GAO1BE,8DAAAA,SACJvB,GACAC,GAA8BsB;AAG9BA,cAAMC,IAC2BD,YAA/BvB,EAAMyB,cAAcF,KAApBA,IAAyCA,YAAYA,eACjDG,IAAOzB,EAAUQ,YAAYT,GAAOuB,QAAQrB,mEAAMQ,IAA3Ca,KAAoDA;AAE3DI,aADO1B,EAAUQ,YAAYT,GAAOuB,SAASrB,mEAAMkB,MAA5CG,KAAuDA,QAChDA,MAAMC,IAAWD,MAAMG,IAAOH;AAClDA,eAAIvB,EAAM4B,UAAUC,QAAQC,gBACnBP,CAACI,IAAOJ,QAAQrB,mEAAM6B,QAAtBR,IAEFA,CAACI,GAAMzB,mEAAMgB,aAAbK;MAXuBA,GAc1BS,+DAAAA,SACJhC,GACAC,GAA8B+B;AAI9BA,cAAMC,IAAOjC,EAAMyB,cAAcO,MAApBA,KAA+BA,OACtCE,IAAQlC,EAAMyB,cAAcO,OAApBA,KAAgCA;AAE9CA,YAAMG,IAAOlC,EAAUQ,YAAYT,GAAOgC,SADdA,aAAVE,IAAqBhC,mEAAMQ,OAAOR,mEAAMkB,MAC7CY,KAAoDA;AAEjEA,gBAAQE,GAARF;UACEA,KAAKA;AACHA,gBAAaA,UAATC,EAEFD,QAAOA,CADMG,IAAOH,OACN9B,mEAAMkB,MAAbY;AACFA,gBAAaA,iBAATC,EAETD,QAAOA,CADMG,IAAOH,YACN9B,mEAAMkB,MAAbY;gBACWA,aAATC,EACTD,QAAOG,IAAOH;AAEhBA;UACFA,KAAKA;AACHA,gBAAaA,UAATC,EAEFD,QAAOA,CADMG,IAAOH,iBACN9B,mEAAMkB,MAAbY;AACFA,gBAAaA,iBAATC,EAETD,QAAOA,CADMG,IAAOH,UACN9B,mEAAMkB,MAAbY;AACFA,gBAAaA,aAATC,EACTD,QAAOG,IAAOH;AAEhBA;UACFA,KAAKA;AACGI,gBAAKnC,EAAUoC,YAAYrC,GAAOgC,IAA7BA;AACXA,gBAAaA,UAATC,EAEFD,QAAOA,CADMG,IAAOH,MAAMI,IAAKJ,KACjB9B,mEAAMkB,MAAbY;AACFA,gBAAaA,iBAATC,EAETD,QAAOA,CADMG,IAAOH,aAAaI,IAAKJ,WACxB9B,mEAAMgB,aAAbc;AACFA,gBAAaA,aAATC,EACTD,QAAOG,IAAOH,aAAaI,IAAKJ;AAElCA;UAEFA,KAAKA;AACGI,gBAAKnC,EAAUoC,YAAYrC,GAAOgC,MAAMA,GAAGA,IAAtCA;AACXA,gBAAaA,UAATC,EAEFD,QAAOA,CADMG,IAAOH,YAAYI,IAAKJ,QACvB9B,mEAAMgB,aAAbc;AACFA,gBAAaA,iBAATC,EAETD,QAAOA,CADMG,IAAOH,aAAaI,IAAKJ,WACxB9B,mEAAMgB,aAAbc;AACFA,gBAAaA,aAATC,EACTD,QAAOG,IAAOH,aAAaI,IAAKJ;;UAIpCA,KAAKA;AAcGL,gBAbe1B,EAAUa,iBAC7BkB,sBACAA;WACG/B,EAAUc,0BADbiB;;;;;;;;CAFmBnB,IAaOmB,MAAMG,IAAOH,QAAiBA,UAATC,KAAkBD;AACnEA,gBAAaA,UAATC,KAA2BD,iBAATC,EACpBD,QAAOA,CAACL,GAAMzB,mEAAMgB,aAAbc;AACFA,gBAAaA,aAATC,EACTD,QAAOL,IAAOK;QAnEpBA;AAwEAA,cAAMM,MAAMN,yCAANA;MAjFwBA,GAoF1BO,+DAAAA,SAAyBvC,GAAcC,GAA8BsC;AASzEC,iBAASA,IAASD;AAChBA,cAAIJ,EAAKM,MAAMF,OAAXA,EACFA,QAAOA;AAETA,gBAAMG,IAAUzC,EAAU0C,QAASC,gBACjCL,WACAM,EAAAA,sCAASC,QAFKP,GAIVZ,IAAOY,SAASG,IAAUH,QAAQJ,IAAOI;AAC/CJ,cAAOO;AACPH,iBAAOZ;QAVSY;AANlBA,YAAIJ,IAAOlC,EAAUQ;UAAYT;UAAOuC;UAAQrC,mEAAMkB;QAA3CmB,KAAsDA;AACjEA,cAAMN,IAAOjC,EAAMyB,cAAcc,MAApBA,KAA+BA;AAC5CA,YAAML,IAAQlC,EAAMyB,cAAcc,OAApBA,KAAgCA;AAC9CA,cAAMQ,IAAQ9C,EAAUQ,YAAYT,GAAOuC,MAAMrC,mEAAM8C,UAAzCT,KAAwDA;AAetEA,gBAAQL,GAARK;UACEA,KAAKA;AACHA,gBAAaA,UAATN,EACFM,QAAOJ,IAAOI,WAAWQ,IAAQR;AAC5BA,gBAAaA,aAATN,EACTM,QAAOJ,IAAOI,cAAcQ,IAAQR;AAEtCA;UACFA,KAAKA;AACHA,gBAAaA,UAATN,EAGFM,QAFWC,EAAAb,KACHQ,IAAOI,MAAMJ,IAAOI,oBAAoBQ,IAAQR;AAEnDA,gBAAaA,aAATN,EACTM,QAAOJ,IAAOI,WAAWQ,IAAQR;AAEnCA;UACFA,KAAKA;AACGH,gBAAKnC,EAAUoC,YAAYrC,GAAOuC,IAA7BA;AACXA,gBAAaA,UAATN,EACFM,QAAOJ,IAAOI,MAAMH,IAAKG,SAASQ,IAAQR;AACrCA,gBAAaA,aAATN,EACTM,QAAOJ,IAAOI,aAAaH,IAAKG,UAAUQ,IAAQR;AAEpDA;UAEFA,KAAKA;AACGH,gBAAKnC,EAAUoC,YACnBrC,GACAuC,MACAA,GACAA,OACArC,mEAAM+C,WALGV;AAOPZ,gBAAOa,EAAAD;AACXA,gBAAaA,UAATN,EAEFM,QADAZ,KAAQQ,IAAOI,MAAMJ,IAAOI,eAAeH,IAAKG,SAASQ,IAAQR;AAE5DA,gBAAaA,aAATN,EAUTM,QATAZ,KACEQ,IACAI,aACAJ,IACAI,eACAH,IACAG,UACAQ,IACAR;AAGJA;UAEFA,KAAKA;AACCZ,gBAAOa,EAAAD;AACLW,gBAAOjD,EAAU0C,QAASC,gBAC9BL,QACAM,EAAAA,sCAASC,QAFEP;iBAKXA,SAASW,IAAOX,mCAAmCJ,IAAOI;AAC5DA,gBAAaA,UAATN,EAEFM,QADAZ,KAAQQ,IAAOI,MAAMW,IAAOX,SAASQ,IAAQR;AAExCA,gBAAaA,aAATN,EAETM,QADAZ,KAAQQ,IAAOI,aAAaW,IAAOX,UAAUQ,IAAQR;QAhE3DA;AAsEAA,cAAMD,MAAMC,yCAANA;MA3FmEA,GAqHrEY,iEAAAA,SACJnD,GACAC,GAA8BkD;AAI9BA,YAAMC,IAAkBD,EACtBA,OAASA,SACTA,MAAQA,QACRA,YAAcA,aACdA,UAAYA,UAJUA,GAOlBhB,IAAOlC,EAAUQ,YAAYT,GAAOmD,QAAQjD,mEAAMkB,MAA3C+B,KAAsDA;AACnEA,cAAME,IAASrD,EAAMyB,cAAc0B,QAApBA,GACTG,IAAStD,EAAMyB,cAAc0B,QAApBA;AAEfA,YAAeA,YAAXE,KAAiCF,WAAXG,EACjBnB,MAAOgB;iBAEdhB,EAAKM,MAAMU,OAAXA,KACYA,eAAXE,KAAoCF,iBAAXG,GAC1BH;AAIAA,kBAAQE,GAARF;YACEA,KAAKA;AACHI,kBAAMtD,EAAUoC,YAAYrC,GAAOmD,KAA7BA;AACNA;YACFA,KAAKA;AACHI,kBAAMtD,EAAUoC,YAAYrC,GAAOmD,OAAOA,GAAGA,OAAOjD,mEAAM+C,WAApDE;AACNI,kBAAMpB,IAAOgB,eAAeI;AAC5BJ;YACFA,KAAKA;AACHI,kBAAMJ;AACNA;YACFA;AACEA,oBAAMb,MAAMa,sCAANA;UAZVA;AAeAA,kBAAQG,GAARH;YACEA,KAAKA;AACHK,kBAAMvD,EAAUoC,YAAYrC,GAAOmD,OAAOA,CAApCA;AACNA;YACFA,KAAKA;AACHK,kBAAMvD,EAAUoC,YAAYrC,GAAOmD,OAAOA,GAAGA,OAAOjD,mEAAM+C,WAApDE;AACNK,kBAAMrB,IAAOgB,eAAeK;AAC5BL;YACFA,KAAKA;AACHK,kBAAMrB,IAAOgB;AACbA;YACFA;AACEA,oBAAMb,MAAMa,sCAANA;UAZVA;AAcAxB,cAAOQ,IAAOgB,YAAYI,IAAMJ,OAAOK,IAAML;QAjC7CA,OAkCKA;AACLA,gBAAMI,IAAMtD,EAAUoC,YAAYrC,GAAOmD,KAA7BA;AACNK,cAAMvD,EAAUoC,YAAYrC,GAAOmD,KAA7BA;AAmBZxB,cAZqB1B,EAAUa,iBAC7BqC,gBAAgBC,EAAgBC,CAAhBF,IAA0BC,EAAgBE,CAAhBH,GAC1CA;WAEElD,EAAUc,0BAFZoC,YALWA,eAAXE,KAAoCF,iBAAXE,IAA0BF,UAAUA,EAK7DA,GAHWA,eAAXG,KAAoCH,iBAAXG,IAA0BH,UAAUA,EAG7DA;gBAIUM,gEAAkBN,YAAYE,GAAQF,KAAtCA,CAJVA;cAKQM,gEAAkBN,YAAYG,GAAQH,KAAtCA,CALRA;;;CAFmBtC,IAcnBsC,MACAhB,KAGYgB,eAAXE,KAAoCF,iBAAXE,IAA0BF,OAAOI,IAAMJ,OACrDA,eAAXG,KAAoCH,iBAAXG,IAA0BH,OAAOK,IAAML,MACjEA;QA7BGA;AA+BPA,eAAOA,CAACxB,GAAMzB,mEAAMgB,aAAbiC;MArFuBA,GAwF1BO,2DAAAA,SACJ1D,GACAC,GAA8ByD;AAG9BA,cAAMvB,IACJlC,EAAUQ,YAAYT,GAAO0D,QAAQxD,mEAAMgB,aAA3CwC,KAA6DA,MACzDC,IAAiDD,QAArC1D,EAAMyB,cAAciC,WAApBA,IAA2CA,IAAIA;AAC3DE,YAAO5D,EAAMyB,cAAciC,MAApBA;AACPG,YAAyB5D,EAAUa,iBACvC4C,uBACAA;WACOzD,EAAUc,0BADjB2C;;;;;;;;;;;;OAF6BA;AAiB/BA,eAAOA,CACLvB,IACEuB,mBACAG,IACAH,OACAE,IACAF,QACAC,IACAD,MACFxD,mEAAMgB,aATDwC;MAxBuBA,GAqC1BI,4DAAAA,SACJ9D,GACAC,GAA8B6D;AAG9BA,YAAIC,IAAQ9D,EAAUQ,YAAYT,GAAO8D,SAAS5D,mEAAMkB,MAA5C0C;AACNE,YAAY/D,EAAUQ,YAAYT,GAAO8D,SAAS5D,mEAAMQ,IAA5CoD,KAAqDA;AACjE7B,YAAOjC,EAAMyB,cAAcqC,MAApBA;AAEbA,YAAaA,YAAT7B,EACG8B,OACHA,IAAQD,OAEVjD,IAAeiD;iBACGA,WAAT7B,EACJ8B,OACHA,IAAQD,OAEVjD,IAAeiD;YAEfA,OAAMxB,MAAMwB,mBAAmB7B,CAAzB6B;AAGRA,eAAOA,CADMC,IAAQD,MAAMjD,IAAeiD,MAAME,IAAYF,KAC9C5D,mEAAMgB,aAAb4C;MArBuBA,GAwB1BG,8DAAAA,SACJjE,GACAC,GAA8BgE;AAM9BA,eAAOA,EAFLhE,EAAUQ,YAAYT,GAAOiE,QAAQ/D,mEAAMgB,aAA3C+C,KAA6DA,QAC3CA,sBACN/D,mEAAMgB,aAAb+C;MANuBA,GCzb1BC,4DAAAA,SAAsBlE,GAAcC,GAA8BiE;AAEtEA,YAAIC,IAAID;AACRA,YAAIvC,IAAOuC;AACPjE,UAAUmE,qBAEZzC,KAAQ1B,EAAUoE,SAASpE,EAAUmE,kBAAkBpE,CAA/CkE;AAEVA,WAAGA;AACDA,gBAAMI,IACJrE,EAAUQ,YAAYT,GAAOkE,OAAOC,GAAGjE,mEAAMQ,IAA7CwD,KAAsDA;AACxDA,cAAIK,IAAatE,EAAUuE,gBAAgBxE,GAAOkE,OAAOC,CAAxCD;AACbjE,YAAUwE,qBACZF,IACEtE,EAAUyE,YACRzE,EAAUoE,SAASpE,EAAUwE,kBAAkBzE,CAA/CkE,GACAjE,EAAU0E,MAFZT,IAGIK;AAER5C,gBACOuC,IAAJC,IAAQD,WAAWA,MACpBA,SACAI,IACAJ,UACAK,IACAL;AACFC;QAlBCD,SAmBMlE,EAAM4E,SAASV,OAAOC,CAAtBD;AAETA,YAAIlE,EAAM4E,SAASV,MAAfA,KAA0BjE,EAAUwE,iBAClCF,KAAavE,EAAM4E,SAASV,MAAfA,IACbjE,EAAUuE,gBAAgBxE,GAAOkE,MAAjCA,IACAA,IACAjE,EAAUwE,qBACZF,IACEtE,EAAUyE,YACRzE,EAAUoE,SAASpE,EAAUwE,kBAAkBzE,CAA/CkE,GACAjE,EAAU0E,MAFZT,IAGIK,IAER5C,KAAQuC,cAAcK,IAAaL;AAErCA,eAAOvC,IAAOuC;MA1CwDA,GA+ClEW,8DAAAA,SACJ7E,GACAC,GAA8B4E;AAY9BA,cAAMrD,IATYsD,EAChBD,IAAMA,MACNA,KAAOA,MACPA,IAAMA,KACNA,KAAOA,MACPA,IAAMA,KACNA,KAAOA,KANSC,EASS9E,EAAMyB,cAAcoD,IAApBA,CAAVA,GACXE,IACSF,SAAbrD,KAAkCqD,SAAbrD,IAAoBtB,mEAAM8E,WAAW9E,mEAAM+E,YAC5DC,IAAYjF,EAAUQ,YAAYT,GAAO6E,KAAKE,CAAlCF,KAA4CA;AACxDM,YAAYlF,EAAUQ,YAAYT,GAAO6E,KAAKE,CAAlCF,KAA4CA;AAE9DA,eAAOA,CADMK,IAAYL,MAAMrD,IAAWqD,MAAMM,GAClCJ,CAAPF;MAlBuBA,GAqB1BO,gEAAAA,SACJpF,GACAC,GAA8BmF;AAG9BA,cAAM5D,IAAyC4D,UAA9BpF,EAAMyB,cAAc2D,IAApBA,IAAsCA,OAAOA,MACxDL,IAAqBK,SAAb5D,IAAoBtB,mEAAMmF,cAAcnF,mEAAMoF;AAC5DF,YAAIF,IAAYjF,EAAUQ,YAAYT,GAAOoF,KAAKL,CAAlCK;AACZD,YAAYlF,EAAUQ,YAAYT,GAAOoF,KAAKL,CAAlCK;AACXF,aAAcC,KAMXI,IAA+BH,SAAb5D,IAAoB4D,SAASA,SAChDF,MACHA,IAAYK,IAETJ,MACHA,IAAYI,MARdJ,IADAD,IAAYE;AAadA,eAAOA,CADMF,IAAYE,MAAM5D,IAAW4D,MAAMD,GAClCJ,CAAPK;MAtBuBA,GAyB1BI,6DAAAA,SACJxF,GACAC,GAA8BuF;AAG9BA,cAAMT,IAAQ7E,mEAAMoB;AAGpBkE,eAAOA,CADMA,OADKvF,EAAUQ,YAAYT,GAAOwF,QAAQT,CAArCS,KAA+CA,SAEnDT,CAAPS;MANuBA,GAS1BC,8DAAAA,SACJzF,GACAC,GAA8BwF;AAI9BA,eAAOA,CADsCA,WAAhCzF,EAAMyB,cAAcgE,MAApBA,IAAyCA,SAASA,SACjDvF,mEAAMC,MAAbsF;MAJuBA,GAO1BC,2DAAAA,SACJ1F,GACAC,GAA8ByF;AAG9BA,eAAOA,CAACA,QAAQxF,mEAAMC,MAAfuF;MAHuBA,GAM1BC,8DAAAA,SACJ3F,GACAC,GAA8B0F;AAG9BA,cAAMC,IACJ3F,EAAUQ,YAAYT,GAAO2F,MAAMzF,mEAAM2F,WAAzCF,KAAyDA,SACrDG,IACJ7F,EAAUQ,YAAYT,GAAO2F,QAAQzF,mEAAM2F,WAA3CF,KAA2DA;AACvDI,YACJ9F,EAAUQ,YAAYT,GAAO2F,QAAQzF,mEAAM2F,WAA3CF,KAA2DA;AAE7DA,eAAOA,CADMC,IAAWD,QAAQG,IAAaH,QAAQI,GACvC7F,mEAAM2F,WAAbF;MAVuBA,GC5H1BK,oEAAAA,SACJhG,GACAC,GAA8B+F;AAG9BA,YAAIC;AAGFA,YAFEjG,EAAMkG,SAASF,OAAfA,IAEQG,OAAOC,OAAOpG,EAAMyB,cAAcuE,OAApBA,CAAPA,CAAPA,IAGA/F,EAAUQ,YAAYT,GAAOgG,SAAS9F,mEAAM8C,UAA5CgD,KAA2DA;AAEvEA,YAAIK,IAASpG,EAAUuE,gBAAgBxE,GAAOgG,IAAjCA;AACbK,YAASpG,EAAUqG,YAAYD,GAAQrG,CAA9BgG;AACLrE,YAAOqE;AACXA,cAAMO,IAAUtG,EAAU0C,QAASC,gBACjCoD,SACAnD,EAAAA,sCAASC,QAFKkD;AAIhBA,YAAIQ,IAASP;AACRA,UAAQxD,MAAMuD,OAAdA,KAAuCS,EAAAA,6CAASR,CAArBD,MAC9BQ,IAASvG,EAAU0C,QAASC;UAC1BoD;UACAnD,EAAAA,sCAASC;QAFFkD,GAITrE,KAAQqE,SAASQ,IAASR,QAAQC,IAAUD;AAc9CA,eAZArE,KACEqE,cACAO,IACAP,WACAO,IACAP,QACAQ,IACAR,OACAO,IACAP,YACAK,IACAL;MArC4BA,GA2C1BU,oEAAAA,SACJ1G,GACAC,GAA8ByG;AAG9BA,cAAMC,IAAwCD,YAAhC1G,EAAMyB,cAAciF,MAApBA;AACdA,YAAIxB,IACFjF,EAAUQ,YACRT,GACA0G,QACAC,IAAQzG,mEAAMoB,cAAcpB,mEAAMQ,IAHpCgG,KAIKA,SACHL,IAASpG,EAAUuE,gBAAgBxE,GAAO0G,IAAjCA;AACbL,YAASpG,EAAUqG;UAAYD;UAAQrG;QAA9B0G;AACLC,cACFzB,IAAYwB,MAAMxB;AAEpBwB,eAAOA,YAAYxB,IAAYwB,UAAUL,IAASK;MAfpBA,GAkB1BE,6DAAAA,SAAuB5G,GAAcC,GAA8B2G;AAEvEA,YAAMC,IAAY5G,EAAU6G,gBAAgB9G,EAAMyB,cAAcmF,KAApBA,CAA1BA,GACZ1B,IACJjF,EAAUQ,YAAYT,GAAO4G,QAAQ1G,mEAAM8C,UAA3C4D,KAA0DA,KACtDzB,IAAYlF,EAAUQ,YAAYT,GAAO4G,MAAM1G,mEAAM8C,UAAzC4D,KAAwDA;AAC1EA,cAAMG,IAAY9G,EAAUQ,YAAYT,GAAO4G,MAAM1G,mEAAM8C,UAAzC4D,KAAwDA;YACtEP,IAASpG,EAAUuE,gBAAgBxE,GAAO4G,IAAjCA;AACbP,YAASpG,EAAUqG,YAAYD,GAAQrG,CAA9B4G;AAETA,YACcH,EAAAA,6CAASvB,CAArB0B,KACYH,EAAAA,6CAAStB,CAArByB,KACYH,EAAAA,6CAASM,CAArBH,EAGMI,KAAKZ,OAAOlB,CAAP0B,KAAqBR,OAAOjB,CAAPyB,GAChCjF,IACEiF,UACAC,IACAD,QACA1B,IACA0B,OACAC,KACCG,IAAKJ,SAASA,UACfzB,IACAyB,OACAC,GACII,IAAOC,KAAKC,IAAIf,OAAOW,CAAPH,CAATA,GAEXjF,IADWiF,MAATK,IACFtF,KAAQqF,IAAKJ,OAAOA,QAEpBjF,MAASqF,IAAKJ,SAASA,UAAUK,IAEnCtF,KAAQiF,UAAUP,IAASO;aACtBA;AACLjF,cAAOiF;AAEPA,cAAIQ,IAAWlC;AACVA,YAAUzC,MAAMmE,OAAhBA,KAAyCH,EAAAA,6CAASvB,CAArB0B,MAChCQ,IAAWnH,EAAU0C,QAASC;YAC5BiE,IAAYD;YACZ/D,EAAAA,sCAASC;UAFA8D,GAIXjF,KAAQiF,SAASQ,IAAWR,QAAQ1B,IAAY0B;AAE9CJ,cAASrB;AACRA,YAAU1C,MAAMmE,OAAhBA,KAAyCH,EAAAA,6CAAStB,CAArByB,MAChCJ,IAASvG,EAAU0C,QAASC,gBAC1BiE,IAAYD,QACZ/D,EAAAA,sCAASC,QAFF8D,GAITjF,KAAQiF,SAASJ,IAASI,QAAQzB,IAAYyB;AAI1CS,cAASpH,EAAU0C,QAASC,gBAChCiE,IAAYD,QACZ/D,EAAAA,sCAASC,QAFI8D;AAIfjF,eAAQiF,SAASS,IAAST;AAExBjF,cADc8E,EAAAA,6CAASM,CAArBH,IACFjF,KAAQuF,KAAKC,IAAIf,OAAOW,CAAPH,CAATA,IAA8BA,SAEtCjF,KAAQiF,cAAcG,IAAYH;AAEpCjF,eAAQiF,SAASQ,IAAWR,QAAQJ,IAASI;eACrC3G,EAAU0E,SAAS0C,IAAST,SAASS,IAAST;AAEtDjF,cADAA,IAAQiF,cAGNC,IACAD,QACAQ,IACAR,OACAS,IACAT,aACAC,IACAD,SACAJ,IACAI,QACAC,IACAD,SACAJ,IACAI,OACAC,IACAD,SACAS,IACAT,UACAP,IACAO;QAvDGA;AAyDPA,eAAOjF;MA5FgEiF,GA+FnEU,iEAAAA,SAA2BtH,GAAcC,GAA8BqH;AAE3EA,cAAMT,IAAY5G,EAAU6G,gBAAgB9G,EAAMyB,cAAc6F,KAApBA,CAA1BA;AAClBA,YAAMpC,IACJjF,EAAUQ,YAAYT,GAAOsH,QAAQpH,mEAAM8C,UAA3CsE,KAA0DA;AAC5DA,YAAIjB,IAASpG,EAAUuE,gBAAgBxE,GAAOsH,IAAjCA;AACbjB,YAASpG,EAAUqG,YAAYD,GAAQrG,CAA9BsH;AACL3F,YAAO2F;AAEXA,YAAI5E,IAAUwC;AACTA,UAAUzC,MAAM6E,OAAhBA,MACH5E,IAAUzC,EAAU0C,QAASC;UAC3BiE,IAAYS;UACZzE,EAAAA,sCAASC;QAFDwE,GAIV3F,KAAQ2F,SAAS5E,IAAU4E,QAAQpC,IAAYoC;AAE3CC,YAAWtH,EAAU0C,QAASC,gBAClCiE,IAAYS,UACZzE,EAAAA,sCAASC,QAFMwE;AAIjBjB,YACEpG,EAAU0E,SACVkC,IACAS,QACA5E,IACA4E,MACAC,IACAD,SACAjB;AAEFiB,eADA3F,KAAQ2F,cAAcC,IAAWD,SAAS5E,IAAU4E,UAAUjB,IAASiB;MA9BIA,GAkCvEE,yEAAAA,SACJxH,GACAC,GAA8BuH;AAG9BA,YAAIC,IAAOD;AACPvH,UAAUmE,qBAEZqD,KAAQxH,EAAUoE,SAASpE,EAAUmE,kBAAkBpE,CAA/CwH;AAENvH,UAAUwE,qBAGZgD,KAAQxH,EAAUoE,SAASpE,EAAUwE,kBAAkBzE,CAA/CwH;AAEVA,YAAIvH,EAAUmE,kBAAkBoD;AAC9BA,gBAAME,IAAQ1H,EAAiC2H,gBAAjCH;eACFA,CAACE,EAAKE,yBAIhBH,KAAQxH,EAAUoE,SAASpE,EAAUmE,kBAAkBsD,CAA/CF;QANoBA;AAShCA,gBAAQxH,EAAMyB,cAAc+F,MAApBA,GAARA;UACEA,KAAKA;AACHA,mBAAOC,IAAOD;UAChBA,KAAKA;AACHA,mBAAOC,IAAOD;QAJlBA;AAMAA,cAAMlF,MAAMkF,yBAANA;MA5BwBA,GCvM1BK,2DAAAA,SACJ7H,GACAC,GAA8B4H;AAGxBC,YAAS1B,OAAOpG,EAAMyB,cAAcoG,KAApBA,CAAPA;AAEfA,eAAOA,CAAC1B,OAAO2B,CAAPD,GADgBA,KAAVC,IAAc5H,mEAAMC,SAASD,mEAAM6H,cAC1CF;MALuBA,GAQ1BG,+DAAAA,SACJhI,GACAC,GAA8B+H;AAW9BA,YAAMC,IARoDnD,EACxDkD,KAAOA,CAACA,OAAO9H,mEAAM6B,QAAdiG,GACPA,OAASA,CAACA,OAAO9H,mEAAM+C,WAAd+E,GACTA,UAAYA,CAACA,OAAO9H,mEAAMgI,cAAdF,GACZA,QAAUA,CAACA,OAAO9H,mEAAMiI,QAAdH,GACVA,OAASA,CAACA,MAAM9H,mEAAMQ,IAAbsH,EAL+ClD,EAQlC9E,EAAMyB,cAAcuG,IAApBA,CAAVA;cACRxG,IAAWyG,EAAMD,CAANA;AACXjD,YAAQkD,EAAMD,CAANA;AACdA,cAAM9C,IAAYjF,EAAUQ,YAAYT,GAAOgI,KAAKjD,CAAlCiD,KAA4CA;AACxD7C,YAAYlF,EAAUQ,YAAYT,GAAOgI,KAAKjD,CAAlCiD,KAA4CA;AAG9DA,eAAKxG,IAKEwG,CADA9C,IAAY1D,IAAW2D,GAChBJ,CAAPiD,IAHEA,CADAA,cAAc9C,IAAY8C,OAAO7C,IAAY6C,KACtC9H,mEAAMgB,aAAb8G;MApBqBA,GA0B1BI,2DAAAA,SACJpI,GACAC,GAA8BmI;AAG9BA,cAAM5G,IAAWxB,EAAMyB,cAAc2G,IAApBA;AACjBA,YAAIzG;AAEJyG,YAAiBA,UAAb5G,EAQF4G,QANAC,IAAMpI,EAAUQ,YAAYT,GAAOoI,OAAOlI,mEAAM6H,cAA1CK,KAA6DA,KACpDA,QAAXC,EAAID,CAAJA,MAEFC,IAAMD,MAAMC,IAGPD,CADAA,MAAMC,GACCnI,mEAAM6H,cAAbK;YAEQA,UAAb5G,KAAmC4G,UAAb5G,KAAmC4G,UAAb5G,IACxCvB,EAAUQ,YAAYT,GAAOoI,OAAOlI,mEAAMiI,QAA1CC,KAAuDA,MAEvDnI,EAAUQ,YAAYT,GAAOoI,OAAOlI,mEAAMQ,IAA1C0H,KAAmDA;AAI3DA,gBAAQ5G,GAAR4G;UACEA,KAAKA;AACHzG,gBAAOyG,cAAcC,IAAMD;AAC3BA;UACFA,KAAKA;AACHzG,gBAAOyG,eAAeC,IAAMD;AAC5BA;UACFA,KAAKA;AACHzG,gBAAOyG,cAAcC,IAAMD;AAC3BA;UACFA,KAAKA;AACHzG,gBAAOyG,cAAcC,IAAMD;AAC3BA;UACFA,KAAKA;AACHzG,gBAAOyG,iBAAiBC,IAAMD;AAC9BA;UACFA,KAAKA;AACHzG,gBAAOyG,gBAAgBC,IAAMD;AAC7BA;UACFA,KAAKA;AACHzG,gBAAOyG,eAAeC,IAAMD;AAC5BA;UACFA,KAAKA;AACHzG,gBAAOyG,gBAAgBC,IAAMD;AAC7BA;UACFA,KAAKA;AACHzG,gBAAOyG,cAAcC,IAAMD;AAC3BA;UACFA,KAAKA;AACHzG,gBAAOyG,cAAcC,IAAMD;AAC3BA;UACFA,KAAKA;AACHzG,gBAAOyG,cAAcC,IAAMD;QAhC/BA;AAmCAA,YAAIzG,EACFyG,QAAOA,CAACzG,GAAMzB,mEAAMgB,aAAbkH;AAITA,gBAAQ5G,GAAR4G;UACEA,KAAKA;AACHzG,gBAAOyG,cAAcC,IAAMD;AAC3BA;UACFA,KAAKA;AACHzG,gBAAOyG,eAAeC,IAAMD;AAC5BA;UACFA,KAAKA;AACHzG,gBAAOyG,eAAeC,IAAMD;AAC5BA;UACFA,KAAKA;AACHzG,gBAAOyG,eAAeC,IAAMD;AAC5BA;UACFA;AACEA,kBAAM9F,MAAM8F,4BAA4B5G,CAAlC4G;QAdVA;AAgBAA,eAAOA,CAACzG,GAAMzB,mEAAMiI,QAAbC;MA/EuBA,GAkF1BE,6DAAAA,SACJtI,GACAC,GAA8BqI;AAY9BA,eATmDC;UACjDD,IAAMA,CAACA,WAAWpI,mEAAMkB,MAAlBkH;UACNA,GAAKA,CAACA,UAAUpI,mEAAMkB,MAAjBkH;UACLA,cAAgBA,CAACA,0BAA0BpI,mEAAMiI,QAAjCG;UAChBA,OAASA,CAACA,cAAcpI,mEAAMkB,MAArBkH;UACTA,SAAWA,CAACA,gBAAgBpI,mEAAMkB,MAAvBkH;UACXA,UAAYA,CAACA,YAAYpI,mEAAMC,MAAnBmI;QANqCC,EASlCvI,EAAMyB,cAAc6G,UAApBA,CAAVA;MAZuBA,GAe1BE,oEAAAA,SACJxI,GACAC,GAA8BuI;AAI9BA,YAAMC,IAA4DD,EAChEA,MAAQA;UAACA;UAActI,mEAAMwI;UAASxI,mEAAM8E;QAApCwD,GACRA,KAAOA,CAACA,cAActI,mEAAMwI,SAASxI,mEAAM8E,QAApCwD,GACPA,OAASA,CAACA,cAActI,mEAAMwI,SAASxI,mEAAM8E,QAApCwD,GACTA,UAAYA;UAACA;UAAQtI,mEAAM+E;UAAY/E,mEAAM+E;QAAjCuD,GACZA,UAAYA,CAACA,QAAQtI,mEAAM+E,YAAY/E,mEAAM+E,UAAjCuD,GACZA,cAAgBA,CAACA,MAAMtI,mEAAMwI,SAASxI,mEAAM8E,QAA5BwD,GAChBA,OAASA;UAACA;UAAMtI,mEAAMQ;UAAMR,mEAAMgB;QAAzBsH,EAPuDA;AAUlEA,cAAMG,IAAmB3I,EAAMyB,cAAc+G,UAApBA,GACnBA,CAACI,GAAQC,GAAYC,CAArBN,IAAoCC,EAAWE,CAAXH;AACpCO,YACJ9I,EAAUQ,YAAYT,GAAOwI,mBAAmBK,CAAhDL,KAA+DA;AAExCA,oBAArBG,IAyBFhH,IAvBqB1B,EAAUa,iBAC7B0H,eACAA;WACKvI,EAAUc,0BADfyH;;;;;;;;;;;;;;;;;;CAFmB3H,IAuBC2H,MAAMO,IAAgBP,MACdA,mBAArBG,KACHK,IACJ/I,EAAUQ,YAAYT,GAAOwI,WAAWtI,mEAAMwI,OAA9CF,KAA0DA,KAC5D7G,IAAOoH,IAAgBP,QAAQQ,IAAUR,YAEzC7G,IAAOoH,IAAgBH;AAEzBJ,eAAOA,CAAC7G,GAAMmH,CAAPN;MApDuBA,GAuD1BS,2DAAAA,SAAsBjJ,GAAcC,GAA8BgJ;AAEtEA,cAAM/D,IACJjF,EAAUQ,YAAYT,GAAOiJ,SAAS/I,mEAAM6B,QAA5CkH,KAAyDA;AACrDC,YAAUjJ,EAAU6G,gBAAgB9G,EAAMyB,cAAcwH,KAApBA,CAA1BA;AAChBA,eACEC,IACAD,gBACAC,IACAD,qBACAC,IACAD,aACA/D,IACA+D;MAboEA,GAsBlEE,4DAAAA,SACJnJ,GACAC,GAA8BkJ;AAG9BA,YAAMC,IAAOpJ,EAAMyB,cAAc0H,IAApBA;AAGbA,gBAAQC,GAARD;UACEA,KAAKA;AACHhH,gBAAOlC,EAAUQ,YAAYT,GAAOmJ,QAAQjJ,mEAAMkB,MAA3C+H,KAAsDA;AACtDhH,iBAAOgH;AACdA;UACFA,KAAKA;AACHhH,gBAAOlC,EAAUQ,YAAYT,GAAOmJ,QAAQjJ,mEAAMQ,IAA3CyI,KAAoDA;AAC3DxH,gBAAOwH,0BAA0BhH,IAAOgH;AACxCA;UACFA,KAAKA;AACHhH,gBAAOlC,EAAUQ,YAAYT,GAAOmJ,QAAQjJ,mEAAMQ,IAA3CyI,KAAoDA;AAC3DxH,gBAAOwH,0BAA0BhH,IAAOgH;AACxCA;UACFA,KAAKA;AAEGtI,gBAAeZ,EAAUa,iBAC7BqI,YACAA;WACGlJ,EAAUc,0BADboI;;;CAFmBA;AAQrBhH,gBAAOlC,EAAUQ,YAAYT,GAAOmJ,QAAQjJ,mEAAMQ,IAA3CyI,KAAoDA;AAC3DxH,gBAAOd,IAAesI,MAAMhH,IAAOgH;AACnCA;UAEFA,KAAKA;AAEGtI,gBAAeZ,EAAUa,iBAC7BqI,cACAA;WACGlJ,EAAUc,0BADboI;;;;;;;;;;CAFmBA;AAerBhH,gBAAOlC,EAAUQ,YAAYT,GAAOmJ,QAAQjJ,mEAAMQ,IAA3CyI,KAAoDA;AAC3DxH,gBAAOd,IAAesI,MAAMhH,IAAOgH;AACnCA;UAEFA,KAAKA;AAIGtI,gBAAeZ,EAAUa,iBAC7BqI,aACAA;WACGlJ,EAAUc,0BADboI;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAFmBA;AAiCrBhH,gBAAOlC,EAAUQ,YAAYT,GAAOmJ,QAAQjJ,mEAAMQ,IAA3CyI,KAAoDA;AAC3DxH,gBAAOd,IAAesI,MAAMhH,IAAOgH;AACnCA;UAEFA,KAAKA;AACGtI,gBAAeZ,EAAUa,iBAC7BqI,yBACAA;WACGlJ,EAAUc,0BADboI;;;;;;;;;;;CAFmBA;AAgBrBhH,gBAAOlC,EAAUQ,YAAYT,GAAOmJ,QAAQjJ,mEAAMQ,IAA3CyI,KAAoDA;AAC3DxH,gBAAOd,IAAesI,MAAMhH,IAAOgH;AACnCA;UAEFA,KAAKA;AACGtI,gBAAeZ,EAAUa,iBAC7BqI,kBACAA;WACGlJ,EAAUc,0BADboI;;;;CAFmBA;AASrBhH,gBAAOlC,EAAUQ,YAAYT,GAAOmJ,QAAQjJ,mEAAMQ,IAA3CyI,KAAoDA;AAC3DxH,gBAAOd,IAAesI,MAAMhH,IAAOgH;AACnCA;UAEFA;AACEA,kBAAM7G,MAAM6G,uBAAuBC,CAA7BD;QA7HVA;AA+HAA,eAAOA,CAACxH,GAAMzB,mEAAMgB,aAAbiI;MArIuBA,GAwI1BE,2DAAAA,SACJrJ,GACAC,GAA8BoJ;AAG9BA,cAAMnE,IACJjF,EAAUQ,YAAYT,GAAOqJ,YAAYnJ,mEAAMwI,OAA/CW,KAA2DA;AACvDlE,YACJlF,EAAUQ,YAAYT,GAAOqJ,WAAWnJ,mEAAMwI,OAA9CW,KAA0DA;AAE5DA,eAAOA,CADMnE,IAAYmE,QAAQlE,GACnBjF,mEAAMwI,OAAbW;MARuBA,GAW1BC,8DAAAA,SACJtJ,GACAC,GAA8BqJ;AAG9BA,cAAMpE,IAAYjF,EAAUQ,YAAYT,GAAOsJ,SAASpJ,mEAAMQ,IAA5C4I,KAAqDA,KACjEnE,IAAYlF,EAAUQ,YAAYT,GAAOsJ,OAAOpJ,mEAAMQ,IAA1C4I,KAAmDA;AAC/DC,YACJtJ,EAAUQ,YAAYT,GAAOsJ,QAAQpJ,mEAAMQ,IAA3C4I,KAAoDA;AAStDA,eAAOA,CAPLA,uBACApE,IACAoE,OACAnE,IACAmE,QACAC,IACAD,KACYpJ,mEAAMgB,aAAboI;MAfuBA,GAkB1BE,+DAAAA,SACJxJ,GACAC,GAA8BuJ;AAG9BA,cAAMtE,IAAYjF,EAAUQ,YAAYT,GAAOwJ,QAAQtJ,mEAAMQ,IAA3C8I,KAAoDA;AAChErE,YAAYlF,EAAUQ,YAAYT,GAAOwJ,MAAMtJ,mEAAMQ,IAAzC8I,KAAkDA;AAgBpEA,eAAOA,CAfcvJ,EAAUa,iBAC7B0I,iBACAA;WACOvJ,EAAUc,0BADjByI;;;;;;;;;CAFmB3I,IAcO2I,MAAMtE,IAAYsE,OAAOrE,IAAYqE,KACnDtJ,mEAAMgB,aAAbsI;MApBuBA,GAuB1BC,iEAAAA,SACJzJ,GACAC,GAA8BwJ;AAG9BA,eAAOA,CAACA,iBAAiBvJ,mEAAMgB,aAAxBuI;MAHuBA,GAM1BC,0DAAAA,SACJ1J,GACAC,GAA8ByJ;AAG9BA,cAAMxE,IAAYjF,EAAUQ,YAAYT,GAAO0J,KAAKxJ,mEAAMQ,IAAxCgJ,KAAiDA;AAEnEA,eAAOA,CACLA,iBAFgBzJ,EAAUQ;UAAYT;UAAO0J;UAAKxJ,mEAAMQ;QAAxCgJ,KAAiDA,OAErCA,OAAOxE,IAAYwE,qBAC/CxJ,mEAAMiI,QAFDuB;MALuBA,GCva1BC,0EAAAA,SACJ3J,GACAC,GAA8B0J;AAG9BA,cAAMC,IAAW3J,EAAU4J,iBAAiB7J,EAAMyB,cAAckI,MAApBA,CAA3BA;AACjBA,YAAIG,IAAQH;AACR1J,UAAUmE,qBACZ0F,KAAS7J,EAAUoE,SAASpE,EAAUmE,kBAAkBpE,CAA/C2J;AAEP1J,UAAUwE,qBACZqF,KAAS7J,EAAUoE,SAASpE,EAAUwE,kBAAkBzE,CAA/C2J;AAEPG,cACFA,IAAQ7J,EAAUyE,YAAYoF,GAAO7J,EAAU0E,MAAvCgF;AAEVA,YAAII,IAAWJ;AACX1J,UAAU+J,uBACZD,IAAW9J,EAAUyE,YACnBzE,EAAUoE,SAASpE,EAAU+J,oBAAoBhK,CAAjD2J,GACA1J,EAAU0E,MAFDgF;AAKbA,YAAItD,IAASsD;AACT3J,UAAM4E,SAAS+E,OAAfA,MAEFtD,IAASpG,EAAUuE,gBAAgBxE,GAAO2J,OAAjCA;AAEXA,YAAIM,IAAcN;AACd3J,UAAM4E,SAAS+E,QAAfA,MAGFM,IAAchK,EAAUQ,YAAYT,GAAO2J,UAAUzJ,mEAAMQ,IAA7CiJ,KAAsDA;AAEtEA,YAAIO,IAAQP;AACRtD,aAAU4D,MAEZC,IAAQJ;AAENG,cACFA,IAAchK,EAAU0E,SAASgF,YAAYM,IAAcN;AAE7DA,cAAMQ,IAAOR,CAAAA,GACPS,IAAYpK,EAAMqK,QAANV;AAClBA,iBAASnJ,IAAImJ,GAAGnJ,IAAI4J,EAAUE,QAAQ9J,IACpC2J,GAAK3J,CAALmJ,IAAU1J,EAAU6G,gBAAgBsD,EAAU5J,CAAVmJ,CAA1BA;AAERhI,YACFgI,cACAC,IACAD,MACAQ,EAAKxJ,KAAKgJ,IAAVA,IACAA,UACAG,IACAC,IACA1D,IACA6D,IACAD,IACAN;AACFhI,YAAO1B,EAAUsK,OAAOvK,GAAO2B,CAAxBgI;AAIN1J,UAAiCuK,aAAab,MAAMC,CAApDD,IAAgEhI;AACjEgI,eAAOA;MA9DuBA,GAqE1Bc,2EAAAA,SACJzK,GACAC,GAA8BwK;AAG9BA,cAAMb,IAAW3J,EAAU4J,iBAAiB7J,EAAMyB,cAAcgJ,MAApBA,CAA3BA,GACXN,IAAOM,CAAAA,GACPL,IAAYpK,EAAMqK,QAANI;AAClBA,iBAASjK,IAAIiK,GAAGjK,IAAI4J,EAAUE,QAAQ9J,IACpC2J,GAAK3J,CAALiK,IAAUxK,EAAUQ,YAAYT,GAAOyK,QAAQjK,GAAGN,mEAAMQ,IAA9C+J,KAAuDA;AAGnEA,eAAOA,CADMb,IAAWa,MAAMN,EAAKxJ,KAAK8J,IAAVA,IAAkBA,KAClCvK,mEAAMgB,aAAbuJ;MAVuBA,GAa1BC,6EAAAA,SACJ1K,GACAC,GAA8ByK;AAS9BA,eAJczK,EAAU0K,SAAVD;UACZ1K;UACAC;QAFYgI,EAIDyC,CAANA,IAAWA;MATYA,GAY1BE,yEAAAA,SACJ5K,GACAC,GAA8B2K;AAK9BA,YAAIjJ,IAAOiJ,UADT3K,EAAUQ,YAAYT,GAAO4K,aAAa1K,mEAAMQ,IAAhDkK,KAAyDA,WAC3BA;AAC5B3K,UAAUwE,qBAGZ9C,KAAQ1B,EAAUyE,YAChBzE,EAAUoE,SAASpE,EAAUwE,kBAAkBzE,CAA/C4K,GACA3K,EAAU0E,MAFJiG;AAKL5K,UAAwB6K,mBACrB9H,IAAQ9C,EAAUQ,YAAYT,GAAO4K,SAAS1K,mEAAMQ,IAA5CkK,KAAqDA,QACnEjJ,KAAQ1B,EAAU0E,SAASiG,YAAY7H,IAAQ6H,SAE/CjJ,KAAQ1B,EAAU0E,SAASiG;AAG7BA,eADAjJ,IAAQiJ;MApBsBA,GC5D1BE,oDAAAA,SACJ9K,GACAC,GAA8B6K;AAI9BA,eAAOA,CADM7K,EAAU8K,OAAO/K,EAAMyB,cAAcqJ,MAApBA,CAAjBnJ,GACCzB,mEAAMC,MAAb2K;MAJuBA,GAO1BE,yDAAAA,SACJhL,GACAC,GAA8B+K;AAI9BA,gBADkBhL,EACAO,YAAlByK;UACEA,KAAKA;AACHA,mBAAOA,CAACA,MAAM9K,mEAAMC,MAAb6K;UACTA,KAAKA;AAIHA,mBAHMhK,IACJf,EAAUQ,YANET,GAMqBgL,QAAQ9K,mEAAMQ,IAA/CsK,KAAwDA,MACrCC,yDAAYjK,CAAZkK;eAGlBF;AACHA,gBAAMG,IACJlL,EAAUQ,YAZET,GAYqBgL,QAAQ9K,mEAAMQ,IAA/CsK,KAAwDA;AACpDI,gBACJnL,EAAUQ,YAdET,GAcqBgL,QAAQ9K,mEAAMQ,IAA/CsK,KAAwDA;AAE1DA,mBAAOA,CADMC,yDAAYE,CAAZH,EAAsBA,CAAtBA,IAA2BA,QAAQC,yDAAYG,CAAZJ,EAAsBA,CAAtBA,GAClC9K,mEAAM6B,QAAbiJ;UAETA;AACQ3K,gBAAeC,MAnBPN,EAmBuBO,UAApByK;AACjBA,qBAASxK,IAAIwK,GAAGxK,IApBFR,EAoBgBO,YAAYC,IACxCH,GAASG,CAATwK,IACE/K,EAAUQ,YAtBAT,GAsBuBgL,QAAQxK,GAAGN,mEAAMQ,IAAlDsK,KAA2DA;AAG/DA,mBAAOA,CADMA,MAAM3K,EAASM,KAAKqK,GAAdA,IAAqBA,cAC1B9K,mEAAMgB,aAAb8J;QAxBXA;MAJ8BA,GAiC1BK,2DAAAA,SAAsBrL,GAAcC,GAA8BoL;AAEtEA,cAAMnC,IAAUjJ,EAAU6G,gBAAgB9G,EAAMyB,cAAc4J,KAApBA,CAA1BA;AACVtI,YAAQ9C,EAAUQ,YAAYT,GAAOqL,QAAQnL,mEAAMQ,IAA3C2K,KAAoDA;AAElEA,eADanC,IAAUmC,SAASJ,yDAAYlI,CAAZsI,EAAmBA,CAAnBA,IAAwBA;MAJcA,GAQlEC,2DAAAA,SACJtL,GACAC,GAA8BqL;AAI9BA,eAAOA,EADMrL,EAAUQ,YAAYT,GAAOsL,SAASpL,mEAAMkB,MAA5CkK,KAAuDA,QACrDA,WAAWpL,mEAAMkB,MAAzBkK;MAJuBA,GAO1BC,4DAAAA,SACJvL,GACAC,GAA8BsL;AAI9BA,eAAOA,CAACA,OADKtL,EAAUQ,YAAYT,GAAOuL,SAASrL,mEAAMkB,MAA5CmK,KAAuDA,QAC/CA,WAAWrL,mEAAMoB,WAA/BiK;MAJuBA,GAO1BC,4DAAAA,SACJxL,GACAC,GAA8BuL;AAG9BA,cAAMhK,IAC2BgK,YAA/BxL,EAAMyB,cAAc+J,KAApBA,IAAyCA,YAAYA,eACjDC,IAAYxL,EAAUQ,YAAYT,GAAOwL,QAAQtL,mEAAMQ,IAA3C8K,KAAoDA;AAEhE7J,aADO1B,EAAUQ,YAAYT,GAAOwL,SAAStL,mEAAMkB,MAA5CoK,KAAuDA,QAChDA,MAAMhK,IAAWgK,MAAMC,IAAYD;AAEvDA,eAAIxL,EAAM4B,UAAUC,QAAQC,gBACnB0J,CAAC7J,IAAO6J,QAAQtL,mEAAM6B,QAAtByJ,IAEFA,CAAC7J,GAAMzB,mEAAMgB,aAAbsK;MAZuBA,GAe1BE,2DAAAA,SACJ1L,GACAC,GAA8ByL;AAI9BA,cAAMxJ,IAAQlC,EAAMyB,cAAciK,OAApBA,KAAgCA,cAExCZ,IAAO7K,EAAUQ,YAAYT,GAAO0L,SADdA,aAAVxJ,IAAqBhC,mEAAMQ,OAAOR,mEAAMkB,MAC7CsK,KAAoDA;AACjEA,gBAAQxJ,GAARwJ;UACEA,KAAKA;AAEHA,mBAAOA,CADMZ,IAAOY,cACNxL,mEAAMgB,aAAbwK;UAETA,KAAKA;AAEHA,mBAAOA,CADMZ,IAAOY,cACNxL,mEAAMgB,aAAbwK;eAEJA;AAIHA,mBAHMtJ,IAAKnC,EAAUoC,YAAYrC,GAAO0L,IAA7BA,GAGJA,CADMZ,IAAOY,aAAatJ,IAAKsJ,KACxBxL,mEAAMgB,aAAbwK;UAETA,KAAKA;AAGHA,mBAFMtJ,IAAKnC,EAAUoC,YAAYrC,GAAO0L,MAAMA,GAAGA,IAAtCA,GAEJA,CADMZ,IAAOY,YAAYtJ,IAAKsJ,eACvBxL,mEAAMgB,aAAbwK;UAETA,KAAKA;AAWHA,mBAAOA,CAVczL,EAAUa,iBAC7B4K,oBACAA;WACGzL,EAAUc,0BADb2K;;;;CAFmB7K,IASO6K,MAAMZ,IAAOY,KAC3BxL,mEAAMgB,aAAbwK;QA/BXA;AAkCAA,cAAMpJ,MAAMoJ,iCAANA;MAzCwBA,GA4C1BC,iEAAAA,SACJ3L,GACAC,GAA8B0L;AAG9BA,YAAMvI,IAAkBuI,EACtBA,OAASA,SACTA,MAAQA,QACRA,YAAcA,aACdA,UAAYA,UAJUA;AAQxBA,cAAMtI,IAASrD,EAAMyB,cAAckK,QAApBA,GACTrI,IAAStD,EAAMyB,cAAckK,QAApBA;AACfA,YAAMC,IACOD,eAAXtI,KACWsI,WAAXtI,KACWsI,eAAXrI,KACWqI,WAAXrI,GAEIwH,IAAO7K,EAAUQ,YAAYT,GAAO2L,UADxBC,IAAqB1L,mEAAMkB,SAASlB,mEAAMQ,IAC/CiL,KAAqDA;AAElEA,YAAeA,YAAXtI,KAAiCsI,WAAXrI,EAExBqI,QAAOA,CADAb,GACO5K,mEAAMQ,IAAbiL;AACFA,YAAIb,EAAKrI,MAAMkJ,WAAXA,KAA2BC,GAAoBD;AAIxDA,kBAAQtI,GAARsI;YACEA,KAAKA;AACHpI,kBAAMtD,EAAUoC,YAAYrC,GAAO2L,KAA7BA;AACNA;YACFA,KAAKA;AACHpI,kBAAMtD,EAAUoC,YAAYrC,GAAO2L,OAAOA,GAAGA,OAAOzL,mEAAM+C,WAApD0I;AACNpI,kBAAMuH,IAAOa,eAAepI;AAC5BoI;YACFA,KAAKA;AACHpI,kBAAMoI;AACNA;YACFA;AACEA,oBAAMrJ,MAAMqJ,uCAANA;UAZVA;AAeAA,kBAAQrI,GAARqI;YACEA,KAAKA;AACHnI,kBAAMvD,EAAUoC,YAAYrC,GAAO2L,OAAOA,CAApCA;AACNA;YACFA,KAAKA;AACHnI,kBAAMvD,EAAUoC,YAAYrC,GAAO2L,OAAOA,GAAGA,OAAOzL,mEAAM+C,WAApD0I;AACNnI,kBAAMsH,IAAOa,eAAenI;AAC5BmI;YACFA,KAAKA;AACHnI,kBAAMsH,IAAOa;AACbA;YACFA;AACEA,oBAAMrJ,MAAMqJ,uCAANA;UAZVA;AAcAhK,cAAOmJ,IAAOa,YAAYpI,IAAMoI,OAAOnI,IAAMmI;QAjCWA,MAmClDpI,KAAMtD,EAAUoC,YAAYrC,GAAO2L,KAA7BA,GACNnI,IAAMvD,EAAUoC,YAAYrC,GAAO2L,KAA7BA,GAmBZhK,IAZqB1B,EAAUa,iBAC7B6K,gBAAgBvI,EAAgBC,CAAhBsI,IAA0BvI,EAAgBE,CAAhBqI,GAC1CA;WAEE1L,EAAUc,0BAFZ4K,YALWA,eAAXtI,KAAoCsI,iBAAXtI,IAA0BsI,UAAUA,EAK7DA,GAHWA,eAAXrI,KAAoCqI,iBAAXrI,IAA0BqI,UAAUA,EAG7DA;gBAIUlI,+DAAkBkI,YAAYtI,GAAQsI,KAAtCA,CAJVA;cAKQlI,+DAAkBkI,YAAYrI,GAAQqI,KAAtCA,CALRA;;;CAFmB9K,IAcnB8K,MACAb,KAGYa,eAAXtI,KAAoCsI,iBAAXtI,IAA0BsI,OAAOpI,IAAMoI,OACrDA,eAAXrI,KAAoCqI,iBAAXrI,IAA0BqI,OAAOnI,IAAMmI,MACjEA;AAEJA,eAAOA,CAAChK,GAAMzB,mEAAMgB,aAAbyK;MAzFuBA,GA4F1BE,+DAAAA,SACJ7L,GACAC,GAA8B4L;AAS9BA,cAAMrK,IANYsD,EAChB+G,WAAaA,kBACbA,WAAaA,kBACbA,WAAaA,KAHG/G,EAMS9E,EAAMyB,cAAcoK,MAApBA,CAAVA;AAEXf,YAAO7K,EAAUQ,YAAYT,GAAO6L,QADxBrK,IAAWtB,mEAAMkB,SAASlB,mEAAMQ,IACrCmL,KAAmDA;AAkBhEA,eAAOA,CAhBHrK,IAEKsJ,IAAOtJ,IAGOvB,EAAUa,iBAC7B+K,mBACAA;WACK5L,EAAUc,0BADf8K;;;;CAFmBhL,IASCgL,MAAMf,IAAOe,KAEvB3L,mEAAMgB,aAAb2K;MA7BuBA,GAgC1BC,yDAAAA,SACJ9L,GACAC,GAA8B6L;AAS9BA,cAAMtK,IANYsD,EAChBgH,MAAQA,gCACRA,OAASA,gCACTA,MAAQA,UAHQhH,EAMS9E,EAAMyB,cAAcqK,MAApBA,CAAVA;AAEjBA,eAAOA,EADM7L,EAAUQ,YAAYT,GAAO8L,QAAQ5L,mEAAMkB,MAA3C0K,KAAsDA,QACpDtK,GAAUtB,mEAAMgB,aAAxB4K;MAXuBA,GAc1BC,0DAAAA,SAAqB/L,GAAcC,GAA8B8L;AAGrEA,eAAOA,mBADK9L,EAAUQ,YAAYT,GAAO+L,QAAQ7L,mEAAMQ,IAA3CqL,KAAoDA,QACjCA;MAHsCA,GAMjEC,+DAAAA,SACJhM,GACAC,GAA8B+L;AAW1BrK,YAAOqK,oBAPPhM,EAAMkG,SAAS8F,MAAfA,IAEI/L,EAAU8K,OAAO/K,EAAMyB,cAAcuK,MAApBA,CAAjBA,IAGA/L,EAAUQ,YAAYT,GAAOgM,QAAQ9L,mEAAMQ,IAA3CsL,KAAoDA,QAExBA;AACaA,qBAAhChM,EAAMyB,cAAcuK,MAApBA,MAEfrK,IAAOqK,YAAYrK,IAAOqK;AAE5BA,eAAOA,CAACrK,GAAMzB,mEAAMgB,aAAb8K;MAhBuBA,GAqB1BC,0DAAAA,SACJjM,GACAC,GAA8BgM;AAE9BA,cAAMnB,IAAO7K,EAAUQ,YAAYT,GAAOiM,QAAQ/L,mEAAMQ,IAA3CuL,KAAoDA;AAC3DC,YAAMjM,EAAUQ,YAAYT,GAAOiM,OAAO/L,mEAAMQ,IAA1CuL,KAAmDA;AAc/DA,eAAOA,CAbchM,EAAUa,iBAC7BmL,aACAA;WACOhM,EAAUc,0BADjBkL;;;;;;;CAFmBpL,IAYOoL,MAAMnB,IAAOmB,OAAOC,IAAMD,KACxC/L,mEAAMgB,aAAb+K;MAjBuBA,GAoB1BE,4DAAAA,SACJnM,GACAC,GAA8BkM;AAE9BA,cAAMrB,IAAO7K,EAAUQ,YAAYT,GAAOmM,QAAQjM,mEAAMQ,IAA3CyL,KAAoDA,MAC3DC,IAAOnM,EAAUQ,YAAYT,GAAOmM,QAAQjM,mEAAMQ,IAA3CyL,KAAoDA;AAC3DE,YAAKpM,EAAUQ,YAAYT,GAAOmM,MAAMjM,mEAAMQ,IAAzCyL,KAAkDA;AAc7DA,eAAOA,CAXclM,EAAUa;UAC7BqL;UACAA;WACOlM,EAAUc,0BADjBoL;;;;;;QAFmBtL,IAUOsL,MAAMrB,IAAOqB,OAAOC,IAAOD,OAAOE,IAAKF,KACrDjM,mEAAMgB,aAAbiL;MAlBuBA,GAqB1BG,4DAAAA,SACJtM,GACAC,GAA8BqM;AAI9BA,eAAOA,EAFMrM,EAAUQ,YAAYT,GAAOsM,QAAQpM,mEAAMkB,MAA3CkL,KAAsDA,QAC/CA,iCACNpM,mEAAMgB,aAAboL;MAJuBA,GC5Y1BC,kEAAAA,SACJvM,GACAC,GAA8BsM;AAI9BA,eAAOA;UADMtM,EAAU6G,gBAAgB9G,EAAMyB,cAAc8K,KAApBA,CAA1B5K;UACCzB,mEAAMC;QAAboM;MAJuBA,GAO1BC,kEAAAA,SAAwBxM,GAAcC,GAA8BuM;AAExEA,cAAMtH,IACJjF,EAAUQ,YAAYT,GAAOwM,SAAStM,mEAAM8C,UAA5CwJ,KAA2DA;AAE7DA,eADgBvM,EAAU6G,gBAAgB9G,EAAMyB,cAAc+K,KAApBA,CAA1BtD,IACCsD,QAAQtH,IAAYsH;MALmCA,GCC9DtM;gBAAAA,GAAK;AACfA,UAAAA,EAAAC,SAAA,CAAA,IAAA;AACAD,UAAAA,EAAAuM,MAAA,GAAA,IAAA;AACAvM,UAAAA,EAAAkB,SAAA,GAAA,IAAA;AACAlB,UAAAA,EAAAgB,gBAAA,CAAA,IAAA;AACAhB,UAAAA,EAAAwM,YAAA,CAAA,IAAA;AACAxM,UAAAA,EAAAyM,YAAA,CAAA,IAAA;AACAzM,UAAAA,EAAA0M,cAAA,GAAA,IAAA;AACA1M,UAAAA,EAAA2M,aAAA,GAAA,IAAA;AACA3M,UAAAA,EAAA6H,iBAAA,GAAA,IAAA;AACA7H,UAAAA,EAAAoB,cAAA,GAAA,IAAA;AACApB,UAAAA,EAAA4M,SAAA,GAAA,IAAA;AACA5M,UAAAA,EAAA6M,OAAA,GAAA,IAAA;AACA7M,UAAAA,EAAA8M,SAAA,GAAA,IAAA;AACA9M,UAAAA,EAAA+M,QAAA,GAAA,IAAA;AACA/M,UAAAA,EAAAgN,iBAAA,CAAA,IAAA;AACAhN,UAAAA,EAAAgI,iBAAA,GAAA,IAAA;AACAhI,UAAAA,EAAAiI,WAAA,GAAA,IAAA;AACAjI,UAAAA,EAAAwI,UAAA,GAAA,IAAA;AACAxI,UAAAA,EAAA+C,cAAA,GAAA,IAAA;AACA/C,UAAAA,EAAA6B,WAAA,GAAA,IAAA;AACA7B,UAAAA,EAAAiN,gBAAA,CAAA,IAAA;AACAjN,UAAAA,EAAA+E,aAAA,CAAA,IAAA;AACA/E,UAAAA,EAAAkN,KAAA,CAAA,IAAA;AACAlN,UAAAA,EAAAmN,aAAA,CAAA,IAAA;AACAnN,UAAAA,EAAA8E,WAAA,CAAA,IAAA;AACA9E,UAAAA,EAAAoN,cAAA,EAAA,IAAA;AACApN,UAAAA,EAAAqN,cAAA,EAAA,IAAA;AACArN,UAAAA,EAAAsN,aAAA,EAAA,IAAA;AACAtN,UAAAA,EAAAmF,cAAA,EAAA,IAAA;AACAnF,UAAAA,EAAAoF,aAAA,EAAA,IAAA;AACApF,UAAAA,EAAA2F,cAAA,EAAA,IAAA;AACA3F,UAAAA,EAAA8C,aAAA,EAAA,IAAA;AACA9C,UAAAA,EAAAuN,QAAA,EAAA,IAAA;UACAvN,EAAAwN,QAAA,EAAA,IAAA;AACAxN,UAAAA,EAAAQ,OAAA,EAAA,IAAA;MAnCe,GAALR,uEAAAA,qEAAK,CAAA,EAAjB;UAyCayN,mFAAP,cAAmCC,EAAAA,+CAAnC;QA8BJC,YAAYC,IAAO,cAAY;AAC7B,gBAAMA,CAAN;AA7BF,eAAAC,kBAAoC;YAGlC,CAAC7N,mEAAMgB,eAAehB,mEAAMkB,MAA5B;YAEA,CAAClB,mEAAMgB,eAAehB,mEAAMgB,aAA5B;YAKA,CAAChB,mEAAMkB,QAAQlB,mEAAMkB,MAArB;YAGA,CAAClB,mEAAMkB,QAAQlB,mEAAMgB,aAArB;YAGA,CAAChB,mEAAMoB,aAAapB,mEAAMoB,WAA1B;YAEA;cAACpB,mEAAMgI;cAAgBhI,mEAAMgI;YAA7B;YAEA,CAAChI,mEAAM6B,UAAU7B,mEAAM6B,QAAvB;YAEA,CAAC7B,mEAAMmF,aAAanF,mEAAMmF,WAA1B;YAEA,CAACnF,mEAAMoF,YAAYpF,mEAAMoF,UAAzB;UAxBkC;eA8B7B0I,gBAAgB;AASrB,qBAAWC,KAAO/N,mEAGV6C,KAAQ7C,mEAAM+N,CAAN,GAMO,aAAjB,OAAOlL,MACV,KAA0C,WAAWkL,CAArD,IAA4DlL;AAS/D,eAAKmL,iBACH,qTAYEC,OAAOC,oBAAoBC,UAA3B,EAAuC1N,KAAK,GAA5C,CAbJ;QA9B6B;QAoD/B2N,KAAK1M,GAAoB;AACvB,gBAAM0M,KAAK1M,CAAX;AAEK,eAAKe,UAGR,KAAKA,QAAQ4L,MAAb,IAFA,KAAK5L,UAAU,IAAI6L,EAAAA,mCAAM,KAAKC,eAAf;AAKjB,eAAK9L,QAAQ+L,eAAe9M,EAAU+M,eAAV,CAA5B;AACA,eAAKhM,QAAQiM,kBAAkBhN,CAA/B;AACA,eAAKe,QAAQkM,mBAAmBjN,CAAhC;AAEA,gBAAMkN,IAAU,CAAA;AAEhB,cAAMC,IAAuBC,EAAAA,uDAAsBpN,CAAhC;AACnB,mBAASpB,IAAI,GAAGA,IAAIuO,EAAWzE,QAAQ9J,IACrCsO,GAAQG,KACN,KAAKtM,QAAQuM,QAAQH,EAAWvO,CAAX,GAAeqC,EAAAA,sCAASsM,kBAA7C,CADF;AAMI/E,cAAsBgF,EAAAA,kDAAiBxN,CAA3B;eACTpB,IAAI,GAAGA,IAAI4J,EAAUE,QAAQ9J,IACpCsO,GAAQG,KACN,KAAKtM,QAAQuM,QAAQ9E,EAAU5J,CAAV,EAAa6O,MAAb,GAAsBxM,EAAAA,sCAASC,QAApD,CADF;AAMEgM,YAAQxE,WACV,KAAKE,aAAL,YAAiC,SAASsE,EAAQnO,KAAK,IAAb,IAAqB;AAEjE,eAAKqN,gBAAgB;QAlCE;QA2CzBsB,OAAO3N,GAAY;AAEjB,gBAAM4N,IAAcpB,OAAOqB,OAAO,KAAKhF,YAAnB;AAEpB,gBAAM8E,OAAO3N,CAAb;AACA,eAAKqM,gBAAgB;AAErB,eAAKrL,QAAS4L,MAAd;AACA,iBAAOgB,EAAY5O,KAAK,MAAjB,IAA2B,WAAWgB;QAR5B;QAkBnB8N,gBAAgBC,GAAY;AAC1B,iBAAOA,IAAO;QADY;QAW5B3E,OAAO4E,GAAc;AAGnBA,cAASA,EACNC,QAAQ,OAAO,MADT,EAENA,QAAQ,OAAO,MAFT,EAGNA,QAAQ,MAAM,KAHR;AAIT,iBAAO,MAAMD,IAAS;QAPH;QAgBrBE,iBAAiBF,GAAc;AAI7B,iBADcA,EAAOG,MAAM,KAAb,EAAoBC,IAAI,KAAKhF,MAA7BiF,EACDrP,KAAK,cAAX;QAJsB;QAiB/B4J,OAAOvK,GAAc2B,GAAcsO,IAAW,OAAK;AACjD,cAAIC,IAAc;AAElB,cAAI,CAAClQ,EAAMmQ,oBAAoB,CAACnQ,EAAMmQ,iBAAiBC,kBAAkB;AAEvE,gBAAIC,IAAUrQ,EAAMsQ,eAAN;AACVD,kBACFA,IAAsBE,EAAAA,yCAAKF,GAAS,KAAKG,eAAe,CAA9C,GACVN,KAAe,KAAKxL,YAAY2L,IAAU,MAAM,KAAjC;AAIjB,qBAAS7P,IAAI,GAAGA,IAAIR,EAAMyQ,UAAUnG,QAAQ9J,IACtCR,GAAMyQ,UAAUjQ,CAAhB,EAAmBoD,SAAS8M,EAAAA,qDAAWC,UACnCC,IAAa5Q,EAAMyQ,UAAUjQ,CAAhB,EAAmBqQ,WAAYC,YAA/B,OAEjBT,IAAU,KAAKU,kBAAkBH,CAAvB,OAERV,KAAe,KAAKxL,YAAY2L,GAAS,KAA1B;UAfgD;AAqBnEW,cACJhR,EAAMiR,kBAAkBjR,EAAMiR,eAAeH,YAArB;AACpBI,cAAWjB,IAAW,KAAK,KAAKkB,YAAYH,CAAjB;iBAC1Bd,IAAcvO,IAAOuP;QA3BqB;QA0CnD7O,YACErC,GACAoR,GACAC,IAAQ,GACRC,IAAS,OACTvM,IAAQ7E,mEAAMQ,MAAI;AAEdV,YAAM4B,UAAUC,QAAQC,iBAC1BuP;AAEF,gBAAME,IAAiBvR,EAAM4B,UAAUC,QAAQC,gBAAgB,MAAM;AAErE,cAAI0P,IAAgBzM;AACR,cAARsM,IACFG,IAAgBtR,mEAAM6B,WACL,IAARsP,IACTG,IAAgBtR,mEAAM+C,cACbqO,MACTE,IAAgBtR,mEAAM6H;AAGpB3F,cAAK,KAAK3B,YAAYT,GAAOoR,GAAMI,CAA9B,KAAgDD;AAGzD,cAAc,MAAVF,KAAe,CAACC,EAClB,QAAOlP;AAGT,cAAgBqE,EAAAA,6CAASrE,CAArB,EAKF,QAJAA,IAAK+D,OAAOC,OAAOhE,CAAP,IAAaiP,CAApB,GACDC,MACFlP,IAAK+D,OAAO,CAACC,OAAOhE,CAAP,CAAR,IAEAA;AAGG,cAARiP,IACFjP,IAAK,GAAGA,CAAH,MAAWiP,CAAX,KACY,IAARA,MACTjP,IAAK,GAAGA,CAAH,MAAW,CAACiP,CAAZ;AAEHC,gBACFlP,IAAKiP,IAAQ,KAAKjP,CAAL,MAAa,IAAIA,CAAJ;AAExB8E,eAAKuK,MAAM1M,CAAX,KAAqBmC,KAAKuK,MAAMD,CAAX,MACvBpP,IAAK,IAAIA,CAAJ;AAEP,iBAAOA;QA1CW;MA1OhB,GAnENsP,8DAAA,CAAA;AAmEa/D,kEAAAA,sBAAAA;kEAzCDzN,QAAAA;APkQZ,UAAMuD,kEAAoBA,SACxBkO,GACAzP,GACA0P,GAAe;AAEf,eAAc,YAAV1P,IACK,MACY,eAAVA,IACFyP,IAAW,mBAAmBC,IAClB,WAAV1P,IACFyP,IAAW,gBAEXC;MATM,GA/RjBF,+CAAA,CAAA;AAkBgB3R,mDAAAA,qBAAAA;AAQAK,mDAAAA,oBAAAA;mDAuEA4B,iBAAAA;AA2MAmB,mDAAAA,mBAAAA;AA3NA5B,mDAAAA,gBAAAA;AATAF,mDAAAA,gBAAAA;mDATAF,eAAAA;AAvBAP,mDAAAA,eAAAA;AA+ZAqD,mDAAAA,gBAAAA;AAhRA1B,mDAAAA,iBAAAA;mDA+MAmB,aAAAA;AAuCAI,mDAAAA,cAAAA;AChXT,UAAM+N,gEAAkB3N,2DA7D/BwN,+CAAA,CAAA;AAgBgBxN,mDAAAA,cAAAA;AA6CH2N,mDAAAA,kBAAAA;AA+DGpM,mDAAAA,gBAAAA;mDA7DAZ,gBAAAA;AAkDAW,mDAAAA,eAAAA;AAoBAE,mDAAAA,aAAAA;AA/CAN,mDAAAA,kBAAAA;mDAuDAO,gBAAAA;AC/ET,UAAMmM,gEAAkB9L,mEA9D/B0L,+CAAA,CAAA;AAqNgBlK,mDAAAA,2BAAAA;AAjIAZ,mDAAAA,eAAAA;AA+FAU,mDAAAA,mBAAAA;mDArHHwK,kBAAAA;AA3CG9L,mDAAAA,sBAAAA;AA6CAU,mDAAAA,sBAAAA;ACsKT,UAAMqL,0DAAa3J,0DAEb4J,yDAAY5J,0DAxOzBsJ,8CAAA,CAAA;AA0BgB1J,kDAAAA,kBAAAA;AA4ZA0B,kDAAAA,aAAAA;kDAlOAT,cAAAA;AA1EAX,kDAAAA,gBAAAA;AAuPAgB,kDAAAA,iBAAAA;AAbAD,kDAAAA,cAAAA;kDApWAxB,cAAAA;AA2IAW,kDAAAA,uBAAAA;AA+EAW,kDAAAA,eAAAA;AAoMAM,kDAAAA,oBAAAA;kDAzBAD,kBAAAA;AA/KHuI,kDAAAA,aAAAA;AAhLG3J,kDAAAA,cAAAA;AAkLH4J,kDAAAA,YAAAA;AClJN,UAAMC,4EAAyBtI,yEAtFtC+H,oDAAA,CAAA;AAuGgBhH,wDAAAA,0BAAAA;AAfAD,wDAAAA,wBAAAA;wDAFHwH,yBAAAA;AArEGtI,wDAAAA,uBAAAA;AAoGAiB,wDAAAA,sBAAAA;ACjGhB,UAAMsH,yDAAY,yBASZjH,2DAAcA,SAAUlI,GAAa;AACzC,eAAImP,uDAAUC,KAAKpP,CAAf,IACK,CAACA,GAAO7C,mEAAMC,MAAd,IAEF,CAAC,YAAY4C,IAAQ,KAAK7C,mEAAMgB,aAAhC;MAJkC,GAcrCuC,iEAAoBA,SACxB2O,GACAlQ,GACA0P,GAAe;AAEf,eAAc,YAAV1P,IACK,MACY,eAAVA,IACFkQ,IAAa,mBAAmBR,IACpB,WAAV1P,IACFkQ,IAAa,gBAEbR;MATM,GA6TJS,2DAAcrG,8DA3W3B0F,8CAAA,CAAA;AA2DgB5G,kDAAAA,OAAAA;AA4CAO,kDAAAA,cAAAA;kDAuLAQ,kBAAAA;AA5IAH,kDAAAA,cAAAA;AA2NAO,kDAAAA,aAAAA;AA7KAN,kDAAAA,oBAAAA;kDA/DAH,eAAAA;AATAD,kDAAAA,eAAAA;AApDAP,kDAAAA,YAAAA;AA2CAM,kDAAAA,cAAAA;kDAiOAS,aAAAA;AA2BHsG,kDAAAA,cAAAA;AArBGrG,kDAAAA,kBAAAA;AA6CAG,kDAAAA,eAAAA;kDAuBAG,eAAAA;AA1FAR,kDAAAA,YAAAA;AChUhB,UAAA4F,mDAAA,CAAA;AAgBgBnF,uDAAAA,gBAAAA;AASAC,uDAAAA,gBAAAA;AEzBhB,UAAAkF,2DAAA,CAAA;AAcEnF,+DAAAA,wBAAAA;AACAC,+DAAAA,wBAAAA;ACeK,UAAM8F,8DAAsB,IAAI3E,oFAGjC4E,qDAAkD,OAAA;QAAA,CAAA;QACnDC;QACAC;QACAC;QACAC;QACAC;QACA9H;QACAV;QACAyI;MARmD;AAUxD,iBAAW/E,KAAQyE,mDACjBD,6DAAoB3H,SAASmD,CAA7B,IAAqCyE,mDAAWzE,CAAX;AA5CvC,UAAA4D,yCAAA,CAAA;AAwBA,6CAAA,sBAAA;AAAA,6CAAA,QAAA;6CAMaY,sBAAAA;;;;;;;;AC9Bb,mCAAuB;AAChB,IAAM;AAAA,EACX;AAAA,EACA;AAAA,EACA;AACF,IAAI,6BAAAQ;", "names": ["lists_create_empty", "block", "generator", "Order", "ATOMIC", "lists_create_with", "elements", "Array", "itemCount_", "i", "valueToCode", "NONE", "join", "lists_repeat", "functionName", "provideFunction_", "FUNCTION_NAME_PLACEHOLDER_", "element", "repeatCount", "FUNCTION_CALL", "lists_length", "MEMBER", "lists_isEmpty", "LOGICAL_NOT", "lists_indexOf", "operator", "getFieldValue", "item", "code", "workspace", "options", "oneBasedIndex", "ADDITION", "lists_getIndex", "mode", "where", "list", "at", "getAdjusted", "Error", "lists_setIndex", "cacheList", "match", "listVar", "nameDB_", "getDistinctName", "NameType", "VARIABLE", "value", "ASSIGNMENT", "SUBTRACTION", "xVar", "lists_getSublist", "wherePascalCase", "where1", "where2", "at1", "at2", "getSubstringIndex", "lists_sort", "direction", "type", "getCompareFunctionName", "lists_split", "input", "delimiter", "lists_reverse", "controls_if", "n", "STATEMENT_PREFIX", "injectId", "conditionCode", "branchCode", "statementToCode", "STATEMENT_SUFFIX", "prefixLines", "INDENT", "getInput", "logic_compare", "OPERATORS", "order", "EQUALITY", "RELATIONAL", "argument0", "argument1", "logic_operation", "LOGICAL_AND", "LOGICAL_OR", "defaultArgument", "logic_negate", "logic_boolean", "logic_null", "logic_ternary", "value_if", "CONDITIONAL", "value_then", "value_else", "controls_repeat_ext", "repeats", "getField", "String", "Number", "branch", "addLoopTrap", "loopVar", "endVar", "isNumber", "controls_whileUntil", "until", "controls_for", "variable0", "getVariableName", "increment", "up", "step", "Math", "abs", "startVar", "incVar", "controls_forEach", "indexVar", "controls_flow_statements", "xfix", "loop", "getSurroundLoop", "suppressPrefixSuffix", "math_number", "number", "UNARY_NEGATION", "math_arithmetic", "tuple", "MULTIPLICATION", "DIVISION", "math_single", "arg", "math_constant", "CONSTANTS", "math_number_property", "PROPERTIES", "MODULUS", "dropdownProperty", "suffix", "inputOrder", "outputOrder", "numberToCheck", "divisor", "math_change", "varName", "math_on_list", "func", "math_modulo", "math_constrain", "argument2", "math_random_int", "math_random_float", "math_atan2", "procedures_defreturn", "funcName", "getProcedureName", "xfix1", "loopTrap", "INFINITE_LOOP_TRAP", "returnValue", "xfix2", "args", "variables", "getVars", "length", "scrub_", "definitions_", "procedures_callreturn", "procedures_callnoreturn", "forBlock", "procedures_ifreturn", "hasReturnValue_", "text", "quote_", "text_join", "forceString", "codeAndOrder", "element0", "element1", "text_append", "text_length", "text_isEmpty", "text_indexOf", "substring", "text_charAt", "text_getSubstring", "requiresLengthCall", "text_changeCase", "text_trim", "text_print", "text_prompt_ext", "text_count", "sub", "text_replace", "from", "to", "text_reverse", "variables_get", "variables_set", "NEW", "INCREMENT", "DECREMENT", "BITWISE_NOT", "UNARY_PLUS", "TYPEOF", "VOID", "DELETE", "AWAIT", "EXPONENTIATION", "BITWISE_SHIFT", "IN", "INSTANCEOF", "BITWISE_AND", "BITWISE_XOR", "BITWISE_OR", "YIELD", "COMMA", "JavascriptGenerator", "CodeGenerator", "constructor", "name", "ORDER_OVERRIDES", "isInitialized", "key", "addReservedWords", "Object", "getOwnPropertyNames", "globalThis", "init", "reset", "Names", "RESERVED_WORDS_", "setVariableMap", "getVariableMap", "populateVariables", "populateProcedures", "defvars", "devVarList", "allDeveloperVariables", "push", "getName", "DEVELOPER_VARIABLE", "allUsedVarModels", "getId", "finish", "definitions", "values", "scrubNakedValue", "line", "string", "replace", "multiline_quote_", "split", "map", "lines", "thisOnly", "commentCode", "outputConnection", "targetConnection", "comment", "getCommentText", "wrap", "COMMENT_WRAP", "inputList", "inputTypes", "VALUE", "childBlock", "connection", "targetBlock", "allNestedComments", "nextBlock", "nextConnection", "nextCode", "blockToCode", "atId", "delta", "negate", "defaultAtIndex", "orderForInput", "floor", "$jscomp$tmp$exports$module$name", "listName", "opt_at", "controls_ifelse", "controls_repeat", "math_round", "math_trig", "procedures_defnoreturn", "strRegExp", "test", "stringName", "text_prompt", "javascriptGenerator", "generators", "lists", "logic", "loops", "math", "procedures", "variablesDynamic", "javascript"]}