<template>
  <div class="blockly-container">
    <div class="blockly-header">
      <h2>CatFlow 可视化编程</h2>
      <div class="blockly-controls">
        <button @click="generateCode" class="btn-primary">生成代码</button>
        <button @click="clearWorkspace" class="btn-secondary">清空</button>
        <button @click="saveWorkspace" class="btn-success">保存</button>
        <button @click="loadWorkspace" class="btn-info">加载</button>
      </div>
    </div>
    <div ref="blocklyDiv" class="blockly-workspace"></div>
    <div class="code-output" v-if="generatedCode">
      <h3>生成的代码:</h3>
      <pre><code>{{ generatedCode }}</code></pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as Blockly from 'blockly'
import 'blockly/javascript'

/**
 * Blockly编辑器组件
 * 提供可视化编程界面，支持拖拽式编程
 */

const blocklyDiv = ref<HTMLElement>()
const generatedCode = ref<string>('')
let workspace: Blockly.WorkspaceSvg | null = null

/**
 * 初始化Blockly工作区
 */
const initBlockly = () => {
  if (!blocklyDiv.value) return

  // 定义工具箱配置
  const toolbox = {
    kind: 'categoryToolbox',
    contents: [
      {
        kind: 'category',
        name: '逻辑',
        colour: '#5C81A6',
        contents: [
          {
            kind: 'block',
            type: 'controls_if'
          },
          {
            kind: 'block',
            type: 'logic_compare'
          },
          {
            kind: 'block',
            type: 'logic_operation'
          },
          {
            kind: 'block',
            type: 'logic_negate'
          },
          {
            kind: 'block',
            type: 'logic_boolean'
          }
        ]
      },
      {
        kind: 'category',
        name: '循环',
        colour: '#5CA65C',
        contents: [
          {
            kind: 'block',
            type: 'controls_repeat_ext'
          },
          {
            kind: 'block',
            type: 'controls_whileUntil'
          },
          {
            kind: 'block',
            type: 'controls_for'
          }
        ]
      },
      {
        kind: 'category',
        name: '数学',
        colour: '#5C68A6',
        contents: [
          {
            kind: 'block',
            type: 'math_number'
          },
          {
            kind: 'block',
            type: 'math_arithmetic'
          },
          {
            kind: 'block',
            type: 'math_single'
          }
        ]
      },
      {
        kind: 'category',
        name: '文本',
        colour: '#5CA68D',
        contents: [
          {
            kind: 'block',
            type: 'text'
          },
          {
            kind: 'block',
            type: 'text_join'
          },
          {
            kind: 'block',
            type: 'text_print'
          }
        ]
      },
      {
        kind: 'category',
        name: '变量',
        colour: '#A65C81',
        custom: 'VARIABLE'
      },
      {
        kind: 'category',
        name: '函数',
        colour: '#9A5CA6',
        custom: 'PROCEDURE'
      }
    ]
  }

  // 创建工作区
  workspace = Blockly.inject(blocklyDiv.value, {
    toolbox: toolbox,
    grid: {
      spacing: 20,
      length: 3,
      colour: '#ccc',
      snap: true
    },
    zoom: {
      controls: true,
      wheel: true,
      startScale: 1.0,
      maxScale: 3,
      minScale: 0.3,
      scaleSpeed: 1.2
    },
    trashcan: true,
    scrollbars: true,
    sounds: false,
    oneBasedIndex: false
  })

  // 添加一些示例块
  try {
    const xml = Blockly.utils.xml.textToDom(`
      <xml xmlns="https://developers.google.com/blockly/xml">
        <block type="text_print" x="20" y="20">
          <value name="TEXT">
            <shadow type="text">
              <field name="TEXT">Hello CatFlow!</field>
            </shadow>
          </value>
        </block>
      </xml>
    `)
    Blockly.Xml.domToWorkspace(xml, workspace)
  } catch (error) {
    console.error('添加示例块失败:', error)
  }
}

/**
 * 生成代码
 */
const generateCode = () => {
  if (!workspace) return
  
  try {
    // 生成JavaScript代码
    const code = Blockly.JavaScript.workspaceToCode(workspace)
    generatedCode.value = code || '// 没有生成代码'
  } catch (error) {
    console.error('生成代码时出错:', error)
    generatedCode.value = '// 生成代码时出错'
  }
}

/**
 * 清空工作区
 */
const clearWorkspace = () => {
  if (!workspace) return
  
  if (confirm('确定要清空工作区吗？')) {
    workspace.clear()
    generatedCode.value = ''
  }
}

/**
 * 保存工作区
 */
const saveWorkspace = () => {
  if (!workspace) return
  
  try {
    const xml = Blockly.Xml.workspaceToDom(workspace)
    const xmlText = Blockly.Xml.domToPrettyText(xml)
    
    // 创建下载链接
    const blob = new Blob([xmlText], { type: 'application/xml' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'catflow-workspace.xml'
    a.click()
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('保存工作区时出错:', error)
    alert('保存失败')
  }
}

/**
 * 加载工作区
 */
const loadWorkspace = () => {
  if (!workspace) return
  
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.xml'
  input.onchange = (event) => {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (!file) return
    
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const xmlText = e.target?.result as string
        const xml = Blockly.utils.xml.textToDom(xmlText)
        workspace!.clear()
        Blockly.Xml.domToWorkspace(xml, workspace!)
      } catch (error) {
        console.error('加载工作区时出错:', error)
        alert('加载失败')
      }
    }
    reader.readAsText(file)
  }
  input.click()
}

onMounted(() => {
  initBlockly()
})

onUnmounted(() => {
  if (workspace) {
    workspace.dispose()
    workspace = null
  }
})
</script>
