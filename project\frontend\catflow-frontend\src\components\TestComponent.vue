<template>
  <div class="test-component">
    <h2>API连接测试</h2>
    
    <div class="test-section">
      <h3>健康检查</h3>
      <button @click="testHealth" :disabled="loading">
        {{ loading ? '测试中...' : '测试健康检查' }}
      </button>
      <div v-if="healthResult" class="result">
        <strong>结果:</strong> {{ healthResult }}
      </div>
    </div>

    <div class="test-section">
      <h3>结构配置测试</h3>
      <button @click="testStructConfig" :disabled="loading">
        {{ loading ? '测试中...' : '测试结构配置' }}
      </button>
      <div v-if="structResult" class="result">
        <strong>结果:</strong>
        <pre>{{ structResult }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h3>Blockly测试</h3>
      <button @click="testBlockly" :disabled="loading">
        {{ loading ? '测试中...' : '测试Blockly' }}
      </button>
      <div v-if="blocklyResult" class="result">
        <strong>结果:</strong> {{ blocklyResult }}
      </div>
      <div ref="testBlocklyDiv" class="test-blockly" v-show="showBlockly"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import * as Blockly from 'blockly'
import { HealthService, StructConfigService } from '../services/api'
import { registerCustomBlocks } from '../utils/customBlocks'

/**
 * 测试组件
 * 用于验证各个功能模块是否正常工作
 */

const loading = ref(false)
const healthResult = ref('')
const structResult = ref('')
const blocklyResult = ref('')
const showBlockly = ref(false)
const testBlocklyDiv = ref<HTMLElement>()

/**
 * 测试健康检查API
 */
const testHealth = async () => {
  loading.value = true
  healthResult.value = ''
  
  try {
    const result = await HealthService.checkHealth()
    healthResult.value = JSON.stringify(result, null, 2)
  } catch (error) {
    healthResult.value = `错误: ${error}`
  } finally {
    loading.value = false
  }
}

/**
 * 测试结构配置API
 */
const testStructConfig = async () => {
  loading.value = true
  structResult.value = ''
  
  try {
    const result = await StructConfigService.getAllStructConfigs()
    structResult.value = JSON.stringify(result, null, 2)
  } catch (error) {
    structResult.value = `错误: ${error}`
  } finally {
    loading.value = false
  }
}

/**
 * 测试Blockly功能
 */
const testBlockly = async () => {
  loading.value = true
  blocklyResult.value = ''
  showBlockly.value = true
  
  try {
    if (!testBlocklyDiv.value) {
      throw new Error('Blockly容器未找到')
    }

    // 注册自定义块
    registerCustomBlocks()

    // 创建简单的工作区
    const workspace = Blockly.inject(testBlocklyDiv.value, {
      toolbox: {
        kind: 'categoryToolbox',
        contents: [
          {
            kind: 'category',
            name: '测试块',
            colour: '#5C81A6',
            contents: [
              {
                kind: 'block',
                type: 'structure_block'
              }
            ]
          }
        ]
      },
      grid: {
        spacing: 20,
        length: 3,
        colour: '#ccc',
        snap: true
      },
      zoom: {
        controls: true,
        wheel: true,
        startScale: 0.8,
        maxScale: 2,
        minScale: 0.3
      },
      trashcan: true
    })

    // 添加一个测试块
    const xml = Blockly.utils.xml.textToDom(`
      <xml xmlns="https://developers.google.com/blockly/xml">
        <block type="structure_block" x="20" y="20">
          <field name="STRUCT_NAME">测试结构</field>
          <field name="STRUCT_TYPE">coordinator</field>
          <field name="DESCRIPTION">这是一个测试结构</field>
        </block>
      </xml>
    `)
    
    Blockly.Xml.domToWorkspace(xml, workspace)
    
    blocklyResult.value = 'Blockly工作区创建成功！'
  } catch (error) {
    blocklyResult.value = `错误: ${error}`
    showBlockly.value = false
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.test-component {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 2rem;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.test-section h3 {
  margin-top: 0;
  color: #333;
}

button {
  padding: 0.5rem 1rem;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

button:hover:not(:disabled) {
  background-color: #0056b3;
}

button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.result {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 4px solid #007bff;
}

.result pre {
  margin: 0;
  white-space: pre-wrap;
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
}

.test-blockly {
  width: 100%;
  height: 300px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-top: 1rem;
}
</style>
