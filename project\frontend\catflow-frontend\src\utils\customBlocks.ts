import * as Blockly from 'blockly'

/**
 * 自定义Blockly块定义
 * 用于表示智能体结构的包含关系
 */

/**
 * 结构块 - 表示一个完整的智能体结构
 */
export const structureBlock = {
  type: 'structure_block',
  message0: '结构: %1 %2 类型: %3 %4 描述: %5 %6 %7',
  args0: [
    {
      type: 'field_input',
      name: 'STRUCT_NAME',
      text: '新结构'
    },
    {
      type: 'input_dummy'
    },
    {
      type: 'field_dropdown',
      name: 'STRUCT_TYPE',
      options: [
        ['协调器', 'coordinator'],
        ['顺序执行', 'sequential'],
        ['并发执行', 'concurrent'],
        ['循环执行', 'loop']
      ]
    },
    {
      type: 'input_dummy'
    },
    {
      type: 'field_input',
      name: 'DESCRIPTION',
      text: '结构描述'
    },
    {
      type: 'input_dummy'
    },
    {
      type: 'input_statement',
      name: 'COMPONENTS',
      check: ['main_agent', 'sub_structure', 'agent_ref']
    }
  ],
  colour: 230,
  tooltip: '定义一个智能体结构',
  helpUrl: ''
}

/**
 * 主智能体块
 */
export const mainAgentBlock = {
  type: 'main_agent_block',
  message0: '主智能体: %1 %2 类型: %3 %4 模型: %5 %6 指令: %7 %8 子结构引用: %9',
  args0: [
    {
      type: 'field_input',
      name: 'AGENT_NAME',
      text: 'main_agent'
    },
    {
      type: 'input_dummy'
    },
    {
      type: 'field_dropdown',
      name: 'AGENT_TYPE',
      options: [
        ['顺序智能体', 'sequential_agent'],
        ['并发智能体', 'concurrent_agent'],
        ['循环智能体', 'loop_agent']
      ]
    },
    {
      type: 'input_dummy'
    },
    {
      type: 'field_input',
      name: 'MODEL',
      text: 'qwen-max'
    },
    {
      type: 'input_dummy'
    },
    {
      type: 'field_input',
      name: 'INSTRUCTION',
      text: '智能体指令'
    },
    {
      type: 'input_dummy'
    },
    {
      type: 'input_statement',
      name: 'SUB_STRUCTURE_REFS',
      check: 'sub_structure_ref'
    }
  ],
  previousStatement: 'main_agent',
  colour: 160,
  tooltip: '定义主智能体，引用子结构',
  helpUrl: ''
}

/**
 * 子结构块
 */
export const subStructureBlock = {
  type: 'sub_structure_block',
  message0: '子结构: %1 %2 类型: %3 %4 描述: %5 %6 %7',
  args0: [
    {
      type: 'field_input',
      name: 'SUB_NAME',
      text: '子结构名称'
    },
    {
      type: 'input_dummy'
    },
    {
      type: 'field_dropdown',
      name: 'SUB_TYPE',
      options: [
        ['顺序执行', 'sequential'],
        ['并发执行', 'concurrent'],
        ['循环执行', 'loop'],
        ['顺序智能体', 'sequential_agent']
      ]
    },
    {
      type: 'input_dummy'
    },
    {
      type: 'field_input',
      name: 'SUB_DESCRIPTION',
      text: '子结构描述'
    },
    {
      type: 'input_dummy'
    },
    {
      type: 'input_statement',
      name: 'SUB_AGENT_REFS',
      check: 'agent_ref'
    }
  ],
  previousStatement: 'sub_structure',
  nextStatement: 'sub_structure',
  colour: 120,
  tooltip: '定义子结构',
  helpUrl: ''
}

/**
 * 智能体引用块
 */
export const agentRefBlock = {
  type: 'agent_ref_block',
  message0: '智能体: %1',
  args0: [
    {
      type: 'field_input',
      name: 'AGENT_NAME',
      text: 'agentName'
    }
  ],
  previousStatement: 'agent_ref',
  nextStatement: 'agent_ref',
  colour: 65,
  tooltip: '引用一个智能体',
  helpUrl: ''
}

/**
 * 子结构引用块
 */
export const subStructureRefBlock = {
  type: 'sub_structure_ref_block',
  message0: '子结构: %1',
  args0: [
    {
      type: 'field_input',
      name: 'SUB_STRUCTURE_NAME',
      text: 'subStructureName'
    }
  ],
  previousStatement: 'sub_structure_ref',
  nextStatement: 'sub_structure_ref',
  colour: 95,
  tooltip: '引用一个子结构',
  helpUrl: ''
}

/**
 * 配置块
 */
export const configBlock = {
  type: 'config_block',
  message0: '配置 %1 温度: %2 %3 最大输出: %4',
  args0: [
    {
      type: 'input_dummy'
    },
    {
      type: 'field_number',
      name: 'TEMPERATURE',
      value: 0.7,
      min: 0,
      max: 1,
      precision: 0.1
    },
    {
      type: 'input_dummy'
    },
    {
      type: 'field_number',
      name: 'MAX_TOKENS',
      value: 204800,
      min: 1,
      max: 1000000
    }
  ],
  previousStatement: 'config',
  colour: 290,
  tooltip: '生成内容配置',
  helpUrl: ''
}

/**
 * 注册所有自定义块
 */
export function registerCustomBlocks() {
  // 注册结构块
  Blockly.Blocks['structure_block'] = {
    init: function() {
      this.jsonInit(structureBlock)
    }
  }

  // 注册主智能体块
  Blockly.Blocks['main_agent_block'] = {
    init: function() {
      this.jsonInit(mainAgentBlock)
    }
  }

  // 注册子结构块
  Blockly.Blocks['sub_structure_block'] = {
    init: function() {
      this.jsonInit(subStructureBlock)
    }
  }

  // 注册智能体引用块
  Blockly.Blocks['agent_ref_block'] = {
    init: function() {
      this.jsonInit(agentRefBlock)
    }
  }

  // 注册子结构引用块
  Blockly.Blocks['sub_structure_ref_block'] = {
    init: function() {
      this.jsonInit(subStructureRefBlock)
    }
  }

  // 注册配置块
  Blockly.Blocks['config_block'] = {
    init: function() {
      this.jsonInit(configBlock)
    }
  }
}

/**
 * 生成结构配置的JavaScript代码
 * @param workspace Blockly工作区
 * @param originalMeta 原始的meta信息，如果存在则保持不变
 */
export function generateStructureCode(workspace: Blockly.WorkspaceSvg, originalMeta?: any): any {
  const topBlocks = workspace.getTopBlocks()
  const structures: any = {}

  topBlocks.forEach(block => {
    if (block.type === 'structure_block') {
      const structName = block.getFieldValue('STRUCT_NAME')
      const structType = block.getFieldValue('STRUCT_TYPE')
      const description = block.getFieldValue('DESCRIPTION')

      const structure: any = {
        name: structName,
        type: structType,
        description: description,
        agent_refs: [],
        sub_structures: {}
      }

      // 处理组件
      const componentsBlock = block.getInputTargetBlock('COMPONENTS')
      if (componentsBlock) {
        processComponents(componentsBlock, structure)
      }

      structures[structName] = structure
    }
  })

  // 使用原始meta信息，如果不存在则使用默认值
  const meta = originalMeta || {
    description: "智能体结构配置文件",
    purpose: "定义多智能体系统的组织结构和协作模式",
    structure: {
      structures: {
        type: "object",
        description: "结构配置对象，包含所有智能体结构的定义"
      }
    },
    usage: {
      description: "使用方法",
      steps: [
        "1. 在structures对象中定义新的智能体结构",
        "2. 设置结构的基本属性：name、type、description",
        "3. 配置main_agent定义主智能体",
        "4. 在agent_refs中列出所有需要的智能体",
        "5. 根据需要在sub_structures中定义子结构",
        "6. 设置全局指令和生成配置"
      ]
    }
  }

  return {
    meta: meta,
    structures: structures
  }
}

/**
 * 处理组件块
 */
function processComponents(block: Blockly.Block, structure: any) {
  let currentBlock: Blockly.Block | null = block

  while (currentBlock) {
    if (currentBlock.type === 'main_agent_block') {
      structure.main_agent = {
        name: currentBlock.getFieldValue('AGENT_NAME'),
        type: currentBlock.getFieldValue('AGENT_TYPE'),
        model: currentBlock.getFieldValue('MODEL'),
        instruction: currentBlock.getFieldValue('INSTRUCTION'),
        sub_structure_refs: []  // 主agent引用子结构
      }

      // 处理主智能体的sub_structure_refs
      const subStructureRefsBlock = currentBlock.getInputTargetBlock('SUB_STRUCTURE_REFS')
      if (subStructureRefsBlock) {
        structure.main_agent.sub_structure_refs = processSubStructureRefs(subStructureRefsBlock)
      }
    } else if (currentBlock.type === 'sub_structure_block') {
      const subName = currentBlock.getFieldValue('SUB_NAME')
      structure.sub_structures[subName] = {
        name: subName,
        type: currentBlock.getFieldValue('SUB_TYPE'),
        description: currentBlock.getFieldValue('SUB_DESCRIPTION'),
        agent_refs: []  // 子结构引用智能体
      }

      // 处理子结构的agent_refs
      const subAgentRefsBlock = currentBlock.getInputTargetBlock('SUB_AGENT_REFS')
      if (subAgentRefsBlock) {
        structure.sub_structures[subName].agent_refs = processAgentRefs(subAgentRefsBlock)
      }
    } else if (currentBlock.type === 'agent_ref_block') {
      const agentName = currentBlock.getFieldValue('AGENT_NAME')
      if (!structure.agent_refs.includes(agentName)) {
        structure.agent_refs.push(agentName)
      }
    }

    currentBlock = currentBlock.getNextBlock()
  }
}

/**
 * 处理智能体引用
 */
function processAgentRefs(block: Blockly.Block): string[] {
  const agentRefs: string[] = []
  let currentBlock: Blockly.Block | null = block

  while (currentBlock) {
    if (currentBlock.type === 'agent_ref_block') {
      const agentName = currentBlock.getFieldValue('AGENT_NAME')
      agentRefs.push(agentName)
    }
    currentBlock = currentBlock.getNextBlock()
  }

  return agentRefs
}

/**
 * 处理子结构引用
 */
function processSubStructureRefs(block: Blockly.Block): string[] {
  const subStructureRefs: string[] = []
  let currentBlock: Blockly.Block | null = block

  while (currentBlock) {
    if (currentBlock.type === 'sub_structure_ref_block') {
      const subStructureName = currentBlock.getFieldValue('SUB_STRUCTURE_NAME')
      subStructureRefs.push(subStructureName)
    }
    currentBlock = currentBlock.getNextBlock()
  }

  return subStructureRefs
}

/**
 * 将结构配置转换为Blockly XML
 */
export function structConfigToBlocklyXML(structConfig: any): string {
  const structName = structConfig.name || '未命名结构'
  const structType = structConfig.type || 'coordinator'
  const description = structConfig.description || ''

  let xmlContent = `
    <block type="structure_block" x="20" y="20">
      <field name="STRUCT_NAME">${escapeXML(structName)}</field>
      <field name="STRUCT_TYPE">${structType}</field>
      <field name="DESCRIPTION">${escapeXML(description)}</field>
      <statement name="COMPONENTS">`

  // 添加主智能体
  if (structConfig.main_agent) {
    const mainAgent = structConfig.main_agent
    xmlContent += `
        <block type="main_agent_block">
          <field name="AGENT_NAME">${escapeXML(mainAgent.name || 'main_agent')}</field>
          <field name="AGENT_TYPE">${mainAgent.type || 'loop_agent'}</field>
          <field name="MODEL">${escapeXML(mainAgent.model || 'qwen-max')}</field>
          <field name="INSTRUCTION">${escapeXML(mainAgent.instruction || '')}</field>`

    // 添加主智能体的sub_structure_refs
    if (mainAgent.sub_structure_refs && mainAgent.sub_structure_refs.length > 0) {
      xmlContent += `
          <statement name="SUB_STRUCTURE_REFS">`

      mainAgent.sub_structure_refs.forEach((subStructureName: string, index: number) => {
        xmlContent += `
            <block type="sub_structure_ref_block">
              <field name="SUB_STRUCTURE_NAME">${escapeXML(subStructureName)}</field>`

        if (index < mainAgent.sub_structure_refs.length - 1) {
          xmlContent += `
              <next>`
        }
      })

      // 关闭next标签
      for (let i = mainAgent.sub_structure_refs.length - 1; i > 0; i--) {
        xmlContent += `
              </next>
            </block>`
      }

      xmlContent += `
            </block>
          </statement>`
    }

    xmlContent += `</block>`
  }

  // 添加子结构
  if (structConfig.sub_structures) {
    const subStructures = Object.values(structConfig.sub_structures) as any[]

    subStructures.forEach((subStruct, index) => {
      if (structConfig.main_agent || index > 0) {
        xmlContent += `
          <next>`
      }

      xmlContent += `
            <block type="sub_structure_block">
              <field name="SUB_NAME">${escapeXML(subStruct.name || '')}</field>
              <field name="SUB_TYPE">${subStruct.type || 'sequential'}</field>
              <field name="SUB_DESCRIPTION">${escapeXML(subStruct.description || '')}</field>`

      // 添加子结构的agent_refs
      if (subStruct.agent_refs && subStruct.agent_refs.length > 0) {
        xmlContent += `
              <statement name="SUB_AGENT_REFS">`

        subStruct.agent_refs.forEach((agentName: string, agentIndex: number) => {
          xmlContent += `
                <block type="agent_ref_block">
                  <field name="AGENT_NAME">${escapeXML(agentName)}</field>`

          if (agentIndex < subStruct.agent_refs.length - 1) {
            xmlContent += `
                  <next>`
          }
        })

        // 关闭next标签
        for (let i = subStruct.agent_refs.length - 1; i > 0; i--) {
          xmlContent += `
                  </next>
                </block>`
        }

        xmlContent += `
                </block>
              </statement>`
      }

      xmlContent += `
            </block>`
    })

    // 关闭子结构的next标签
    for (let i = subStructures.length - 1; i >= 0; i--) {
      if (structConfig.main_agent || i > 0) {
        xmlContent += `
          </next>`
      }
    }
  }

  xmlContent += `
      </statement>
    </block>`

  return `<xml xmlns="https://developers.google.com/blockly/xml">${xmlContent}</xml>`
}

/**
 * 转义XML特殊字符
 */
function escapeXML(str: string): string {
  return str
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')
}
